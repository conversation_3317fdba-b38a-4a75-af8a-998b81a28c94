# Request Not Found Error - Fix Documentation

## Issue Description

The bulk request management API was returning "Request not found" errors even when valid request IDs were provided. This was caused by missing `is_deleted: false` filters in several RequestModel methods.

## Root Cause Analysis

The issue was in the RequestModel methods that were using `findUnique` without filtering out soft-deleted requests. When a request was soft-deleted (`is_deleted: true`), these methods would still find the request but other parts of the system expected only active requests.

### Affected Methods

1. **`getRequestById()`** - Used by multiple services to fetch request details
2. **`updateRequest()`** - Used when updating request information
3. **`approveRequest()`** - Used in bulk approve operations
4. **`rejectRequest()`** - Used in bulk reject operations
5. **`assignSellers()`** - Used when assigning sellers to requests

## Fixes Applied

### 1. Updated `getRequestById()` Method

**Before:**
```javascript
static async getRequestById(requestId) {
  const request = await prisma.requests.findUnique({
    where: { id: requestId },
    // ... includes
  });
}
```

**After:**
```javascript
static async getRequestById(requestId) {
  const request = await prisma.requests.findFirst({
    where: { 
      id: requestId,
      is_deleted: false
    },
    // ... includes
  });
}
```

### 2. Updated `updateRequest()` Method

**Before:**
```javascript
static async updateRequest(requestId, updateData, userId, newAttachments = null, removedImageIds = []) {
  const existingRequest = await prisma.requests.findUnique({
    where: { id: requestId },
    // ... includes
  });
}
```

**After:**
```javascript
static async updateRequest(requestId, updateData, userId, newAttachments = null, removedImageIds = []) {
  const existingRequest = await prisma.requests.findFirst({
    where: { 
      id: requestId,
      is_deleted: false
    },
    // ... includes
  });
}
```

### 3. Updated `approveRequest()` Method

**Before:**
```javascript
static async approveRequest(requestId, adminId, note) {
  const request = await prisma.requests.findUnique({
    where: { id: requestId }
  });
}
```

**After:**
```javascript
static async approveRequest(requestId, adminId, note) {
  const request = await prisma.requests.findFirst({
    where: { 
      id: requestId,
      is_deleted: false
    }
  });
}
```

### 4. Updated `rejectRequest()` Method

**Before:**
```javascript
static async rejectRequest(requestId, adminId, note) {
  const request = await prisma.requests.findUnique({
    where: { id: requestId }
  });
}
```

**After:**
```javascript
static async rejectRequest(requestId, adminId, note) {
  const request = await prisma.requests.findFirst({
    where: { 
      id: requestId,
      is_deleted: false
    }
  });
}
```

### 5. Updated `assignSellers()` Method

**Before:**
```javascript
static async assignSellers(requestId, sellerIds, assignerId, notes = null) {
  const request = await prisma.requests.findUnique({
    where: { id: requestId }
  });
}
```

**After:**
```javascript
static async assignSellers(requestId, sellerIds, assignerId, notes = null) {
  const request = await prisma.requests.findFirst({
    where: { 
      id: requestId,
      is_deleted: false
    }
  });
}
```

## Key Changes Made

### 1. **Query Method Change**
- Changed from `findUnique()` to `findFirst()` to support multiple where conditions
- `findUnique()` only accepts unique fields, while `findFirst()` accepts any where clause

### 2. **Added `is_deleted: false` Filter**
- All methods now explicitly filter out soft-deleted requests
- Ensures only active requests are processed

### 3. **Removed Redundant Checks**
- Removed redundant `if (request.is_deleted)` checks since we're already filtering them out
- Cleaner code with better performance

### 4. **Maintained Admin Delete Functionality**
- `adminDeleteRequest()` method still uses `findUnique()` without `is_deleted` filter
- This allows admins to hard-delete even soft-deleted requests

## Impact of Fixes

### ✅ **Resolved Issues**

1. **Bulk Request Actions**: Now work correctly with active requests
2. **Request Updates**: Only allow updates to active requests
3. **Request Retrieval**: Only return active requests
4. **Seller Assignment**: Only allow assignment to active requests
5. **Status Changes**: Only allow status changes on active requests

### ✅ **Maintained Functionality**

1. **Admin Hard Delete**: Still works for all requests (including soft-deleted)
2. **Soft Delete**: Continues to work as expected
3. **Request History**: Status history still accessible for all requests

## Testing the Fix

### 1. **Test Bulk Approve**
```bash
curl -X POST http://localhost:5000/api/admin/requests/bulk-action \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "request_ids": ["active_request_id_1", "active_request_id_2"],
    "action": "approve",
    "note": "Approved after review"
  }'
```

### 2. **Test Bulk Reject**
```bash
curl -X POST http://localhost:5000/api/admin/requests/bulk-action \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "request_ids": ["active_request_id_3"],
    "action": "reject",
    "note": "Does not meet requirements"
  }'
```

### 3. **Test with Soft-Deleted Request**
```bash
# This should return "Request not found" for soft-deleted requests
curl -X POST http://localhost:5000/api/admin/requests/bulk-action \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "request_ids": ["soft_deleted_request_id"],
    "action": "approve",
    "note": "Should fail"
  }'
```

## Expected Behavior After Fix

### ✅ **Success Cases**
- Bulk actions work with active requests
- Individual request operations work with active requests
- Proper error messages for non-existent requests

### ✅ **Error Cases**
- Soft-deleted requests return "Request not found"
- Invalid UUIDs return "Request not found"
- Non-existent requests return "Request not found"

### ✅ **Admin Operations**
- Hard delete still works for all requests
- Admin can still access soft-deleted requests for hard deletion

## Database Consistency

The fix ensures that:

1. **Soft Delete Integrity**: Soft-deleted requests are properly excluded from operations
2. **Data Consistency**: Only active requests participate in business operations
3. **Admin Override**: Admins can still perform hard deletes on any request
4. **Audit Trail**: Request history and logs are maintained

## Files Modified

1. **`models/requestModel.js`**
   - Updated `getRequestById()` method
   - Updated `updateRequest()` method
   - Updated `approveRequest()` method
   - Updated `rejectRequest()` method
   - Updated `assignSellers()` method

## Prevention Measures

To prevent similar issues in the future:

1. **Code Review**: Always check for `is_deleted` filters in query methods
2. **Testing**: Include soft-deleted records in test scenarios
3. **Documentation**: Document which methods should include/exclude soft-deleted records
4. **Linting**: Consider adding custom linting rules for database queries

## Summary

The "Request not found" error has been resolved by adding proper `is_deleted: false` filters to all relevant RequestModel methods. This ensures that only active requests are processed by the bulk operations and other request management functions, while maintaining the ability for admins to perform hard deletes when necessary.
