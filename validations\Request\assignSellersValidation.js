const { body, validationResult } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

const assignSellersValidation = [
  // Required fields
  body('request_id')
    .notEmpty().withMessage('The request ID field is required.')
    .isUUID().withMessage('The request ID must be a valid UUID.'),
  
  body('seller_ids')
    .notEmpty().withMessage('The seller IDs field is required.')
    .isArray().withMessage('The seller IDs must be an array.')
    .custom(async (value) => {
      if (!value.length) {
        throw new Error('At least one seller ID is required.');
      }
      
      // Validate that all seller IDs are valid UUIDs
      for (const sellerId of value) {
        if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(sellerId)) {
          throw new Error(`Seller ID ${sellerId} is not a valid UUID.`);
        }
      }
      
      // Validate that all seller IDs exist in the database
      const sellers = await prisma.users.findMany({
        where: {
          id: { in: value },
          roles: {
            some: {
              role: {
                name: 'Seller'
              }
            }
          }
        }
      });
      
      if (sellers.length !== value.length) {
        const foundSellerIds = sellers.map(seller => seller.id);
        const missingSellerIds = value.filter(id => !foundSellerIds.includes(id));
        throw new Error(`The following seller IDs do not exist or are not sellers: ${missingSellerIds.join(', ')}`);
      }
      
      return true;
    }),
  
  // Optional fields
  body('notes')
    .optional()
    .isString().withMessage('The notes must be a string.')
    .isLength({ max: 500 }).withMessage('The notes may not be greater than 500 characters.'),
  
  // Handle validation errors
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const formattedErrors = {};
      
      errors.array().forEach(error => {
        if (!formattedErrors[error.path]) {
          formattedErrors[error.path] = [];
        }
        formattedErrors[error.path].push(error.msg);
      });
      
      return res.status(422).json({
        success: false,
        message: 'The given data was invalid.',
        error: formattedErrors
      });
    }
    next();
  }
];

module.exports = assignSellersValidation;
