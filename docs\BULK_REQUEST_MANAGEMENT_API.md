# Bulk Request Management API

## Overview

The Bulk Request Management API allows administrators to perform batch operations on multiple requests simultaneously. This includes approving, rejecting, or deleting multiple requests in a single API call.

## Endpoint

**POST** `/api/admin/requests/bulk-action`

### Authentication
- **Required**: Admin JWT token
- **Authorization**: Only users with "Admin" role can access this endpoint

## Request Payload

```json
{
  "request_ids": ["uuid1", "uuid2", "uuid3"],
  "action": "approve|reject|delete",
  "note": "Optional note for approve/reject actions"
}
```

### Payload Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `request_ids` | Array | Yes | Array of request UUIDs (1-100 requests) |
| `action` | String | Yes | Action to perform: "approve", "reject", or "delete" |
| `note` | String | Conditional | Required for approve/reject, optional for delete (max 1000 chars) |

### Validation Rules

1. **request_ids**:
   - Must be a non-empty array
   - Maximum 100 request IDs per batch
   - All IDs must be valid UUIDs
   - No duplicate IDs allowed

2. **action**:
   - Must be one of: "approve", "reject", "delete"

3. **note**:
   - Required for "approve" and "reject" actions
   - Optional for "delete" action
   - Maximum 1000 characters

## API Examples

### 1. Bulk Approve Requests

```bash
POST /api/admin/requests/bulk-action
Authorization: Bearer <admin_jwt_token>
Content-Type: application/json

{
  "request_ids": [
    "550e8400-e29b-41d4-a716-************",
    "550e8400-e29b-41d4-a716-************",
    "550e8400-e29b-41d4-a716-************"
  ],
  "action": "approve",
  "note": "All requests meet quality standards and are approved for processing."
}
```

#### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Successfully approved 3 request(s)",
  "data": {
    "total": 3,
    "successful": [
      {
        "request_id": "550e8400-e29b-41d4-a716-************",
        "action": "approve",
        "result": {
          "id": "550e8400-e29b-41d4-a716-************",
          "title": "Website Development Project",
          "status": "Approved",
          "admin_notes": "All requests meet quality standards and are approved for processing.",
          "approved_by": "admin_id",
          "approved_at": "2024-01-20T10:30:00.000Z",
          "buyer": {
            "id": "buyer_id",
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>"
          }
        },
        "note": "All requests meet quality standards and are approved for processing."
      },
      {
        "request_id": "550e8400-e29b-41d4-a716-************",
        "action": "approve",
        "result": {
          "id": "550e8400-e29b-41d4-a716-************",
          "title": "Mobile App Development",
          "status": "Approved",
          "admin_notes": "All requests meet quality standards and are approved for processing.",
          "approved_by": "admin_id",
          "approved_at": "2024-01-20T10:30:00.000Z"
        },
        "note": "All requests meet quality standards and are approved for processing."
      }
    ],
    "failed": [],
    "summary": {
      "total_processed": 3,
      "successful_count": 3,
      "failed_count": 0,
      "action": "approve",
      "admin_id": "admin_id",
      "note": "All requests meet quality standards and are approved for processing.",
      "processed_at": "2024-01-20T10:30:00.000Z"
    }
  }
}
```

### 2. Bulk Reject Requests

```bash
POST /api/admin/requests/bulk-action
Authorization: Bearer <admin_jwt_token>
Content-Type: application/json

{
  "request_ids": [
    "550e8400-e29b-41d4-a716-************",
    "550e8400-e29b-41d4-a716-************"
  ],
  "action": "reject",
  "note": "Requests do not meet platform guidelines. Please review and resubmit with proper documentation."
}
```

#### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Successfully rejected 2 request(s)",
  "data": {
    "total": 2,
    "successful": [
      {
        "request_id": "550e8400-e29b-41d4-a716-************",
        "action": "reject",
        "result": {
          "id": "550e8400-e29b-41d4-a716-************",
          "title": "Incomplete Project Request",
          "status": "Rejected",
          "admin_notes": "Requests do not meet platform guidelines. Please review and resubmit with proper documentation.",
          "rejected_by": "admin_id",
          "rejected_at": "2024-01-20T10:35:00.000Z"
        },
        "note": "Requests do not meet platform guidelines. Please review and resubmit with proper documentation."
      }
    ],
    "failed": [
      {
        "request_id": "550e8400-e29b-41d4-a716-************",
        "action": "reject",
        "error": "Request not found",
        "note": "Requests do not meet platform guidelines. Please review and resubmit with proper documentation."
      }
    ],
    "summary": {
      "total_processed": 2,
      "successful_count": 1,
      "failed_count": 1,
      "action": "reject",
      "admin_id": "admin_id",
      "note": "Requests do not meet platform guidelines. Please review and resubmit with proper documentation.",
      "processed_at": "2024-01-20T10:35:00.000Z"
    }
  }
}
```

### 3. Bulk Delete Requests

```bash
POST /api/admin/requests/bulk-action
Authorization: Bearer <admin_jwt_token>
Content-Type: application/json

{
  "request_ids": [
    "550e8400-e29b-41d4-a716-************",
    "550e8400-e29b-41d4-a716-************"
  ],
  "action": "delete",
  "note": "Spam requests removed from platform"
}
```

#### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Successfully deleted 2 request(s)",
  "data": {
    "total": 2,
    "successful": [
      {
        "request_id": "550e8400-e29b-41d4-a716-************",
        "action": "delete",
        "result": {
          "request_id": "550e8400-e29b-41d4-a716-************",
          "deleted_by": "admin_id",
          "deleted_at": "2024-01-20T10:40:00.000Z",
          "attachments_deleted": 3,
          "offers_deleted": 2,
          "offer_attachments_deleted": 5,
          "assigned_sellers_deleted": 1,
          "status_updates_deleted": 4
        },
        "note": "Spam requests removed from platform"
      }
    ],
    "failed": [],
    "summary": {
      "total_processed": 2,
      "successful_count": 2,
      "failed_count": 0,
      "action": "delete",
      "admin_id": "admin_id",
      "note": "Spam requests removed from platform",
      "processed_at": "2024-01-20T10:40:00.000Z"
    }
  }
}
```

## Error Responses

### Validation Error (422)
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "request_ids": [
      "Request IDs are required",
      "Cannot process more than 100 requests at once"
    ],
    "action": [
      "Action must be one of: approve, reject, delete"
    ],
    "note": [
      "Note is required for approve and reject actions"
    ]
  }
}
```

### Unauthorized Error (403)
```json
{
  "success": false,
  "message": "Access denied. Admin role required."
}
```

### Server Error (500)
```json
{
  "success": false,
  "message": "Internal server error occurred during bulk operation",
  "errors": {
    "general": ["Internal server error occurred during bulk operation"]
  }
}
```

## Action Details

### Approve Action
- Changes request status to "Approved"
- Sets `approved_by` field to admin ID
- Sets `approved_at` timestamp
- Stores admin note in `admin_notes` field
- Creates status history entry
- Sends notification to buyer (if configured)

### Reject Action
- Changes request status to "Rejected"
- Sets `rejected_by` field to admin ID
- Sets `rejected_at` timestamp
- Stores admin note in `admin_notes` field
- Creates status history entry
- Sends notification to buyer (if configured)

### Delete Action
- **Hard delete** - completely removes request and all related data
- Deletes request attachments and files from server
- Deletes all offers and offer attachments
- Removes assigned seller relationships
- Deletes status history
- Creates activity log entry
- **Cannot be undone**

## Partial Success Handling

The API processes each request individually, so if some requests fail, others can still succeed. The response includes:

- `successful`: Array of successfully processed requests
- `failed`: Array of requests that failed with error details
- `summary`: Overall statistics of the operation

## Rate Limiting

- Maximum 100 requests per batch operation
- Recommended to process in smaller batches for better performance
- Large batches may take longer to complete

## Best Practices

1. **Batch Size**: Keep batches under 50 requests for optimal performance
2. **Error Handling**: Always check both successful and failed arrays in response
3. **Logging**: The system automatically logs all bulk operations
4. **Notifications**: Consider notifying affected users about status changes
5. **Backup**: For delete operations, ensure you have proper backups

## Frontend Integration Example

```javascript
async function bulkApproveRequests(requestIds, note) {
  try {
    const response = await fetch('/api/admin/requests/bulk-action', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${adminToken}`
      },
      body: JSON.stringify({
        request_ids: requestIds,
        action: 'approve',
        note: note
      })
    });

    const result = await response.json();
    
    if (result.success) {
      console.log(`Successfully approved ${result.data.successful.length} requests`);
      if (result.data.failed.length > 0) {
        console.warn(`${result.data.failed.length} requests failed to approve`);
      }
    }
    
    return result;
  } catch (error) {
    console.error('Bulk approve error:', error);
    throw error;
  }
}
```
