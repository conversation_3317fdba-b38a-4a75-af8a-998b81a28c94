const PublicOfferModel = require('../models/publicOfferModel');
const CodeGeneratorService = require('./codeGeneratorService');
const ApiError = require('../utils/apiError');

class PublicOfferService {
  /**
   * Get public offers with filtering and pagination
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @param {Object} sorting - Sorting criteria
   * @returns {Promise<Object>} Paginated offers
   */
  static async getPublicOffers(filters = {}, page = 1, limit = 10, sorting = {}) {
    try {
      return await PublicOfferModel.getPublicOffers(filters, page, limit, sorting);
    } catch (error) {
      throw new ApiError(500, `Failed to get public offers: ${error.message}`);
    }
  }

  /**
   * Get offer details by ID
   * @param {string} offerId - Offer ID
   * @returns {Promise<Object>} Offer details
   */
  static async getOfferDetails(offerId) {
    try {
      return await PublicOfferModel.getOfferDetails(offerId);
    } catch (error) {
      throw new ApiError(500, `Failed to get offer details: ${error.message}`);
    }
  }

  /**
   * Get offers by seller
   * @param {string} sellerId - Seller ID
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated offers
   */
  static async getOffersBySeller(sellerId, page = 1, limit = 10) {
    try {
      return await PublicOfferModel.getOffersBySeller(sellerId, page, limit);
    } catch (error) {
      throw new ApiError(500, `Failed to get seller offers: ${error.message}`);
    }
  }

  /**
   * Get offers by category
   * @param {string} categoryId - Category ID
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated offers
   */
  static async getOffersByCategory(categoryId, page = 1, limit = 10) {
    try {
      return await PublicOfferModel.getOffersByCategory(categoryId, page, limit);
    } catch (error) {
      throw new ApiError(500, `Failed to get category offers: ${error.message}`);
    }
  }

  /**
   * Get offers by subcategory
   * @param {string} subcategoryId - Subcategory ID
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated offers
   */
  static async getOffersBySubcategory(subcategoryId, page = 1, limit = 10) {
    try {
      return await PublicOfferModel.getOffersBySubcategory(subcategoryId, page, limit);
    } catch (error) {
      throw new ApiError(500, `Failed to get subcategory offers: ${error.message}`);
    }
  }

  /**
   * Search offers
   * @param {string} searchTerm - Search term
   * @param {Object} filters - Additional filters
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated search results
   */
  static async searchOffers(searchTerm, filters = {}, page = 1, limit = 10) {
    try {
      return await PublicOfferModel.searchOffers(searchTerm, filters, page, limit);
    } catch (error) {
      throw new ApiError(500, `Failed to search offers: ${error.message}`);
    }
  }

  /**
   * Get featured offers
   * @param {number} limit - Number of featured offers to return
   * @returns {Promise<Array>} Featured offers
   */
  static async getFeaturedOffers(limit = 10) {
    try {
      return await PublicOfferModel.getFeaturedOffers(limit);
    } catch (error) {
      throw new ApiError(500, `Failed to get featured offers: ${error.message}`);
    }
  }

  /**
   * Get offer statistics
   * @returns {Promise<Object>} Offer statistics
   */
  static async getOfferStatistics() {
    try {
      return await PublicOfferModel.getOfferStatistics();
    } catch (error) {
      throw new ApiError(500, `Failed to get offer statistics: ${error.message}`);
    }
  }
}

module.exports = PublicOfferService;
