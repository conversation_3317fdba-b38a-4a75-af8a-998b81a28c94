# Admin User Management with Business Information

## Overview

When admin users fetch user details, business information is automatically included for sellers. This provides a comprehensive view of seller profiles including their business details.

## Updated API Responses

### 1. GET /api/admin/users/:id - Get User by ID

#### For Seller Users
```json
{
  "success": true,
  "message": "User retrieved successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>",
    "email": "<EMAIL>",
    "phone_number": "+*********0",
    "profile_picture_url": "/public/uploads/avatars/john_avatar.jpg",
    "gender": "Male",
    "date_of_birth": "1990-01-15T00:00:00.000Z",
    "address": "123 Main Street, City, State",
    "is_email_verified": true,
    "is_approved": true,
    "status": "Active",
    "is_deleted": false,
    "created_at": "2024-01-01T10:00:00.000Z",
    "updated_at": "2024-01-15T14:30:00.000Z",
    "roles": ["Seller"],
    "business_informations": [
      {
        "id": "550e8400-e29b-41d4-a716-446655440001",
        "seller_id": "550e8400-e29b-41d4-a716-446655440000",
        "business_name": "Tech Solutions Inc",
        "short_description": "We provide innovative technology solutions",
        "long_description": "Tech Solutions Inc is a leading technology company...",
        "logo_url": "/public/uploads/business/logos/550e8400-e29b-41d4-a716-446655440001.png",
        "banner_url": "/public/uploads/business/banners/550e8400-e29b-41d4-a716-446655440002.jpg",
        "address": "123 Tech Street, Suite 456",
        "city": "San Francisco",
        "state": "California",
        "country": "USA",
        "postal_code": "94105",
        "phone_number": "******-555-0123",
        "email": "<EMAIL>",
        "website_url": "https://www.techsolutions.com",
        "business_type": "Technology",
        "business_category": "Software Development",
        "established_year": 2020,
        "employee_count": "11-50",
        "annual_revenue": "$1M-$10M",
        "business_license": "BL-CA-*********",
        "tax_id": "TAX-*********",
        "social_media_links": {
          "facebook": "https://facebook.com/techsolutions",
          "twitter": "https://twitter.com/techsolutions",
          "linkedin": "https://linkedin.com/company/tech-solutions"
        },
        "operating_hours": {
          "monday": "9:00 AM - 6:00 PM",
          "tuesday": "9:00 AM - 6:00 PM",
          "wednesday": "9:00 AM - 6:00 PM",
          "thursday": "9:00 AM - 6:00 PM",
          "friday": "9:00 AM - 6:00 PM",
          "saturday": "10:00 AM - 2:00 PM",
          "sunday": "Closed"
        },
        "services_offered": [
          "Web Development",
          "Mobile App Development",
          "Cloud Solutions",
          "IT Consulting"
        ],
        "certifications": [
          "ISO 9001:2015 Quality Management",
          "AWS Certified Solutions Architect",
          "Microsoft Gold Partner"
        ],
        "is_verified": true,
        "verification_status": "verified",
        "verification_date": "2024-01-16T09:15:00.000Z",
        "is_active": true,
        "is_deleted": false,
        "created_at": "2024-01-15T10:30:00.000Z",
        "updated_at": "2024-01-16T09:15:00.000Z"
      },
      {
        "id": "550e8400-e29b-41d4-a716-446655440002",
        "seller_id": "550e8400-e29b-41d4-a716-446655440000",
        "business_name": "Digital Marketing Pro",
        "short_description": "Professional digital marketing services",
        "long_description": "We help businesses grow their online presence...",
        "logo_url": "/public/uploads/business/logos/550e8400-e29b-41d4-a716-446655440003.png",
        "banner_url": "/public/uploads/business/banners/550e8400-e29b-41d4-a716-446655440004.jpg",
        "address": "456 Marketing Ave",
        "city": "Los Angeles",
        "state": "California",
        "country": "USA",
        "postal_code": "90210",
        "phone_number": "******-555-0456",
        "email": "<EMAIL>",
        "website_url": "https://www.digitalmarketingpro.com",
        "business_type": "Service",
        "business_category": "Digital Marketing",
        "established_year": 2019,
        "employee_count": "1-10",
        "annual_revenue": "$100K-$1M",
        "business_license": "BL-CA-*********",
        "tax_id": "TAX-*********",
        "social_media_links": {
          "facebook": "https://facebook.com/digitalmarketingpro",
          "instagram": "https://instagram.com/digitalmarketingpro"
        },
        "operating_hours": {
          "monday": "8:00 AM - 7:00 PM",
          "tuesday": "8:00 AM - 7:00 PM",
          "wednesday": "8:00 AM - 7:00 PM",
          "thursday": "8:00 AM - 7:00 PM",
          "friday": "8:00 AM - 5:00 PM",
          "saturday": "Closed",
          "sunday": "Closed"
        },
        "services_offered": [
          "SEO Optimization",
          "Social Media Marketing",
          "PPC Advertising",
          "Content Marketing"
        ],
        "certifications": [
          "Google Ads Certified",
          "Facebook Blueprint Certified"
        ],
        "is_verified": false,
        "verification_status": "pending",
        "verification_date": null,
        "is_active": true,
        "is_deleted": false,
        "created_at": "2024-01-14T14:20:00.000Z",
        "updated_at": "2024-01-14T14:20:00.000Z"
      }
    ]
  }
}
```

#### For Buyer Users
```json
{
  "success": true,
  "message": "User retrieved successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440010",
    "first_name": "Jane",
    "last_name": "Smith",
    "email": "<EMAIL>",
    "phone_number": "+1*********",
    "profile_picture_url": "/public/uploads/avatars/jane_avatar.jpg",
    "gender": "Female",
    "date_of_birth": "1992-05-20T00:00:00.000Z",
    "address": "456 Buyer Lane, City, State",
    "is_email_verified": true,
    "is_approved": true,
    "status": "Active",
    "is_deleted": false,
    "created_at": "2024-01-02T11:00:00.000Z",
    "updated_at": "2024-01-10T16:45:00.000Z",
    "roles": ["Buyer"]
    // Note: No business_informations field for buyers
  }
}
```

### 2. GET /api/admin/users - Get All Users

#### Response with Sellers and Buyers
```json
{
  "success": true,
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "first_name": "John",
      "last_name": "Doe",
      "email": "<EMAIL>",
      "roles": ["Seller"],
      "business_informations": [
        {
          "id": "550e8400-e29b-41d4-a716-446655440001",
          "business_name": "Tech Solutions Inc",
          "short_description": "We provide innovative technology solutions",
          "logo_url": "/public/uploads/business/logos/550e8400-e29b-41d4-a716-446655440001.png",
          "verification_status": "verified",
          "is_active": true,
          "created_at": "2024-01-15T10:30:00.000Z"
        }
      ],
      "created_at": "2024-01-01T10:00:00.000Z"
    },
    {
      "id": "550e8400-e29b-41d4-a716-446655440010",
      "first_name": "Jane",
      "last_name": "Smith",
      "email": "<EMAIL>",
      "roles": ["Buyer"],
      // Note: No business_informations field for buyers
      "created_at": "2024-01-02T11:00:00.000Z"
    }
  ],
  "meta": {
    "total": 2,
    "pages": 1,
    "currentPage": 1,
    "perPage": 10
  }
}
```

## Key Features

### 1. **Automatic Inclusion**
- Business information is automatically included for sellers
- No additional API calls needed
- Filtered out for non-seller users

### 2. **Complete Business Data**
- All business information fields included
- Verification status visible
- File URLs for logos and banners
- JSON fields properly formatted

### 3. **Multiple Businesses**
- Sellers can have multiple business entities
- All active businesses are included
- Ordered by creation date (newest first)

### 4. **Admin Benefits**
- Complete seller profile view
- Business verification status at a glance
- Contact information for each business
- Business performance metrics available

## Business Information Fields Included

| Field | Description |
|-------|-------------|
| `business_name` | Name of the business |
| `short_description` | Brief business description |
| `long_description` | Detailed business description |
| `logo_url` | Business logo file path |
| `banner_url` | Business banner file path |
| `address` | Business physical address |
| `city`, `state`, `country` | Location details |
| `postal_code` | ZIP/postal code |
| `phone_number` | Business contact number |
| `email` | Business email address |
| `website_url` | Business website |
| `business_type` | Type of business (Technology, Service, etc.) |
| `business_category` | Specific category |
| `established_year` | Year business was established |
| `employee_count` | Number of employees |
| `annual_revenue` | Revenue range |
| `business_license` | License number |
| `tax_id` | Tax identification |
| `social_media_links` | Social media profiles (JSON) |
| `operating_hours` | Business hours (JSON) |
| `services_offered` | List of services (JSON) |
| `certifications` | Business certifications (JSON) |
| `is_verified` | Admin verification status |
| `verification_status` | pending/verified/rejected |
| `verification_date` | Date of verification |
| `is_active` | Whether business is active |

## Admin Actions Available

1. **View Complete Seller Profile** - See all business information
2. **Verify Business Information** - Update verification status
3. **Monitor Business Activity** - Track business performance
4. **Contact Business** - Direct access to business contact info
5. **Audit Business Details** - Review all submitted information

## Benefits for Admin Management

- **Comprehensive View**: Complete seller profile in one API call
- **Verification Workflow**: Easy access to verification status
- **Business Insights**: Detailed business information for decision making
- **Contact Management**: Direct access to business contact details
- **Performance Tracking**: Monitor seller business growth
