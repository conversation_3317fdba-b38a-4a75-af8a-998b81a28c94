const SortOrderHelper = require('../utils/sortOrderHelper');
const sendResponse = require('../utils/sendResponse');

class SortOrderController {
  /**
   * Update category sort order
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async updateCategorySortOrder(req, res) {
    try {
      const { id } = req.params;
      const { sort_order } = req.body;

      // Parse and validate sort_order
      const parsedSortOrder = parseInt(sort_order);

      if (!sort_order || isNaN(parsedSortOrder) || parsedSortOrder < 1) {
        return sendResponse(
          res,
          false,
          'Valid sort_order is required',
          null,
          { sort_order: ['sort_order must be a positive integer'] },
          null,
          400
        );
      }

      await SortOrderHelper.updateCategorySortOrder(id, parsedSortOrder);

      return sendResponse(
        res,
        true,
        'Category sort order updated successfully',
        { id, sort_order: parsedSortOrder },
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Update subcategory sort order
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async updateSubCategorySortOrder(req, res) {
    try {
      const { id } = req.params;
      const { sort_order } = req.body;

      // Parse and validate sort_order
      const parsedSortOrder = parseInt(sort_order);

      if (!sort_order || isNaN(parsedSortOrder) || parsedSortOrder < 1) {
        return sendResponse(
          res,
          false,
          'Valid sort_order is required',
          null,
          { sort_order: ['sort_order must be a positive integer'] },
          null,
          400
        );
      }

      await SortOrderHelper.updateSubCategorySortOrder(id, parsedSortOrder);

      return sendResponse(
        res,
        true,
        'Subcategory sort order updated successfully',
        { id, sort_order: parsedSortOrder },
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Update form field sort order
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async updateFormFieldSortOrder(req, res) {
    try {
      const { id } = req.params;
      const { sort_order } = req.body;

      // Parse and validate sort_order
      const parsedSortOrder = parseInt(sort_order);

      if (!sort_order || isNaN(parsedSortOrder) || parsedSortOrder < 1) {
        return sendResponse(
          res,
          false,
          'Valid sort_order is required',
          null,
          { sort_order: ['sort_order must be a positive integer'] },
          null,
          400
        );
      }

      await SortOrderHelper.updateFormFieldSortOrder(id, parsedSortOrder);

      return sendResponse(
        res,
        true,
        'Form field sort order updated successfully',
        { id, sort_order: parsedSortOrder },
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Reorder all categories
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async reorderCategories(req, res) {
    try {
      await SortOrderHelper.reorderCategories();

      return sendResponse(
        res,
        true,
        'Categories reordered successfully',
        null,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Reorder subcategories within a category
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async reorderSubCategories(req, res) {
    try {
      const { categoryId } = req.params;

      await SortOrderHelper.reorderSubCategories(categoryId);

      return sendResponse(
        res,
        true,
        'Subcategories reordered successfully',
        { categoryId },
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Reorder form fields within a subcategory
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async reorderFormFields(req, res) {
    try {
      const { subcategoryId } = req.params;

      await SortOrderHelper.reorderFormFields(subcategoryId);

      return sendResponse(
        res,
        true,
        'Form fields reordered successfully',
        { subcategoryId },
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Bulk update category sort orders
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async bulkUpdateCategorySortOrder(req, res) {
    try {
      const { categories } = req.body;

      if (!Array.isArray(categories) || categories.length === 0) {
        return sendResponse(
          res,
          false,
          'Categories array is required',
          null,
          { categories: ['Categories array is required and must not be empty'] },
          null,
          400
        );
      }

      // Validate each category object
      for (const category of categories) {
        if (!category.id || !category.sort_order || category.sort_order < 1) {
          return sendResponse(
            res,
            false,
            'Each category must have valid id and sort_order',
            null,
            { categories: ['Each category must have valid id and sort_order (positive integer)'] },
            null,
            400
          );
        }
      }

      // Update each category's sort order
      for (const category of categories) {
        await SortOrderHelper.updateCategorySortOrder(category.id, category.sort_order);
      }

      return sendResponse(
        res,
        true,
        'Category sort orders updated successfully',
        { updated: categories.length },
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Bulk update subcategory sort orders
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async bulkUpdateSubCategorySortOrder(req, res) {
    try {
      const { subcategories } = req.body;

      if (!Array.isArray(subcategories) || subcategories.length === 0) {
        return sendResponse(
          res,
          false,
          'Subcategories array is required',
          null,
          { subcategories: ['Subcategories array is required and must not be empty'] },
          null,
          400
        );
      }

      // Validate each subcategory object
      for (const subcategory of subcategories) {
        if (!subcategory.id || !subcategory.sort_order || subcategory.sort_order < 1) {
          return sendResponse(
            res,
            false,
            'Each subcategory must have valid id and sort_order',
            null,
            { subcategories: ['Each subcategory must have valid id and sort_order (positive integer)'] },
            null,
            400
          );
        }
      }

      // Update each subcategory's sort order
      for (const subcategory of subcategories) {
        await SortOrderHelper.updateSubCategorySortOrder(subcategory.id, subcategory.sort_order);
      }

      return sendResponse(
        res,
        true,
        'Subcategory sort orders updated successfully',
        { updated: subcategories.length },
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Bulk update form field sort orders
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async bulkUpdateFormFieldSortOrder(req, res) {
    try {
      const { formFields } = req.body;

      if (!Array.isArray(formFields) || formFields.length === 0) {
        return sendResponse(
          res,
          false,
          'Form fields array is required',
          null,
          { formFields: ['Form fields array is required and must not be empty'] },
          null,
          400
        );
      }

      // Validate each form field object
      for (const formField of formFields) {
        if (!formField.id || !formField.sort_order || formField.sort_order < 1) {
          return sendResponse(
            res,
            false,
            'Each form field must have valid id and sort_order',
            null,
            { formFields: ['Each form field must have valid id and sort_order (positive integer)'] },
            null,
            400
          );
        }
      }

      // Update each form field's sort order
      for (const formField of formFields) {
        await SortOrderHelper.updateFormFieldSortOrder(formField.id, formField.sort_order);
      }

      return sendResponse(
        res,
        true,
        'Form field sort orders updated successfully',
        { updated: formFields.length },
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }
}

module.exports = SortOrderController;
