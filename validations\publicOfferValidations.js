const { query, param } = require('express-validator');

// Validation rules for filtering public offers
const filterPublicOffers = [
  query('category_id')
    .optional()
    .isUUID()
    .withMessage('Category ID must be a valid UUID'),

  query('subcategory_id')
    .optional()
    .isUUID()
    .withMessage('Subcategory ID must be a valid UUID'),

  query('offer_type')
    .optional()
    .isIn(['digital_product', 'physical_product', 'service'])
    .withMessage('Offer type must be one of: digital_product, physical_product, service'),

  query('min_price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Minimum price must be a positive number'),

  query('max_price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Maximum price must be a positive number')
    .custom((value, { req }) => {
      if (req.query.min_price && parseFloat(value) < parseFloat(req.query.min_price)) {
        throw new Error('Maximum price must be greater than or equal to minimum price');
      }
      return true;
    }),

  query('search')
    .optional()
    .isString()
    .withMessage('Search term must be a string')
    .isLength({ min: 2, max: 100 })
    .withMessage('Search term must be between 2 and 100 characters'),

  query('sort_by')
    .optional()
    .isIn(['created_at', 'price', 'offer_title', 'delivery_time'])
    .withMessage('Sort by must be one of: created_at, price, offer_title, delivery_time'),

  query('sort_order')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be either asc or desc'),

  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
];

// Validation rules for offer ID parameter
const offerIdParam = [
  param('id')
    .isUUID()
    .withMessage('Offer ID must be a valid UUID'),
];

// Validation rules for seller ID parameter
const sellerIdParam = [
  param('sellerId')
    .isUUID()
    .withMessage('Seller ID must be a valid UUID'),
];

// Validation rules for category ID parameter
const categoryIdParam = [
  param('categoryId')
    .isUUID()
    .withMessage('Category ID must be a valid UUID'),
];

// Validation rules for subcategory ID parameter
const subcategoryIdParam = [
  param('subcategoryId')
    .isUUID()
    .withMessage('Subcategory ID must be a valid UUID'),
];

// Validation rules for pagination
const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
];

module.exports = {
  filterPublicOffers,
  offerIdParam,
  sellerIdParam,
  categoryIdParam,
  subcategoryIdParam,
  paginationValidation
};
