/*
  Warnings:

  - A unique constraint covering the columns `[username]` on the table `users` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateEnum
CREATE TYPE "subscription_user_types" AS ENUM ('BUYER', 'SELLER', 'BOTH');

-- C<PERSON><PERSON>num
CREATE TYPE "subscription_statuses" AS ENUM ('ACTIVE', 'EXPIRED', 'CANCELLED', 'SUSPENDED');

-- CreateEnum
CREATE TYPE "usage_types" AS ENUM ('REQUEST', 'OFFER', 'ORDER');

-- DropForeignKey
ALTER TABLE "offers" DROP CONSTRAINT "offers_request_id_fkey";

-- DropIndex
DROP INDEX "business_informations_business_category_idx";

-- DropIndex
DROP INDEX "business_informations_business_type_idx";

-- DropIndex
DROP INDEX "business_informations_city_idx";

-- DropIndex
DROP INDEX "business_informations_company_name_idx";

-- DropIndex
DROP INDEX "business_informations_country_idx";

-- DropIndex
DROP INDEX "business_informations_created_at_idx";

-- DropIndex
DROP INDEX "business_informations_is_active_idx";

-- DropIndex
DROP INDEX "business_informations_is_deleted_idx";

-- DropIndex
DROP INDEX "business_informations_seller_id_idx";

-- DropIndex
DROP INDEX "business_informations_verification_status_idx";

-- DropIndex
DROP INDEX "offers_offer_code_idx";

-- DropIndex
DROP INDEX "requests_request_code_idx";

-- DropIndex
DROP INDEX "users_business_name_idx";

-- AlterTable
ALTER TABLE "business_informations" ALTER COLUMN "updated_at" DROP DEFAULT;

-- AlterTable
ALTER TABLE "users" ADD COLUMN     "age_confirm" BOOLEAN DEFAULT false,
ADD COLUMN     "bio" TEXT,
ADD COLUMN     "business_address" TEXT,
ADD COLUMN     "business_document_url" TEXT,
ADD COLUMN     "business_registration_number" TEXT,
ADD COLUMN     "business_type" TEXT,
ADD COLUMN     "business_website" TEXT,
ADD COLUMN     "city" TEXT,
ADD COLUMN     "country" TEXT,
ADD COLUMN     "interests" TEXT,
ADD COLUMN     "nid_document_url" TEXT,
ADD COLUMN     "occupation" TEXT,
ADD COLUMN     "state" TEXT,
ADD COLUMN     "street" TEXT,
ADD COLUMN     "tax_id" TEXT,
ADD COLUMN     "user_type" TEXT,
ADD COLUMN     "username" TEXT,
ADD COLUMN     "zip" TEXT;

-- CreateTable
CREATE TABLE "subscriptions" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "price" DECIMAL(65,30) NOT NULL DEFAULT 0.00,
    "duration_days" INTEGER NOT NULL DEFAULT 30,
    "max_requests" INTEGER,
    "max_offers" INTEGER,
    "max_orders" INTEGER,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "is_featured" BOOLEAN NOT NULL DEFAULT false,
    "user_type" "subscription_user_types" NOT NULL DEFAULT 'BOTH',
    "features" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "subscriptions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_subscriptions" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "subscription_id" TEXT NOT NULL,
    "status" "subscription_statuses" NOT NULL DEFAULT 'ACTIVE',
    "start_date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "end_date" TIMESTAMP(3) NOT NULL,
    "auto_renew" BOOLEAN NOT NULL DEFAULT false,
    "payment_method" TEXT,
    "transaction_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_subscriptions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "subscription_usage" (
    "id" TEXT NOT NULL,
    "user_subscription_id" TEXT NOT NULL,
    "usage_type" "usage_types" NOT NULL DEFAULT 'REQUEST',
    "count" INTEGER NOT NULL DEFAULT 0,
    "month" INTEGER NOT NULL,
    "year" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "subscription_usage_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "subscriptions_name_key" ON "subscriptions"("name");

-- CreateIndex
CREATE INDEX "subscriptions_user_type_idx" ON "subscriptions"("user_type");

-- CreateIndex
CREATE INDEX "subscriptions_is_active_idx" ON "subscriptions"("is_active");

-- CreateIndex
CREATE INDEX "user_subscriptions_user_id_idx" ON "user_subscriptions"("user_id");

-- CreateIndex
CREATE INDEX "user_subscriptions_subscription_id_idx" ON "user_subscriptions"("subscription_id");

-- CreateIndex
CREATE INDEX "user_subscriptions_status_idx" ON "user_subscriptions"("status");

-- CreateIndex
CREATE INDEX "user_subscriptions_end_date_idx" ON "user_subscriptions"("end_date");

-- CreateIndex
CREATE INDEX "subscription_usage_user_subscription_id_idx" ON "subscription_usage"("user_subscription_id");

-- CreateIndex
CREATE INDEX "subscription_usage_month_year_idx" ON "subscription_usage"("month", "year");

-- CreateIndex
CREATE UNIQUE INDEX "subscription_usage_user_subscription_id_usage_type_month_ye_key" ON "subscription_usage"("user_subscription_id", "usage_type", "month", "year");

-- CreateIndex
CREATE UNIQUE INDEX "users_username_key" ON "users"("username");

-- AddForeignKey
ALTER TABLE "offers" ADD CONSTRAINT "offers_request_id_fkey" FOREIGN KEY ("request_id") REFERENCES "requests"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_subscriptions" ADD CONSTRAINT "user_subscriptions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_subscriptions" ADD CONSTRAINT "user_subscriptions_subscription_id_fkey" FOREIGN KEY ("subscription_id") REFERENCES "subscriptions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "subscription_usage" ADD CONSTRAINT "subscription_usage_user_subscription_id_fkey" FOREIGN KEY ("user_subscription_id") REFERENCES "user_subscriptions"("id") ON DELETE CASCADE ON UPDATE CASCADE;
