const Category = require('../models/categoryModel');

class CategoryService {
    static async create(data) {
        return await Category.createCategory(data);
    }

    static async findOne(id, query) {
        return await Category.findCategoryById(id, query);
    }

    static async findByName(name) {
        return await Category.findCategoryByTitle(name);
    }

    static async update(id, data) {
        return await Category.updateCategory(id, data);
    }

    static async delete(id) {
        return await Category.deleteCategory(id);
    }

    static async getAll(filters) {
        return await Category.getAllCategories(filters);
    }

    static async getAllPaginated(filters, page, limit) {
        return await Category.getAllCategoriesPaginated(filters, page, limit);
    }

    static async exists(id) {
        try {
            const category = await this.findOne(id);
            return !!category;
        } catch (error) {
            return false;
        }
    }

    static async getWithSubcategories(id) {
        return await Category.getCategoryWithSubcategories(id);
    }

    static async searchByName(name) {
        return await Category.searchCategoriesByName(name);
    }

    static async getPremiumCategories() {
        return await Category.getPremiumCategories();
    }
}

module.exports = CategoryService;