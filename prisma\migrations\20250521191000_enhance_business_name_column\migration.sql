-- Enhance business_name column in users table
DO $$
BEGIN
    -- Check if business_name column exists
    IF EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'business_name'
    ) THEN
        -- Add index on business_name for better search performance
        IF NOT EXISTS (
            SELECT FROM pg_indexes 
            WHERE tablename = 'users' 
            AND indexname = 'users_business_name_idx'
        ) THEN
            CREATE INDEX "users_business_name_idx" ON "users"("business_name");
        END IF;
        
        -- Add partial index for non-null business names
        IF NOT EXISTS (
            SELECT FROM pg_indexes 
            WHERE tablename = 'users' 
            AND indexname = 'users_business_name_not_null_idx'
        ) THEN
            CREATE INDEX "users_business_name_not_null_idx" ON "users"("business_name") WHERE "business_name" IS NOT NULL;
        END IF;
    END IF;
END $$;
