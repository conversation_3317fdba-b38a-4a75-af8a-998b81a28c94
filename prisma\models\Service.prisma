model services {
  id               String          @id @default(uuid())
  title            String          // e.g., "Website Development"
  slug             String          @unique // "website-development"
  description      String?
  duration         Int?            // Minutes (e.g., 60 for 1-hour service)
  price_type       service_price_types @default(FIXED) // FIXED, HOURLY, VARIABLE
  base_price       Decimal         @default(0.00)
  min_price        Decimal?        // For VARIABLE pricing
  max_price        Decimal?        // For VARIABLE pricing
  is_available     Boolean         @default(true)
  status           service_statuses @default(DRAFT) // DRAFT, ACTIVE, PAUSED
  booking_type     booking_types   @default(INSTANT) // INSTANT, APPROVAL_REQUIRED

  // Relations

  provider         users           @relation(fields: [provider_id], references: [id], name: "SellerService")
  provider_id      String
  category         categories      @relation(fields: [category_id], references: [id], name: "ServiceCategory")
  category_id      String
  sub_category     sub_categories? @relation(fields: [sub_category_id], references: [id], name: "ServiceSubCategory")
  sub_category_id  String?

  // Metadata
  tags             String[]        // ["web-design", "seo"]
  faqs             service_faqs[]
  requirements     service_requirements[]
  seo_title        String?
  seo_description  String?

  // Timestamps
  created_at       DateTime        @default(now())
  updated_at       DateTime        @updatedAt

  // Child Relations
  service_addons   service_addons[]
  service_media    service_media[]
  availability     service_availabilities[]
  reviews          service_reviews[]

  @@index([provider_id])
  @@index([category_id])
  @@index([sub_category_id])
}

enum service_price_types {
  FIXED      // Single price (e.g., $100)
  HOURLY     // Price per hour (e.g., $50/hour)
  VARIABLE   // Price range (e.g., $50-$200)
}

enum service_statuses {
  DRAFT
  ACTIVE
  PAUSED
  DELETED
}

enum booking_types {
  INSTANT           // Book without approval
  APPROVAL_REQUIRED // Provider must confirm
}

model service_addons {
  id           String   @id @default(uuid())
  name         String   // e.g., "Extra Fast Delivery"
  description  String?
  price        Decimal  @default(0.00)
  duration     Int?     // Additional minutes (e.g., +30 mins)
  service      services @relation(fields: [service_id], references: [id])
  service_id   String

  @@index([service_id])
}

model service_media {
  id          String   @id @default(uuid())
  url         String
  type        media_types @default(IMAGE) // IMAGE, VIDEO
  alt_text    String?
  is_primary  Boolean  @default(false)
  service     services @relation(fields: [service_id], references: [id])
  service_id  String

  @@index([service_id])
}

enum media_types {
  IMAGE
  VIDEO
}

model service_availabilities {
  id           String       @id @default(uuid())
  service      services     @relation(fields: [service_id], references: [id])
  service_id   String
  day_of_week  day_of_weeks // MONDAY, TUESDAY, etc.
  start_time   DateTime     // 09:00
  end_time     DateTime     // 17:00
  is_recurring Boolean      @default(true)

  @@index([service_id])
}

enum day_of_weeks {
  MONDAY
  TUESDAY
  WEDNESDAY
  THURSDAY
  FRIDAY
  SATURDAY
  SUNDAY
}

model service_requirements {
  id           String            @id @default(uuid())
  service      services          @relation(fields: [service_id], references: [id])
  service_id   String
  question     String            // e.g., "Do you have a domain name?"
  type         requirement_types @default(TEXT) // TEXT, FILE, CHOICE
  is_required  Boolean           @default(false)
  options      String[]    @default([])

  @@index([service_id])
}

enum requirement_types {
  TEXT
  FILE
  CHOICE
}

model service_reviews {
  id          String     @id @default(uuid())
  service     services   @relation(fields: [service_id], references: [id])
  service_id  String
  user_id     String     // Reference to User table
  rating      Int
  comment     String?
  created_at  DateTime   @default(now())

  @@index([service_id])
  @@index([user_id])
}

model service_faqs {
  id          String   @id @default(uuid())
  service     services @relation(fields: [service_id], references: [id])
  service_id  String
  question    String
  answer      String

  @@index([service_id])
}
