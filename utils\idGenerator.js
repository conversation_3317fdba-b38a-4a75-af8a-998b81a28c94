/**
 * Utility functions for generating unique identifiers
 */

// Counter for requests and offers created on the current day
// This will reset when the server restarts, but that's acceptable
// since the date in the code will still make it unique
const dailyCounters = {
  requests: 0,
  offers: 0,
  lastResetDate: new Date().toDateString()
};

// Check if we need to reset the counters (new day)
function checkAndResetCounters() {
  const today = new Date().toDateString();
  if (today !== dailyCounters.lastResetDate) {
    dailyCounters.requests = 0;
    dailyCounters.offers = 0;
    dailyCounters.lastResetDate = today;
  }
}

/**
 * Generate a unique identifier for requests based on year, month, day and count
 * Format: REQ-YYYYMMDD-XXXX (e.g., REQ-20240615-0001)
 *
 * @param {Object} request - The request object (optional)
 * @returns {string} The generated unique identifier
 */
function generateRequestCode(request = null) {
  checkAndResetCounters();

  // Increment the counter
  dailyCounters.requests++;

  // Use the request's created_at date if available, otherwise use current date
  const date = request && request.created_at ? new Date(request.created_at) : new Date();

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const datePrefix = `${year}${month}${day}`;

  // Generate the sequence number and pad with zeros
  const sequence = String(dailyCounters.requests).padStart(4, '0');

  return `REQ-${datePrefix}-${sequence}`;
}

/**
 * Generate a unique identifier for offers based on year, month, day and count
 * Format: OFF-YYYYMMDD-XXXX (e.g., OFF-20240615-0001)
 *
 * @param {Object} offer - The offer object (optional)
 * @returns {string} The generated unique identifier
 */
function generateOfferCode(offer = null) {
  checkAndResetCounters();

  // Increment the counter
  dailyCounters.offers++;

  // Use the offer's created_at date if available, otherwise use current date
  const date = offer && offer.created_at ? new Date(offer.created_at) : new Date();

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const datePrefix = `${year}${month}${day}`;

  // Generate the sequence number and pad with zeros
  const sequence = String(dailyCounters.offers).padStart(4, '0');

  return `OFF-${datePrefix}-${sequence}`;
}

module.exports = {
  generateRequestCode,
  generateOfferCode
};
