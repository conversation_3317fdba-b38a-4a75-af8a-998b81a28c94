const multer = require('multer');

// You can customize storage config based on your file saving logic
const storage = multer.memoryStorage(); // or use diskStorage
const upload = multer({ storage });

const uploadCategoryFiles = upload.fields([
    { name: 'image', maxCount: 1 },
    { name: 'thumbnail', maxCount: 1 },
  ]);


  const uploadRequestFiles = upload.fields([
    { name: 'image', maxCount: 20 },
    { name: 'file', maxCount: 1 }
  ]);


  const uploadAvatarUser = upload.single('avatar');

  const uploadBusinessFiles = upload.fields([
    { name: 'logo', maxCount: 1 },
    { name: 'banner', maxCount: 1 }
  ]);

  // Bulk upload middleware for CSV/Excel files
  const uploadBulkFile = upload.single('file');

module.exports = { upload, uploadCategoryFiles, uploadRequestFiles, uploadAvatarUser, uploadBusinessFiles, uploadBulkFile };
