const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * Utility class for managing automatic sort order assignment
 */
class SortOrderHelper {
  /**
   * Get the next sort order for categories
   * @returns {Promise<number>} Next sort order value
   */
  static async getNextCategorySortOrder() {
    const lastCategory = await prisma.categories.findFirst({
      where: {
        is_deleted: false
      },
      orderBy: [
        { sort_order: 'desc' },
        { created_at: 'desc' }
      ],
      select: {
        sort_order: true
      }
    });

    // Handle null sort_order values
    const maxSortOrder = lastCategory?.sort_order;
    return (maxSortOrder && maxSortOrder > 0) ? maxSortOrder + 1 : 1;
  }

  /**
   * Get the next sort order for subcategories within a category
   * @param {string} categoryId - Category ID
   * @returns {Promise<number>} Next sort order value
  */
  static async getNextSubCategorySortOrder(categoryId) {
    const lastSubCategory = await prisma.sub_categories.findFirst({
      where: {
        category_id: categoryId,
        is_deleted: false
      },
      orderBy: [
        { sort_order: 'desc' },
        { created_at: 'desc' }
      ],
      select: {
        sort_order: true
      }
    });

    // Handle null sort_order values
    const maxSortOrder = lastSubCategory?.sort_order;
    return (maxSortOrder && maxSortOrder > 0) ? maxSortOrder + 1 : 1;
  }

  /**
   * Get the next sort order for form fields within a subcategory
   * @param {string} subcategoryId - Subcategory ID
   * @returns {Promise<number>} Next sort order value
   */
  static async getNextFormFieldSortOrder(subcategoryId) {
    const lastFormField = await prisma.subcategory_form_fields.findFirst({
      where: {
        subcategory_id: subcategoryId
      },
      orderBy: [
        { sort_order: 'desc' },
        { created_at: 'desc' }
      ],
      select: {
        sort_order: true
      }
    });

    // Handle null sort_order values
    const maxSortOrder = lastFormField?.sort_order;
    return (maxSortOrder && maxSortOrder > 0) ? maxSortOrder + 1 : 1;
  }

  /**
   * Reorder categories after deletion or update
   * @param {string} excludeId - ID to exclude from reordering
   * @returns {Promise<void>}
   */
  static async reorderCategories(excludeId = null) {
    const categories = await prisma.categories.findMany({
      where: {
        is_deleted: false,
        ...(excludeId && { id: { not: excludeId } })
      },
      orderBy: [
        { sort_order: 'asc' },
        { created_at: 'asc' }
      ],
      select: {
        id: true
      }
    });

    // Update sort orders sequentially
    for (let i = 0; i < categories.length; i++) {
      await prisma.categories.update({
        where: { id: categories[i].id },
        data: { sort_order: i + 1 }
      });
    }
  }

  /**
   * Reorder subcategories within a category after deletion or update
   * @param {string} categoryId - Category ID
   * @param {string} excludeId - ID to exclude from reordering
   * @returns {Promise<void>}
   */
  static async reorderSubCategories(categoryId, excludeId = null) {
    const subCategories = await prisma.sub_categories.findMany({
      where: {
        category_id: categoryId,
        is_deleted: false,
        ...(excludeId && { id: { not: excludeId } })
      },
      orderBy: [
        { sort_order: 'asc' },
        { created_at: 'asc' }
      ],
      select: {
        id: true
      }
    });

    // Update sort orders sequentially
    for (let i = 0; i < subCategories.length; i++) {
      await prisma.sub_categories.update({
        where: { id: subCategories[i].id },
        data: { sort_order: i + 1 }
      });
    }
  }

  /**
   * Reorder form fields within a subcategory after deletion or update
   * @param {string} subcategoryId - Subcategory ID
   * @param {string} excludeId - ID to exclude from reordering
   * @returns {Promise<void>}
   */
  static async reorderFormFields(subcategoryId, excludeId = null) {
    const formFields = await prisma.subcategory_form_fields.findMany({
      where: {
        subcategory_id: subcategoryId,
        ...(excludeId && { id: { not: excludeId } })
      },
      orderBy: [
        { sort_order: 'asc' },
        { created_at: 'asc' }
      ],
      select: {
        id: true
      }
    });

    // Update sort orders sequentially
    for (let i = 0; i < formFields.length; i++) {
      await prisma.subcategory_form_fields.update({
        where: { id: formFields[i].id },
        data: { sort_order: i + 1 }
      });
    }
  }

  /**
   * Update sort order for a specific category
   * @param {string} categoryId - Category ID
   * @param {number} newSortOrder - New sort order
   * @returns {Promise<void>}
   */
  static async updateCategorySortOrder(categoryId, newSortOrder) {
    // Validate inputs
    if (!categoryId) {
      throw new Error('Category ID is required');
    }

    if (newSortOrder === null || newSortOrder === undefined || !Number.isInteger(newSortOrder) || newSortOrder < 1) {
      throw new Error('New sort order must be a positive integer');
    }

    // Get current category
    const currentCategory = await prisma.categories.findUnique({
      where: { id: categoryId },
      select: { sort_order: true }
    });

    if (!currentCategory) {
      throw new Error('Category not found');
    }

    const currentSortOrder = currentCategory.sort_order;

    // If current sort_order is null, assign the new sort_order directly
    if (currentSortOrder === null || currentSortOrder === undefined) {
      await prisma.categories.update({
        where: { id: categoryId },
        data: { sort_order: newSortOrder }
      });
      return;
    }

    // If the sort order is the same, no need to update
    if (newSortOrder === currentSortOrder) {
      return;
    }

    // If moving to a higher position (lower sort_order number)
    if (newSortOrder < currentSortOrder) {
      // Increment sort_order for categories between new and current position
      await prisma.categories.updateMany({
        where: {
          sort_order: {
            gte: newSortOrder,
            lt: currentSortOrder
          },
          is_deleted: false
        },
        data: {
          sort_order: {
            increment: 1
          }
        }
      });
    }
    // If moving to a lower position (higher sort_order number)
    else if (newSortOrder > currentSortOrder) {
      // Decrement sort_order for categories between current and new position
      await prisma.categories.updateMany({
        where: {
          sort_order: {
            gt: currentSortOrder,
            lte: newSortOrder
          },
          is_deleted: false
        },
        data: {
          sort_order: {
            decrement: 1
          }
        }
      });
    }

    // Update the target category
    await prisma.categories.update({
      where: { id: categoryId },
      data: { sort_order: newSortOrder }
    });
  }

  /**
   * Update sort order for a specific subcategory
   * @param {string} subcategoryId - Subcategory ID
   * @param {number} newSortOrder - New sort order
   * @returns {Promise<void>}
   */
  static async updateSubCategorySortOrder(subcategoryId, newSortOrder) {
    // Validate inputs
    if (!subcategoryId) {
      throw new Error('Subcategory ID is required');
    }

    if (newSortOrder === null || newSortOrder === undefined || !Number.isInteger(newSortOrder) || newSortOrder < 1) {
      throw new Error('New sort order must be a positive integer');
    }

    // Get current subcategory
    const currentSubCategory = await prisma.sub_categories.findUnique({
      where: { id: subcategoryId },
      select: { sort_order: true, category_id: true }
    });

    if (!currentSubCategory) {
      throw new Error('Subcategory not found');
    }

    const currentSortOrder = currentSubCategory.sort_order;
    const categoryId = currentSubCategory.category_id;

    // If current sort_order is null, assign the new sort_order directly
    if (currentSortOrder === null || currentSortOrder === undefined) {
      await prisma.sub_categories.update({
        where: { id: subcategoryId },
        data: { sort_order: newSortOrder }
      });
      return;
    }

    // If the sort order is the same, no need to update
    if (newSortOrder === currentSortOrder) {
      return;
    }

    // If moving to a higher position (lower sort_order number)
    if (newSortOrder < currentSortOrder) {
      // Increment sort_order for subcategories between new and current position
      await prisma.sub_categories.updateMany({
        where: {
          category_id: categoryId,
          sort_order: {
            gte: newSortOrder,
            lt: currentSortOrder
          },
          is_deleted: false
        },
        data: {
          sort_order: {
            increment: 1
          }
        }
      });
    }
    // If moving to a lower position (higher sort_order number)
    else if (newSortOrder > currentSortOrder) {
      // Decrement sort_order for subcategories between current and new position
      await prisma.sub_categories.updateMany({
        where: {
          category_id: categoryId,
          sort_order: {
            gt: currentSortOrder,
            lte: newSortOrder
          },
          is_deleted: false
        },
        data: {
          sort_order: {
            decrement: 1
          }
        }
      });
    }

    // Update the target subcategory
    await prisma.sub_categories.update({
      where: { id: subcategoryId },
      data: { sort_order: newSortOrder }
    });
  }

  /**
   * Update sort order for a specific form field
   * @param {string} formFieldId - Form field ID
   * @param {number} newSortOrder - New sort order
   * @returns {Promise<void>}
   */
  static async updateFormFieldSortOrder(formFieldId, newSortOrder) {
    // Validate inputs
    if (!formFieldId) {
      throw new Error('Form field ID is required');
    }

    if (newSortOrder === null || newSortOrder === undefined || !Number.isInteger(newSortOrder) || newSortOrder < 1) {
      throw new Error('New sort order must be a positive integer');
    }

    // Get current form field
    const currentFormField = await prisma.subcategory_form_fields.findUnique({
      where: { id: formFieldId },
      select: { sort_order: true, subcategory_id: true }
    });

    if (!currentFormField) {
      throw new Error('Form field not found');
    }

    const currentSortOrder = currentFormField.sort_order;
    const subcategoryId = currentFormField.subcategory_id;

    // If current sort_order is null, assign the new sort_order directly
    if (currentSortOrder === null || currentSortOrder === undefined) {
      await prisma.subcategory_form_fields.update({
        where: { id: formFieldId },
        data: { sort_order: newSortOrder }
      });
      return;
    }

    // If the sort order is the same, no need to update
    if (newSortOrder === currentSortOrder) {
      return;
    }

    // If moving to a higher position (lower sort_order number)
    if (newSortOrder < currentSortOrder) {
      // Increment sort_order for form fields between new and current position
      await prisma.subcategory_form_fields.updateMany({
        where: {
          subcategory_id: subcategoryId,
          sort_order: {
            gte: newSortOrder,
            lt: currentSortOrder
          }
        },
        data: {
          sort_order: {
            increment: 1
          }
        }
      });
    }
    // If moving to a lower position (higher sort_order number)
    else if (newSortOrder > currentSortOrder) {
      // Decrement sort_order for form fields between current and new position
      await prisma.subcategory_form_fields.updateMany({
        where: {
          subcategory_id: subcategoryId,
          sort_order: {
            gt: currentSortOrder,
            lte: newSortOrder
          }
        },
        data: {
          sort_order: {
            decrement: 1
          }
        }
      });
    }

    // Update the target form field
    await prisma.subcategory_form_fields.update({
      where: { id: formFieldId },
      data: { sort_order: newSortOrder }
    });
  }
}

module.exports = SortOrderHelper;
