# Database Schema Issue Fix - Bulk Request Actions

## Issue Description

The bulk request management API was failing with "Request not found" errors because the `approveRequest` and `rejectRequest` methods were trying to update database fields that don't exist in the `requests` table schema.

## Root Cause Analysis

### Missing Database Fields

The `approveRequest` and `rejectRequest` methods were attempting to update these fields in the `requests` table:
- `admin_notes` ❌ (doesn't exist)
- `approved_by` ❌ (doesn't exist)
- `approved_at` ❌ (doesn't exist)
- `rejected_by` ❌ (doesn't exist)
- `rejected_at` ❌ (doesn't exist)

### Actual Database Schema

The `requests` table only contains these fields:
```sql
CREATE TABLE "requests" (
    "id" TEXT NOT NULL,
    "request_code" TEXT,
    "buyer_id" TEXT NOT NULL,
    "category_id" TEXT NOT NULL,
    "sub_category_id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "short_description" TEXT,
    "description" TEXT,
    "quantity" INTEGER NOT NULL DEFAULT 1,
    "budget_min" DOUBLE PRECISION DEFAULT 0,
    "budget_max" DOUBLE PRECISION DEFAULT 0,
    "deadline" TIMESTAMP(3),
    "urgency" TEXT NOT NULL DEFAULT 'Normal',
    "status" TEXT NOT NULL DEFAULT 'Pending',
    "request_type" TEXT NOT NULL DEFAULT 'General',
    "location" TEXT,
    "additional_info" TEXT,
    "file" TEXT,
    "service_period" INTEGER,
    "session_count" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "is_deleted" BOOLEAN NOT NULL DEFAULT false,
    
    CONSTRAINT "requests_pkey" PRIMARY KEY ("id")
);
```

### Correct Approach

Admin notes and approval/rejection details should be stored in the `request_statuses` table, which has these fields:
```sql
CREATE TABLE "request_statuses" (
    "id" TEXT NOT NULL,
    "request_id" TEXT NOT NULL,
    "updated_by" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'Pending',
    "reason" TEXT,
    "previous_status" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    
    CONSTRAINT "request_statuses_pkey" PRIMARY KEY ("id")
);
```

## Fixes Applied

### 1. Updated `approveRequest()` Method

**Before (Problematic):**
```javascript
return await prisma.requests.update({
  where: { id: requestId },
  data: {
    status: 'Approved',
    admin_notes: note,        // ❌ Field doesn't exist
    approved_by: adminId,     // ❌ Field doesn't exist
    approved_at: new Date(),  // ❌ Field doesn't exist
    request_statuses: {
      create: {
        status: 'Approved',
        updated_by: adminId,
        previous_status: request.status,
        reason: note || 'Request approved by admin'
      }
    }
  },
  // ... includes
});
```

**After (Fixed):**
```javascript
return await prisma.requests.update({
  where: { id: requestId },
  data: {
    status: 'Approved',       // ✅ Only update existing field
    request_statuses: {
      create: {
        status: 'Approved',
        updated_by: adminId,    // ✅ Store admin ID in status table
        previous_status: request.status,
        reason: note || 'Request approved by admin' // ✅ Store note in reason field
      }
    }
  },
  // ... includes
});
```

### 2. Updated `rejectRequest()` Method

**Before (Problematic):**
```javascript
return await prisma.requests.update({
  where: { id: requestId },
  data: {
    status: 'Rejected',
    admin_notes: note,        // ❌ Field doesn't exist
    rejected_by: adminId,     // ❌ Field doesn't exist
    rejected_at: new Date(),  // ❌ Field doesn't exist
    request_statuses: {
      create: {
        status: 'Rejected',
        updated_by: adminId,
        previous_status: request.status,
        reason: note || 'Request rejected by admin'
      }
    }
  },
  // ... includes
});
```

**After (Fixed):**
```javascript
return await prisma.requests.update({
  where: { id: requestId },
  data: {
    status: 'Rejected',       // ✅ Only update existing field
    request_statuses: {
      create: {
        status: 'Rejected',
        updated_by: adminId,    // ✅ Store admin ID in status table
        previous_status: request.status,
        reason: note || 'Request rejected by admin' // ✅ Store note in reason field
      }
    }
  },
  // ... includes
});
```

## Data Storage Strategy

### Request Status Information

All approval/rejection information is now properly stored in the `request_statuses` table:

| Field | Purpose | Example |
|-------|---------|---------|
| `status` | New status | "Approved" or "Rejected" |
| `updated_by` | Admin who performed action | Admin user ID |
| `reason` | Admin note/reason | "Meets all requirements" |
| `previous_status` | Status before change | "Pending" |
| `created_at` | Timestamp of action | Auto-generated |

### Retrieving Admin Information

To get approval/rejection details for a request:

```javascript
// Get the latest status change
const latestStatus = await prisma.request_statuses.findFirst({
  where: { 
    request_id: requestId,
    status: { in: ['Approved', 'Rejected'] }
  },
  orderBy: { created_at: 'desc' },
  include: {
    updated_by_user: {
      select: {
        id: true,
        first_name: true,
        last_name: true,
        email: true
      }
    }
  }
});

// Access admin info and notes
const adminId = latestStatus.updated_by;
const adminNote = latestStatus.reason;
const actionDate = latestStatus.created_at;
const adminInfo = latestStatus.updated_by_user;
```

## Testing the Fix

### 1. Test Bulk Approve

```bash
curl -X POST http://localhost:5000/api/admin/requests/bulk-action \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "request_ids": ["valid_request_id_1", "valid_request_id_2"],
    "action": "approve",
    "note": "All requirements met"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Successfully approved 2 request(s)",
  "data": {
    "total": 2,
    "successful": [
      {
        "request_id": "valid_request_id_1",
        "action": "approve",
        "result": {
          "id": "valid_request_id_1",
          "status": "Approved",
          "request_statuses": [
            {
              "status": "Approved",
              "updated_by": "admin_id",
              "reason": "All requirements met",
              "created_at": "2024-01-20T10:30:00.000Z"
            }
          ]
        }
      }
    ],
    "failed": []
  }
}
```

### 2. Test Bulk Reject

```bash
curl -X POST http://localhost:5000/api/admin/requests/bulk-action \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "request_ids": ["valid_request_id_3"],
    "action": "reject",
    "note": "Insufficient documentation"
  }'
```

## Database Migration Considerations

### Option 1: Keep Current Schema (Recommended)
- ✅ Use `request_statuses` table for admin notes and tracking
- ✅ Maintains audit trail with timestamps
- ✅ Supports multiple status changes over time
- ✅ No schema changes required

### Option 2: Add Fields to Requests Table (Not Recommended)
If you really need these fields in the `requests` table, you would need to create a migration:

```sql
-- Migration to add admin fields (NOT RECOMMENDED)
ALTER TABLE "requests" 
ADD COLUMN "admin_notes" TEXT,
ADD COLUMN "approved_by" TEXT,
ADD COLUMN "approved_at" TIMESTAMP(3),
ADD COLUMN "rejected_by" TEXT,
ADD COLUMN "rejected_at" TIMESTAMP(3);

-- Add foreign key constraints
ALTER TABLE "requests" 
ADD CONSTRAINT "requests_approved_by_fkey" 
FOREIGN KEY ("approved_by") REFERENCES "users"("id");

ALTER TABLE "requests" 
ADD CONSTRAINT "requests_rejected_by_fkey" 
FOREIGN KEY ("rejected_by") REFERENCES "users"("id");
```

**Why Option 1 is Better:**
- Maintains proper audit trail
- Supports multiple status changes
- Follows existing database design patterns
- No breaking changes to existing code

## Files Modified

1. **`models/requestModel.js`**
   - Fixed `approveRequest()` method to only update existing fields
   - Fixed `rejectRequest()` method to only update existing fields
   - Removed references to non-existent database fields

2. **`services/requestService.js`**
   - Removed debugging code
   - No functional changes needed

## Impact of Fix

### ✅ **Now Working:**
1. **Bulk Approve**: Updates status and creates proper status history
2. **Bulk Reject**: Updates status and creates proper status history
3. **Admin Notes**: Stored in `request_statuses.reason` field
4. **Admin Tracking**: Admin ID stored in `request_statuses.updated_by` field
5. **Timestamps**: Automatic timestamps in `request_statuses.created_at`

### ✅ **Maintained Functionality:**
1. **Status History**: Complete audit trail maintained
2. **Admin Information**: Accessible through status table relationships
3. **Database Integrity**: No schema violations
4. **Existing APIs**: All other request operations continue to work

## Summary

The "Request not found" error was actually a database schema violation error. The fix involved:

1. **Identifying** that the `requests` table doesn't have admin-specific fields
2. **Utilizing** the existing `request_statuses` table for admin notes and tracking
3. **Removing** references to non-existent database fields
4. **Maintaining** full audit trail and admin information through proper relationships

The bulk request management system now works correctly with the existing database schema while maintaining all required functionality for admin approval and rejection workflows.
