-- CreateEnum
CREATE TYPE "form_field_type" AS ENUM ('TEXT', 'TEXTAREA', 'NUMBER', 'EMAIL', 'PASSWORD', 'DATE', 'TIME', 'DATETIME', 'CHECKBOX', 'RADIO', 'SELECT', 'MULTISELECT', 'FILE', 'PHONE', 'URL', 'COLOR', 'RANGE');

-- CreateTable
CREATE TABLE "subcategory_form_fields" (
    "id" TEXT NOT NULL,
    "subcategory_id" TEXT NOT NULL,
    "label_name" TEXT NOT NULL,
    "input_type" "form_field_type" NOT NULL DEFAULT 'TEXT',
    "label_subtitle" TEXT,
    "placeholder" TEXT,
    "is_required" BOOLEAN NOT NULL DEFAULT false,
    "options" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "default_value" TEXT,
    "validation_regex" TEXT,
    "min_value" INTEGER,
    "max_value" INTEGER,
    "min_length" INTEGER,
    "max_length" INTEGER,
    "sort_order" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "subcategory_form_fields_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "subcategory_form_fields_subcategory_id_idx" ON "subcategory_form_fields"("subcategory_id");

-- CreateIndex
CREATE INDEX "subcategory_form_fields_sort_order_idx" ON "subcategory_form_fields"("sort_order");

-- AddForeignKey
ALTER TABLE "subcategory_form_fields" ADD CONSTRAINT "subcategory_form_fields_subcategory_id_fkey" FOREIGN KEY ("subcategory_id") REFERENCES "sub_categories"("id") ON DELETE CASCADE ON UPDATE CASCADE;
