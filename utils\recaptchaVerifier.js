/**
 * reCAPTCHA verification utility
 */
const axios = require('axios');

class RecaptchaVerifier {
  /**
   * Verify reCAPTCHA token with Google's API
   * @param {string} token - The reCAPTCHA token from the client
   * @param {string} remoteip - The user's IP address (optional)
   * @returns {Promise<Object>} Verification result
   */
  static async verifyToken(token, remoteip = null) {
    try {
      const secretKey = process.env.RECAPTCHA_SECRET_KEY;

      if (!secretKey) {
        console.error('reCAPTCHA secret key not configured');
        return {
          success: false,
          error: 'reCAPTCHA secret key not configured'
        };
      }

      if (!token) {
        return {
          success: false,
          error: 'reCAPTCHA token is required'
        };
      }

      // Prepare the request data
      const requestData = {
        secret: secretKey,
        response: token
      };

      // Add remote IP if provided
      if (remoteip) {
        requestData.remoteip = remoteip;
      }

      // Make request to Google's reCAPTCHA verification API
      console.log('Making reCAPTCHA verification request to Google...');
      const response = await axios.post(
        'https://www.google.com/recaptcha/api/siteverify',
        new URLSearchParams(requestData),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          timeout: 15000 // 15 seconds timeout (increased for server environments)
        }
      );
      console.log('reCAPTCHA verification response received:', response.status);

      const result = response.data;

      if (result.success) {
        return {
          success: true,
          score: result.score || null, // For reCAPTCHA v3
          action: result.action || null, // For reCAPTCHA v3
          challenge_ts: result.challenge_ts,
          hostname: result.hostname
        };
      } else {
        return {
          success: false,
          error: 'reCAPTCHA verification failed',
          errorCodes: result['error-codes'] || []
        };
      }
    } catch (error) {
      console.error('reCAPTCHA verification error:', {
        message: error.message,
        code: error.code,
        response: error.response?.data,
        status: error.response?.status
      });

      if (error.code === 'ECONNABORTED') {
        return {
          success: false,
          error: 'reCAPTCHA verification timeout'
        };
      }

      if (error.code === 'ENOTFOUND') {
        return {
          success: false,
          error: 'Cannot reach reCAPTCHA service - DNS resolution failed'
        };
      }

      if (error.code === 'ECONNREFUSED') {
        return {
          success: false,
          error: 'Cannot reach reCAPTCHA service - connection refused'
        };
      }

      return {
        success: false,
        error: 'reCAPTCHA verification service unavailable'
      };
    }
  }

  /**
   * Verify reCAPTCHA token with minimum score requirement (for reCAPTCHA v3)
   * @param {string} token - The reCAPTCHA token from the client
   * @param {number} minScore - Minimum score required (0.0 to 1.0)
   * @param {string} expectedAction - Expected action name (optional)
   * @param {string} remoteip - The user's IP address (optional)
   * @returns {Promise<Object>} Verification result
   */
  static async verifyTokenWithScore(token, minScore = 0.5, expectedAction = null, remoteip = null) {
    const result = await this.verifyToken(token, remoteip);

    if (!result.success) {
      return result;
    }

    // Check score if provided (reCAPTCHA v3)
    if (result.score !== null && result.score < minScore) {
      return {
        success: false,
        error: `reCAPTCHA score too low: ${result.score} (minimum: ${minScore})`,
        score: result.score
      };
    }

    // Check action if provided (reCAPTCHA v3)
    if (expectedAction && result.action !== expectedAction) {
      return {
        success: false,
        error: `reCAPTCHA action mismatch: expected '${expectedAction}', got '${result.action}'`,
        action: result.action
      };
    }

    return result;
  }

  /**
   * Get user's IP address from request
   * @param {Object} req - Express request object
   * @returns {string} User's IP address
   */
  static getUserIP(req) {
    return req.ip ||
           req.connection.remoteAddress ||
           req.socket.remoteAddress ||
           (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
           req.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
           req.headers['x-real-ip'] ||
           'unknown';
  }
}

module.exports = RecaptchaVerifier;
