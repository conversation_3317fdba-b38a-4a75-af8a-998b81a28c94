MERGE REQUEST API DOCUMENTATION
===============================

Base URL: /api/admin/requests
Authentication: <PERSON><PERSON> (Admin role required)

===============================

1. POST /requests/merge
   Description: Create a new request by merging multiple existing requests (Admin only)

   Endpoint: POST /api/admin/requests/merge

   Headers:
   - Authorization: Bearer <admin_token>
   - Content-Type: application/json

   Payload:
   {
     "title": "New Merged Request Title",                    // Required: 3-200 characters
     "short_description": "Brief description of merged request", // Optional: max 500 characters
     "description": "Detailed description of the merged request", // Optional: max 2000 characters
     "category_id": "uuid",                                 // Required: Valid category UUID
     "sub_category_id": "uuid",                            // Required: Valid sub-category UUID
     "quantity": 5,                                        // Optional: positive integer, default 1
     "budget_min": 100.00,                                 // Optional: positive number
     "budget_max": 1000.00,                               // Optional: positive number >= budget_min
     "deadline": "2024-02-01T00:00:00.000Z",              // Optional: ISO8601 date
     "urgency": "High",                                    // Optional: Low|Normal|High|Urgent
     "request_type": "Service",                            // Optional: General|Service|Product|Consultation
     "location": "New York, USA",                          // Optional: max 200 characters
     "additional_info": "Additional information about the merged request", // Optional: max 1000 characters
     "custom_fields": {                                    // Optional: JSON object
       "special_requirements": "Custom requirement"
     },
     "request_ids_to_merge": [                            // Required: Array of request UUIDs to merge
       "uuid1",
       "uuid2",
       "uuid3"
     ]
   }
   
   Validation Rules:
   - title: Required, 3-200 characters
   - category_id: Required, valid UUID
   - sub_category_id: Required, valid UUID
   - quantity: Optional, positive integer
   - budget_min/budget_max: Optional, positive numbers, max >= min
   - deadline: Optional, valid ISO8601 date
   - urgency: Optional, must be Low|Normal|High|Urgent
   - request_type: Optional, must be General|Service|Product|Consultation
   - request_ids_to_merge: Required, non-empty array of valid UUIDs
   - No duplicate IDs allowed in request_ids_to_merge
   - All requests must exist and not be already merged
   
   Response (Success):
   {
     "success": true,
     "message": "Successfully created new request by merging 3 request(s)",
     "data": {
       "id": "new-merged-request-uuid",
       "request_code": "REQ-*************",
       "title": "New Merged Request Title",
       "short_description": "Brief description of merged request",
       "description": "Detailed description of the merged request",
       "quantity": 1,
       "budget_min": 100.00,
       "budget_max": 500.00,
       "deadline": "2024-02-01T00:00:00.000Z",
       "urgency": "High",
       "status": "Merged",
       "buyer": {
         "id": "admin-uuid",
         "first_name": "Admin",
         "last_name": "User",
         "email": "<EMAIL>",
         "profile_picture_url": "/uploads/profiles/admin.jpg"
       },
       "category": {
         "id": "category-uuid",
         "name": "Technology",
         "description": "Technology services"
       },
       "sub_category": {
         "id": "subcategory-uuid",
         "name": "Web Development",
         "description": "Web development services"
       },
       "merged_requests": [
         {
           "id": "merge-record-uuid",
           "request_id": "new-merged-request-uuid",
           "merged_item_id": "original-request-uuid-1",
           "merged_by": "admin-uuid",
           "created_at": "2024-01-15T10:30:00.000Z",
           "merged_item": {
             "id": "original-request-uuid-1",
             "request_code": "REQ-*************",
             "title": "Original Request 1",
             "short_description": "First original request that was merged",
             "description": "Details of first original request",
             "quantity": 2,
             "budget_min": 50.00,
             "budget_max": 200.00,
             "deadline": "2024-01-25T00:00:00.000Z",
             "urgency": "Normal",
             "status": "Merged",
             "created_at": "2024-01-10T08:00:00.000Z",
             "buyer": {
               "id": "buyer2-uuid",
               "first_name": "Jane",
               "last_name": "Smith",
               "email": "<EMAIL>",
               "profile_picture_url": "/uploads/profiles/image2.jpg"
             },
             "category": {
               "id": "category-uuid",
               "name": "Technology",
               "description": "Technology services"
             },
             "sub_category": {
               "id": "subcategory-uuid",
               "name": "Web Development",
               "description": "Web development services"
             }
           },
           "merged_by_user": {
             "id": "admin-uuid",
             "first_name": "Admin",
             "last_name": "User",
             "email": "<EMAIL>"
           }
         },
         {
           "id": "merge-record-uuid-2",
           "request_id": "main-request-uuid",
           "merged_item_id": "child-request-uuid-2",
           "merged_by": "admin-uuid",
           "created_at": "2024-01-15T10:30:00.000Z",
           "merged_item": {
             "id": "child-request-uuid-2",
             "request_code": "REQ-*************",
             "title": "Child Request 2",
             "short_description": "Second merged request",
             "description": "Details of second merged request",
             "quantity": 1,
             "budget_min": 75.00,
             "budget_max": 300.00,
             "deadline": "2024-01-30T00:00:00.000Z",
             "urgency": "Low",
             "status": "Merged",
             "created_at": "2024-01-12T14:30:00.000Z",
             "buyer": {
               "id": "buyer3-uuid",
               "first_name": "Bob",
               "last_name": "Johnson",
               "email": "<EMAIL>",
               "profile_picture_url": "/uploads/profiles/image3.jpg"
             },
             "category": {
               "id": "category-uuid",
               "name": "Technology",
               "description": "Technology services"
             },
             "sub_category": {
               "id": "subcategory-uuid",
               "name": "Web Development",
               "description": "Web development services"
             }
           },
           "merged_by_user": {
             "id": "admin-uuid",
             "first_name": "Admin",
             "last_name": "User",
             "email": "<EMAIL>"
           }
         }
       ],
       "created_at": "2024-01-08T12:00:00.000Z",
       "updated_at": "2024-01-15T10:30:00.000Z"
     }
   }

===============================

2. GET /requests/:id (Updated to include merged requests)
   Description: Get request details including merged child requests
   
   Endpoint: GET /api/admin/requests/{request_id}
   
   Headers:
   - Authorization: Bearer <admin_token>
   
   URL Parameters:
   - request_id: UUID (required)
   
   Payload: None
   
   Response (Success - Main Request with Merged Children):
   {
     "success": true,
     "message": "Request retrieved successfully",
     "data": {
       "id": "main-request-uuid",
       "request_code": "REQ-*************",
       "title": "Main Request Title",
       "status": "Merged",
       // ... other request fields ...
       "merged_requests": [
         // Array of merged child requests as shown above
       ],
       // ... other request relations (buyer, category, etc.) ...
     }
   }
   
   Response (Success - Regular Request):
   {
     "success": true,
     "message": "Request retrieved successfully",
     "data": {
       "id": "request-uuid",
       "request_code": "REQ-*************",
       "title": "Regular Request",
       "status": "Pending",
       // ... other request fields ...
       "merged_requests": [],  // Empty array for non-merged requests
       // ... other request relations ...
     }
   }

===============================

ERROR RESPONSES:

Validation Error (400):
{
  "success": false,
  "message": "Validation failed",
  "error": {
    "main_request_id": ["Main request ID is required"],
    "request_ids_to_merge": ["Request IDs to merge must be a non-empty array"]
  }
}

Request Not Found (404):
{
  "success": false,
  "message": "Main request not found",
  "error": {
    "general": ["Main request not found"]
  }
}

Business Logic Error (400):
{
  "success": false,
  "message": "Request uuid1 is already merged",
  "error": {
    "general": ["Request uuid1 is already merged"]
  }
}

Unauthorized (401):
{
  "message": "Access Denied"
}

Forbidden (403):
{
  "message": "Access denied. Insufficient permissions."
}

Server Error (500):
{
  "success": false,
  "message": "Failed to merge requests: Database connection error",
  "error": {
    "general": ["Failed to merge requests: Database connection error"]
  }
}

===============================

BUSINESS RULES:

1. Only admins can create merged requests
2. All original requests must exist and not be deleted
3. Original requests cannot already be merged
4. No duplicate request IDs allowed in the merge list
5. Admin must provide all required fields for the new request (title, category, sub_category)
6. When requests are merged:
   - A completely new request is created with admin as the buyer
   - Original requests status changes to "Merged"
   - New request gets "Merged" status
   - Merge records are created linking new request to original requests
   - Status update records are created for all requests for tracking
7. The new merged request contains all original requests in the merged_requests array
8. Admin becomes the "buyer" of the newly created merged request
9. Original requests retain their original buyer information but are marked as merged
