const csv = require('csv-parser');
const { Readable } = require('stream');
const XLSX = require('xlsx');

class CSVProcessor {
  /**
   * Parse CSV buffer to array of objects
   * @param {<PERSON>uffer} buffer - CSV file buffer
   * @returns {Promise<Array>} Parsed data array
   */
  static async parseCSV(buffer) {
    return new Promise((resolve, reject) => {
      const results = [];
      const stream = Readable.from(buffer);

      stream
        .pipe(csv())
        .on('data', (data) => results.push(data))
        .on('end', () => resolve(results))
        .on('error', (error) => reject(error));
    });
  }

  /**
   * Detect CSV format (flat or JSON)
   * @param {Array} data - Parsed data array
   * @returns {string} Format type: 'flat' or 'json'
   */
  static detectCSVFormat(data) {

    if (data.length === 0) return 'json';

    const firstRow = data[0];
    // Check if it has flat format columns
    if (firstRow.hasOwnProperty('label_name') && firstRow.hasOwnProperty('input_type')) {
      return 'flat';
    }

    return 'flat'; // Default to flat format
  }

  /**
   * Validate subcategory bulk upload data
   * @param {Array} data - Parsed data array
   * @returns {Object} Validation result
   */
  static validateSubcategoryData(data) {
    const format = this.detectCSVFormat(data);

    if (format === 'flat') {
      return this.validateFlatFormatData(data);
    } else {
      return this.validateJSONFormatData(data);
    }
  }

  /**
   * Validate flat format CSV data
   * @param {Array} data - Parsed data array
   * @returns {Object} Validation result
   */
  static validateFlatFormatData(data) {
    const errors = [];
    const validData = [];
    const validInputTypes = [
      'text', 'textarea', 'number', 'email', 'password', 'date', 'time',
      'datetime', 'checkbox', 'radio', 'select', 'multiselect', 'file',
      'phone', 'url', 'color', 'range'
    ];

    // Group data by subcategory
    const subcategories = this.groupFlatDataBySubcategory(data);

    Object.entries(subcategories).forEach(([subcategoryTitle, subcategoryData]) => {
      const subcategoryRow = subcategoryData.subcategory;
      const formFields = subcategoryData.formFields;
      const rowNumber = subcategoryData.rowIndex + 2;

      const rowErrors = [];

      // Validate required subcategory fields (8 fields)
      if (!subcategoryRow.category_title || subcategoryRow.category_title.trim() === '') {
        rowErrors.push(`Row ${rowNumber}: category_title is required for subcategory`);
      }
      if (!subcategoryRow.subcategory_title || subcategoryRow.subcategory_title.trim() === '') {
        rowErrors.push(`Row ${rowNumber}: subcategory_title is required for subcategory`);
      }

      // Validate form fields (12 fields)
      formFields.forEach((field) => {
        const fieldRowNumber = field.rowIndex + 2;

        if (!field.label_name || field.label_name.trim() === '') {
          rowErrors.push(`Row ${fieldRowNumber}: label_name is required for form field`);
        }

        if (!field.input_type || field.input_type.trim() === '') {
          rowErrors.push(`Row ${fieldRowNumber}: input_type is required for form field`);
        } else if (!validInputTypes.includes(field.input_type.toLowerCase())) {
          rowErrors.push(`Row ${fieldRowNumber}: invalid input_type '${field.input_type}'`);
        }

        // Validate options for select/radio/checkbox types
        const inputType = field.input_type.toLowerCase();
        if (['select', 'radio', 'checkbox', 'multiselect'].includes(inputType)) {
          if (!field.options || field.options.trim() === '') {
            rowErrors.push(`Row ${fieldRowNumber}: options are required for ${field.input_type} type`);
          }
        }
      });

      if (rowErrors.length > 0) {
        errors.push(...rowErrors);
      } else {
        // Include the subcategory row and all its form field rows in validData
        validData.push(subcategoryRow);
        formFields.forEach(field => {
          validData.push(field);
        });
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      validData,
      totalRows: data.length,
      validRows: validData.length
    };
  }

  /**
   * Validate JSON format CSV data (legacy)
   * @param {Array} data - Parsed data array
   * @returns {Object} Validation result
   */
  static validateJSONFormatData(data) {
    const errors = [];
    const validData = [];
    const requiredFields = ['category_title', 'subcategory_title'];
    const validInputTypes = [
      'TEXT', 'TEXTAREA', 'NUMBER', 'EMAIL', 'PASSWORD', 'DATE', 'TIME',
      'DATETIME', 'CHECKBOX', 'RADIO', 'SELECT', 'MULTISELECT', 'FILE',
      'PHONE', 'URL', 'COLOR', 'RANGE'
    ];

    data.forEach((row, index) => {
      const rowErrors = [];
      const rowNumber = index + 2; // +2 because index starts at 0 and we skip header

      // Check required fields
      requiredFields.forEach(field => {
        if (!row[field] || row[field].toString().trim() === '') {
          rowErrors.push(`Row ${rowNumber}: ${field} is required`);
        }
      });

      // Validate form fields if present
      if (row.form_fields && row.form_fields.trim() !== '') {
        try {
          const formFields = JSON.parse(row.form_fields);
          if (Array.isArray(formFields)) {
            formFields.forEach((field, fieldIndex) => {
              if (!field.label_name) {
                rowErrors.push(`Row ${rowNumber}, Form Field ${fieldIndex + 1}: label_name is required`);
              }
              if (!field.input_type) {
                rowErrors.push(`Row ${rowNumber}, Form Field ${fieldIndex + 1}: input_type is required`);
              } else if (!validInputTypes.includes(field.input_type)) {
                rowErrors.push(`Row ${rowNumber}, Form Field ${fieldIndex + 1}: invalid input_type '${field.input_type}'`);
              }

              // Validate options for select/radio/checkbox types
              if (['SELECT', 'RADIO', 'CHECKBOX', 'MULTISELECT'].includes(field.input_type)) {
                if (!field.options || !Array.isArray(field.options) || field.options.length === 0) {
                  rowErrors.push(`Row ${rowNumber}, Form Field ${fieldIndex + 1}: options array is required for ${field.input_type} type`);
                }
              }
            });
          } else {
            rowErrors.push(`Row ${rowNumber}: form_fields must be a valid JSON array`);
          }
        } catch (e) {
          rowErrors.push(`Row ${rowNumber}: form_fields contains invalid JSON`);
        }
      }

      if (rowErrors.length > 0) {
        errors.push(...rowErrors);
      } else {
        validData.push(row);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      validData,
      totalRows: data.length,
      validRows: validData.length
    };
  }

  /**
   * Group flat format data by subcategory
   * @param {Array} data - Parsed data array
   * @returns {Object} Grouped data
   */
  static groupFlatDataBySubcategory(data) {
    const subcategories = {};
    let currentSubcategory = null;

    data.forEach((row, index) => {
      // If category_title != '' → This is a subcategory row
      if (row.category_title && row.category_title.trim() !== '') {
        // Extract subcategory data (8 fields)
        const subcategoryData = {
          category_title: row.category_title,
          subcategory_title: row.subcategory_title,
          subcategory_description: row.subcategory_description,
          subcategory_color: row.subcategory_color,
          is_featured: row.is_featured,
          seo_title: row.seo_title,
          seo_description: row.seo_description,
          seo_keywords: row.seo_keywords,
          rowIndex: index
        };

        currentSubcategory = row.subcategory_title.trim();
        subcategories[currentSubcategory] = {
          subcategory: subcategoryData,
          formFields: [],
          rowIndex: index
        };
      }
      // If category_title == '' → This is a form field row
      else if (row.category_title === '' || !row.category_title || row.category_title.trim() === '') {
        if (row.label_name && row.label_name.trim() && currentSubcategory) {
          // Extract form field data (12 fields)
          const formFieldData = {
            label_name: row.label_name,
            input_type: row.input_type,
            label_subtitle: row.label_subtitle,
            placeholder: row.placeholder,
            is_required: row.is_required,
            options: row.options,
            default_value: row.default_value,
            validation_regex: row.validation_regex,
            min_value: row.min_value,
            max_value: row.max_value,
            min_length: row.min_length,
            max_length: row.max_length,
            rowIndex: index
          };

          subcategories[currentSubcategory].formFields.push(formFieldData);
        }
      }
    });

    return subcategories;
  }

  /**
   * Process and transform subcategory data for database insertion
   * @param {Array} data - Validated data array
   * @returns {Array} Processed data ready for database
   */
  static processSubcategoryData(data) {
    // Detect format first
    const format = this.detectCSVFormat(data);

    if (format === 'flat') {
      return this.processFlatFormatData(data);
    } else {
      return this.processJSONFormatData(data);
    }
  }

  /**
   * Process JSON format data for database insertion (legacy support)
   * @param {Array} data - Validated data array
   * @returns {Array} Processed data ready for database
   */
  static processJSONFormatData(data) {
    return data.map(row => {
      const processedRow = {
        category_title: row.category_title.trim(),
        subcategory: {
          title: row.subcategory_title.trim(),
          description: row.subcategory_description?.trim() || null,
          color: row.subcategory_color?.trim() || null,
          is_featured: row.is_featured === 'true' || row.is_featured === '1' || row.is_featured === 1 || row.is_featured === true,
          seo_title: row.seo_title?.trim() || null,
          seo_description: row.seo_description?.trim() || null,
          seo_keywords: row.seo_keywords?.trim() || null
        },
        form_fields: []
      };

      // Process form fields if present
      if (row.form_fields && row.form_fields.trim() !== '') {
        try {
          const formFields = JSON.parse(row.form_fields);
          if (Array.isArray(formFields)) {
            processedRow.form_fields = formFields.map((field, index) => ({
              label_name: field.label_name,
              input_type: field.input_type,
              label_subtitle: field.label_subtitle || null,
              placeholder: field.placeholder || null,
              is_required: field.is_required || false,
              options: field.options || [],
              default_value: field.default_value || null,
              validation_regex: field.validation_regex || null,
              min_value: field.min_value || null,
              max_value: field.max_value || null,
              min_length: field.min_length || null,
              max_length: field.max_length || null,
              sort_order: field.sort_order || index + 1
            }));
          }
        } catch (e) {
          // If JSON parsing fails, skip form fields
          console.warn('Failed to parse form_fields JSON:', e.message);
        }
      }

      return processedRow;
    });
  }

  /**
   * Process flat format data for database insertion
   * @param {Array} data - Validated data array
   * @returns {Array} Processed data ready for database
   */
  static processFlatFormatData(data) {
    const subcategories = this.groupFlatDataBySubcategory(data);

    return Object.values(subcategories).map(subcategoryData => {
      const subcategoryRow = subcategoryData.subcategory;
      const formFields = subcategoryData.formFields;

      const processedRow = {
        category_title: subcategoryRow.category_title.trim(),
        subcategory: {
          title: subcategoryRow.subcategory_title.trim(),
          description: subcategoryRow.subcategory_description?.trim() || null,
          color: subcategoryRow.subcategory_color?.trim() || null,
          is_featured: subcategoryRow.is_featured === 'true' || subcategoryRow.is_featured === '1' || subcategoryRow.is_featured === 1 || subcategoryRow.is_featured === true,
          seo_title: subcategoryRow.seo_title?.trim() || null,
          seo_description: subcategoryRow.seo_description?.trim() || null,
          seo_keywords: subcategoryRow.seo_keywords?.trim() || null
        },
        form_fields: []
      };

      // Process form fields
      processedRow.form_fields = formFields.map((field, index) => ({
        label_name: field.label_name.trim(),
        input_type: field.input_type.toUpperCase(),
        label_subtitle: field.label_subtitle?.trim() || null,
        placeholder: field.placeholder?.trim() || null,
        is_required: field.is_required === 'true' || field.is_required === '1' || field.is_required === 1 || field.is_required === true,
        options: field.options ? field.options.split(',').map(opt => opt.trim()).filter(opt => opt) : [],
        default_value: field.default_value?.trim() || null,
        validation_regex: field.validation_regex?.trim() || null,
        min_value: field.min_value ? parseInt(field.min_value) : null,
        max_value: field.max_value ? parseInt(field.max_value) : null,
        min_length: field.min_length ? parseInt(field.min_length) : null,
        max_length: field.max_length ? parseInt(field.max_length) : null,
        sort_order: index + 1
      }));

      return processedRow;
    });
  }

  /**
   * Validate subcategory data without requiring category_title
   * @param {Array} data - Parsed data array
   * @returns {Object} Validation result
   */
  static validateSubcategoryDataWithoutCategory(data) {
    const errors = [];
    const validData = [];

    data.forEach((row, index) => {
      const rowErrors = [];

      // Check if this is a subcategory row (has subcategory_title)
      if (row.subcategory_title && row.subcategory_title.trim() !== '') {
        // Validate required subcategory fields
        if (!row.subcategory_title || row.subcategory_title.trim() === '') {
          rowErrors.push('subcategory_title is required');
        }
        if (!row.subcategory_description || row.subcategory_description.trim() === '') {
          rowErrors.push('subcategory_description is required');
        }
      }
      // Check if this is a form field row (has label_name)
      else if (row.label_name && row.label_name.trim() !== '') {
        // Validate required form field fields
        if (!row.label_name || row.label_name.trim() === '') {
          rowErrors.push('label_name is required for form fields');
        }
        if (!row.input_type || row.input_type.trim() === '') {
          rowErrors.push('input_type is required for form fields');
        }

        // Validate input_type values
        const validInputTypes = ['TEXT', 'TEXTAREA', 'NUMBER', 'EMAIL', 'PASSWORD', 'SELECT', 'CHECKBOX', 'RADIO', 'DATE', 'TIME', 'DATETIME', 'FILE', 'URL', 'TEL'];
        if (row.input_type && !validInputTypes.includes(row.input_type.toUpperCase())) {
          rowErrors.push(`Invalid input_type: ${row.input_type}. Valid types: ${validInputTypes.join(', ')}`);
        }
      }
      // Skip empty rows
      else if (this.isEmptyRow(row)) {
        return; // Skip empty rows
      }
      else {
        rowErrors.push('Row must contain either subcategory_title or label_name');
      }

      if (rowErrors.length > 0) {
        errors.push(`Row ${index + 1}: ${rowErrors.join(', ')}`);
      } else {
        validData.push(row);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      validData,
      totalRows: data.length,
      validRows: validData.length
    };
  }

  /**
   * Process subcategory data without category_title requirement
   * @param {Array} data - Validated data array
   * @param {string} categoryTitle - Category title to use
   * @returns {Array} Processed data ready for database
   */
  static processSubcategoryDataWithoutCategory(data, categoryTitle) {
    const subcategories = this.groupFlatDataBySubcategoryWithoutCategory(data);

    return Object.values(subcategories).map(subcategoryData => {
      const subcategoryRow = subcategoryData.subcategory;
      const formFields = subcategoryData.formFields;

      const processedRow = {
        category_title: categoryTitle, // Use provided category title
        subcategory: {
          title: subcategoryRow.subcategory_title.trim(),
          description: subcategoryRow.subcategory_description?.trim() || null,
          color: subcategoryRow.subcategory_color?.trim() || null,
          is_featured: subcategoryRow.is_featured === 'true' || subcategoryRow.is_featured === '1' || subcategoryRow.is_featured === 1 || subcategoryRow.is_featured === true,
          seo_title: subcategoryRow.seo_title?.trim() || null,
          seo_description: subcategoryRow.seo_description?.trim() || null,
          seo_keywords: subcategoryRow.seo_keywords?.trim() || null
        },
        form_fields: []
      };

      // Process form fields
      processedRow.form_fields = formFields.map((field, index) => ({
        label_name: field.label_name.trim(),
        input_type: field.input_type.toUpperCase(),
        label_subtitle: field.label_subtitle?.trim() || null,
        placeholder: field.placeholder?.trim() || null,
        is_required: field.is_required === 'true' || field.is_required === '1' || field.is_required === 1 || field.is_required === true,
        options: field.options ? field.options.split(',').map(opt => opt.trim()).filter(opt => opt) : [],
        default_value: field.default_value?.trim() || null,
        validation_regex: field.validation_regex?.trim() || null,
        min_value: field.min_value ? parseInt(field.min_value) : null,
        max_value: field.max_value ? parseInt(field.max_value) : null,
        min_length: field.min_length ? parseInt(field.min_length) : null,
        max_length: field.max_length ? parseInt(field.max_length) : null,
        sort_order: index + 1
      }));

      return processedRow;
    });
  }

  /**
   * Group flat format data by subcategory without requiring category_title
   * @param {Array} data - Parsed data array
   * @returns {Object} Grouped data
   */
  static groupFlatDataBySubcategoryWithoutCategory(data) {
    const subcategories = {};
    let currentSubcategory = null;

    data.forEach((row, index) => {
      // If subcategory_title != '' → This is a subcategory row
      if (row.subcategory_title && row.subcategory_title.trim() !== '') {
        // Extract subcategory data (without category_title)
        const subcategoryData = {
          subcategory_title: row.subcategory_title,
          subcategory_description: row.subcategory_description,
          subcategory_color: row.subcategory_color,
          is_featured: row.is_featured,
          seo_title: row.seo_title,
          seo_description: row.seo_description,
          seo_keywords: row.seo_keywords,
          rowIndex: index
        };

        currentSubcategory = row.subcategory_title.trim();
        subcategories[currentSubcategory] = {
          subcategory: subcategoryData,
          formFields: [],
          rowIndex: index
        };
      }
      // If subcategory_title == '' and label_name != '' → This is a form field row
      else if ((!row.subcategory_title || row.subcategory_title.trim() === '') &&
               row.label_name && row.label_name.trim() !== '' && currentSubcategory) {
        // Extract form field data
        const formFieldData = {
          label_name: row.label_name,
          input_type: row.input_type,
          label_subtitle: row.label_subtitle,
          placeholder: row.placeholder,
          is_required: row.is_required,
          options: row.options,
          default_value: row.default_value,
          validation_regex: row.validation_regex,
          min_value: row.min_value,
          max_value: row.max_value,
          min_length: row.min_length,
          max_length: row.max_length,
          rowIndex: index
        };

        subcategories[currentSubcategory].formFields.push(formFieldData);
      }
    });

    return subcategories;
  }

  /**
   * Generate CSV template for subcategory bulk upload
   * @returns {string} CSV template content
   */
  static generateCSVTemplate() {
    const headers = [
      'category_title',
      'subcategory_title',
      'subcategory_description',
      'subcategory_color',
      'is_featured',
      'seo_title',
      'seo_description',
      'seo_keywords',
      'label_name',
      'input_type',
      'label_subtitle',
      'placeholder',
      'is_required',
      'options',
      'default_value',
      'validation_regex',
      'min_value',
      'max_value',
      'min_length',
      'max_length'
    ];

const sampleData = [
  {
    subcategory_title: 'Web Development',
    subcategory_description: 'Professional web development services',
    subcategory_color: '#3498db',
    is_featured: 'true',
    seo_title: 'Web Development Services',
    seo_description: 'Professional web development services for your business',
    seo_keywords: 'web development website programming',
    label_name: '',
    input_type: '',
    label_subtitle: '',
    placeholder: '',
    is_required: '',
    options: '',
    default_value: '',
    validation_regex: '',
    min_value: '',
    max_value: '',
    min_length: '',
    max_length: ''
  },
  // Form field row
  {
    subcategory_title: '',
    subcategory_description: '',
    subcategory_color: '',
    is_featured: '',
    seo_title: '',
    seo_description: '',
    seo_keywords: '',
    label_name: 'Preferred Language',
    input_type: 'text',
    label_subtitle: '',
    placeholder: 'Enter preferred programming language',
    is_required: 'true',
    options: '',
    default_value: '',
    validation_regex: '',
    min_value: '',
    max_value: '',
    min_length: '2',
    max_length: '30'
  },
  // Form field row
  {
    subcategory_title: '',
    subcategory_description: '',
    subcategory_color: '',
    is_featured: '',
    seo_title: '',
    seo_description: '',
    seo_keywords: '',
    label_name: 'Experience Level',
    input_type: 'select',
    label_subtitle: '',
    placeholder: '',
    is_required: 'true',
    options: 'Beginner,Intermediate,Expert',
    default_value: 'Intermediate',
    validation_regex: '',
    min_value: '',
    max_value: '',
    min_length: '',
    max_length: ''
  }
];


    // Generate CSV content
    let csv = headers.join(',') + '\n';
    sampleData.forEach(row => {
      const values = headers.map(header => {
        const value = row[header] || '';
        // Escape commas and quotes in CSV
        if (value.includes(',') || value.includes('"') || value.includes('\n')) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      });
      csv += values.join(',') + '\n';
    });

    return csv;
  }

  /**
   * Generate Excel template for subcategory bulk upload
   * @returns {Buffer} Excel file buffer
   */
  static generateExcelTemplate() {
    const headers = [
      'category_title',
      'subcategory_title',
      'subcategory_description',
      'subcategory_color',
      'is_featured',
      'seo_title',
      'seo_description',
      'seo_keywords',
      'form_fields'
    ];

    const sampleData = [
      [
        'Technology',
        'Web Development',
        'Professional web development services',
        '#3498db',
        'true',
        'Web Development Services',
        'Professional web development services for your business',
        'web development, website, programming',
        '[{"label_name":"Project Type","input_type":"SELECT","label_subtitle":"Select the type of website you need","is_required":true,"options":["E-commerce","Blog","Corporate","Portfolio"],"sort_order":1},{"label_name":"Project Description","input_type":"TEXTAREA","label_subtitle":"Describe your project requirements","placeholder":"Enter detailed project description...","is_required":true,"min_length":50,"max_length":1000,"sort_order":2}]'
      ],
      [
        'Technology',
        'Mobile App Development',
        'iOS and Android app development',
        '#e74c3c',
        'false',
        'Mobile App Development',
        'Professional mobile app development for iOS and Android',
        'mobile app, iOS, Android, development',
        '[{"label_name":"Platform","input_type":"CHECKBOX","label_subtitle":"Select target platforms","is_required":true,"options":["iOS","Android","Cross-platform"],"sort_order":1}]'
      ]
    ];

    const worksheet = XLSX.utils.aoa_to_sheet([headers, ...sampleData]);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Subcategories');

    return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
  }

  /**
   * Parse Excel buffer to array of objects
   * @param {Buffer} buffer - Excel file buffer
   * @returns {Array} Parsed data array
   */
  static parseExcel(buffer) {
    const workbook = XLSX.read(buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];

    return XLSX.utils.sheet_to_json(worksheet);
  }


}

module.exports = CSVProcessor;
