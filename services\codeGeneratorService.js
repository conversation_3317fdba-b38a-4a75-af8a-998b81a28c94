/**
 * Service for generating and managing request and offer codes
 */
const { generateRequestCode, generateOfferCode } = require('../utils/idGenerator');

class CodeGeneratorService {
  /**
   * Generate a code for a request
   * @param {Object} request - The request object
   * @returns {string} The generated code
   */
  static generateRequestCode(request) {
    try {
      return generateRequestCode(request);
    } catch (error) {
      console.error('Error generating request code:', error);
      // Fallback to a simpler code format if there's an error
      const now = new Date();
      const timestamp = now.getTime().toString().slice(-8);
      return `REQ-${timestamp}`;
    }
  }

  /**
   * Generate a code for an offer
   * @param {Object} offer - The offer object
   * @returns {string} The generated code
   */
  static generateOfferCode(offer) {
    try {
      return generateOfferCode(offer);
    } catch (error) {
      console.error('Error generating offer code:', error);
      // Fallback to a simpler code format if there's an error
      const now = new Date();
      const timestamp = now.getTime().toString().slice(-8);
      return `OFF-${timestamp}`;
    }
  }

  /**
   * Add a code to a request object
   * @param {Object} request - The request object
   * @returns {Object} The request object with code
   */
  static addCodeToRequest(request) {
    if (!request) return null;

    const code = this.generateRequestCode(request);
    return {
      ...request,
      request_code: code
    };
  }

  /**
   * Add a code to an offer object
   * @param {Object} offer - The offer object
   * @returns {Object} The offer object with code
   */
  static addCodeToOffer(offer) {
    if (!offer) return null;

    const code = this.generateOfferCode(offer);
    return {
      ...offer,
      offer_code: code
    };
  }

  /**
   * Add codes to a list of requests
   * @param {Array} requests - Array of request objects
   * @returns {Array} Array of request objects with codes
   */
  static addCodesToRequests(requests) {
    if (!requests || !Array.isArray(requests)) return [];

    return requests.map(request => this.addCodeToRequest(request));
  }

  /**
   * Add codes to a list of offers
   * @param {Array} offers - Array of offer objects
   * @returns {Array} Array of offer objects with codes
   */
  static addCodesToOffers(offers) {
    if (!offers || !Array.isArray(offers)) return [];

    return offers.map(offer => this.addCodeToOffer(offer));
  }
}

module.exports = CodeGeneratorService;
