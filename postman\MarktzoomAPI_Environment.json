{"id": "marktzoom-api-environment", "name": "Marktzoom API Environment", "values": [{"key": "baseUrl", "value": "http://localhost:5000", "type": "default", "enabled": true}, {"key": "token", "value": "", "type": "secret", "enabled": true}, {"key": "refresh_token", "value": "", "type": "secret", "enabled": true}, {"key": "user_id", "value": "", "type": "default", "enabled": true}, {"key": "buyer_id", "value": "", "type": "default", "enabled": true}, {"key": "seller_id", "value": "", "type": "default", "enabled": true}, {"key": "request_id", "value": "", "type": "default", "enabled": true}, {"key": "offer_id", "value": "", "type": "default", "enabled": true}, {"key": "category_id", "value": "", "type": "default", "enabled": true}, {"key": "subcategory_id", "value": "", "type": "default", "enabled": true}, {"key": "order_id", "value": "", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-12-15T10:00:00.000Z", "_postman_exported_using": "Postman/10.20.0"}