# Subscription System Documentation

## Overview

The subscription system provides a comprehensive solution for managing user subscriptions with usage limits for buyers and sellers. It includes subscription plans, user subscriptions, usage tracking, and automatic limit enforcement.

## Database Schema

### Tables

#### 1. `subscriptions`
Main subscription plans table managed by admins.

| Field | Type | Description |
|-------|------|-------------|
| id | UUID | Primary key |
| name | String | Unique subscription name |
| description | String | Optional description |
| price | Decimal | Subscription price |
| duration_days | Integer | Subscription duration (default: 30 days) |
| max_requests | Integer | Max requests per month (null = unlimited) |
| max_offers | Integer | Max offers per month (null = unlimited) |
| max_orders | Integer | Max orders per month (null = unlimited) |
| is_active | Boolean | Whether plan is active |
| is_featured | Boolean | Whether plan is featured |
| user_type | Enum | BUYER, SELLER, or BOTH |
| features | JSON | Additional features |

#### 2. `user_subscriptions`
User subscription instances.

| Field | Type | Description |
|-------|------|-------------|
| id | UUID | Primary key |
| user_id | UUID | Foreign key to users |
| subscription_id | UUID | Foreign key to subscriptions |
| status | Enum | ACTIVE, EXPIRED, CANCELLED, SUSPENDED |
| start_date | DateTime | Subscription start date |
| end_date | DateTime | Subscription end date |
| auto_renew | Boolean | Auto-renewal setting |
| payment_method | String | Payment method used |
| transaction_id | String | Payment transaction ID |

#### 3. `subscription_usage`
Monthly usage tracking.

| Field | Type | Description |
|-------|------|-------------|
| id | UUID | Primary key |
| user_subscription_id | UUID | Foreign key to user_subscriptions |
| usage_type | Enum | REQUEST, OFFER, ORDER |
| count | Integer | Usage count for the month |
| month | Integer | Month (1-12) |
| year | Integer | Year |

## API Endpoints

### Admin Routes (`/api/admin/subscriptions`)

#### Create Subscription Plan
```http
POST /api/admin/subscriptions
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "name": "Premium Plan",
  "description": "Premium subscription with enhanced features",
  "price": 29.99,
  "duration_days": 30,
  "max_requests": 100,
  "max_offers": 50,
  "max_orders": 25,
  "user_type": "BUYER",
  "is_featured": true,
  "features": {
    "support": "priority",
    "analytics": true
  }
}
```

#### Get All Subscription Plans
```http
GET /api/admin/subscriptions?page=1&limit=10&user_type=BUYER&is_active=true
Authorization: Bearer <admin_token>
```

#### Get Subscription Plan by ID
```http
GET /api/admin/subscriptions/{id}
Authorization: Bearer <admin_token>
```

#### Update Subscription Plan
```http
PUT /api/admin/subscriptions/{id}
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "price": 39.99,
  "max_requests": 150
}
```

#### Delete Subscription Plan
```http
DELETE /api/admin/subscriptions/{id}
Authorization: Bearer <admin_token>
```

#### Get Subscription Statistics
```http
GET /api/admin/subscriptions/stats
Authorization: Bearer <admin_token>
```

### Buyer Routes (`/api/buyer/subscriptions`)

#### Get Available Subscription Plans
```http
GET /api/buyer/subscriptions/available
Authorization: Bearer <buyer_token>
```

#### Subscribe to a Plan
```http
POST /api/buyer/subscriptions/subscribe
Authorization: Bearer <buyer_token>
Content-Type: application/json

{
  "subscription_id": "uuid",
  "payment_method": "credit_card",
  "transaction_id": "txn_123456",
  "auto_renew": true
}
```

#### Get Active Subscription
```http
GET /api/buyer/subscriptions/active
Authorization: Bearer <buyer_token>
```

#### Get Subscription History
```http
GET /api/buyer/subscriptions/history?page=1&limit=10
Authorization: Bearer <buyer_token>
```

#### Get Usage Status
```http
GET /api/buyer/subscriptions/usage
Authorization: Bearer <buyer_token>
```

#### Cancel Subscription
```http
PUT /api/buyer/subscriptions/{subscription_id}/cancel
Authorization: Bearer <buyer_token>
```

### Seller Routes (`/api/seller/subscriptions`)

Same endpoints as buyer routes, but for sellers.

## Usage Limits & Enforcement

### Middleware Integration

The system automatically enforces subscription limits through middleware:

- `checkRequestLimit` - Applied to request creation routes
- `checkOfferLimit` - Applied to offer creation routes  
- `checkOrderLimit` - Applied to order confirmation routes

### Usage Tracking

Usage is automatically tracked when users perform actions:

1. **Request Creation**: Increments REQUEST usage count
2. **Offer Creation**: Increments OFFER usage count
3. **Order Confirmation**: Increments ORDER usage count

### Limit Checking

Before allowing any action, the system:

1. Checks if user has an active subscription
2. Verifies current month's usage against limits
3. Allows action if within limits
4. Blocks action and returns error if limits exceeded

## Default Subscription Plans

The system includes 6 default plans:

1. **Free Plan** - Limited features for both buyers and sellers
2. **Buyer Basic** - Basic plan for buyers (20 requests, 10 orders)
3. **Buyer Premium** - Premium plan for buyers (100 requests, 50 orders)
4. **Seller Basic** - Basic plan for sellers (30 offers, 20 orders)
5. **Seller Premium** - Premium plan for sellers (100 offers, 100 orders)
6. **Enterprise** - Unlimited plan for large businesses

## Features

### Subscription Management
- Create, read, update, delete subscription plans
- Activate/deactivate plans
- Feature flags and pricing tiers

### User Subscriptions
- Subscribe to plans with payment tracking
- Auto-renewal capabilities
- Subscription history and status tracking

### Usage Tracking
- Monthly usage limits
- Real-time usage monitoring
- Automatic limit enforcement

### Analytics
- Subscription statistics for admins
- Usage analytics per user
- Revenue tracking

## Error Handling

### Common Error Responses

#### Subscription Limit Exceeded
```json
{
  "success": false,
  "message": "Subscription limit exceeded for requests",
  "errors": {
    "subscription": ["You have reached your request limit for this month. Please upgrade your subscription."]
  }
}
```

#### No Active Subscription
```json
{
  "success": false,
  "message": "Active subscription required",
  "errors": {
    "subscription": ["You need an active subscription to perform this action. Please subscribe to a plan."]
  }
}
```

## Migration

To apply the subscription system to your database:

```bash
npx prisma migrate dev --name add_subscriptions
```

This will create the necessary tables and insert default subscription plans.

## Usage Examples

### Check User's Current Usage
```javascript
const usageStatus = await SubscriptionService.getUserUsageStatus(userId);
console.log(usageStatus);
// Output:
// {
//   hasActiveSubscription: true,
//   subscription: { name: "Premium Plan", ... },
//   usage: {
//     requests: { canUse: true, limit: 100, used: 15, remaining: 85 },
//     offers: { canUse: true, limit: null, used: 0, remaining: null },
//     orders: { canUse: true, limit: 50, used: 3, remaining: 47 }
//   }
// }
```

### Manually Track Usage
```javascript
await SubscriptionService.checkAndTrackUsage(userId, 'REQUEST');
```

### Check if User Can Perform Action
```javascript
const canCreateRequest = await SubscriptionModel.checkUsageLimit(userId, 'REQUEST');
if (!canCreateRequest.canUse) {
  throw new Error(canCreateRequest.reason);
}
```
