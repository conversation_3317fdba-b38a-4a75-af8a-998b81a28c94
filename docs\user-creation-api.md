# User Creation API Documentation

This document outlines the API endpoint for creating users with different roles in the system.

## Endpoint

### Create User (Admin Only)

```
POST /api/admin/users
```

This endpoint allows admins to create users with different roles (Buyer, Seller, Supplier, Admin).

**Authentication Required**: Yes (Admin role)

**Request Body:**
```json
{
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "email": "<EMAIL>",
  "phone_number": "+1234567890",
  "password": "SecurePass123",
  "address": "123 Main St, City, Country",
  "gender": "male",
  "role": "Buyer",
  "is_approved": true,
  "status": "active"
}
```

**Required Fields:**
- `first_name`: String (2-50 characters)
- `last_name`: String (2-50 characters)
- `email`: Valid email address (must be unique)
- `phone_number`: Valid phone number (must be unique)
- `password`: String (min 8 characters, must contain uppercase, lowercase, and number)
- `role`: String (one of: "Buyer", "Seller", "Supplier", "Admin")

**Optional Fields:**
- `father_name`: String (max 50 characters)
- `mother_name`: String (max 50 characters)
- `age`: Integer (18-120)
- `gender`: String (one of: "male", "female", "other")
- `address`: String (max 255 characters)
- `national_id_number`: String (must be unique)
- `passport_number`: String (must be unique)
- `profile_picture_url`: Valid URL
- `is_approved`: Boolean
- `status`: String (one of: "active", "inactive", "suspended")

**Response (201 Created):**
```json
{
  "success": true,
  "message": "Buyer created successfully",
  "data": {
    "id": "uuid-string",
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "phone_number": "+1234567890",
    "roles": ["Buyer"],
    "is_approved": true,
    "status": "active",
    "created_at": "2023-05-01T12:00:00Z"
  }
}
```

## Error Responses

### Validation Error (422 Unprocessable Entity)

```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "email": "Email is already in use",
    "password": "Password must be at least 8 characters long"
  }
}
```

### Authentication Error (401 Unauthorized)

```json
{
  "success": false,
  "message": "Access Denied"
}
```

### Authorization Error (403 Forbidden)

```json
{
  "success": false,
  "message": "Admin creation requires authentication"
}
```

### Server Error (500 Internal Server Error)

```json
{
  "success": false,
  "message": "Failed to create user"
}
```
