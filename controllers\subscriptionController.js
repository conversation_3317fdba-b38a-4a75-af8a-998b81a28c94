const SubscriptionService = require('../services/subscriptionService');
const sendResponse = require('../utils/sendResponse');

class SubscriptionController {
  /**
   * Create a new subscription plan (Admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async createSubscription(req, res) {
    try {
      const subscription = await SubscriptionService.createSubscription(req.body);

      return sendResponse(
        res,
        true,
        'Subscription plan created successfully',
        subscription,
        null,
        null,
        201
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Get all subscription plans
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getAllSubscriptions(req, res) {
    try {
      const { user_type, is_active, is_featured } = req.query;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      const filters = {};
      if (user_type) filters.user_type = user_type;
      if (is_active !== undefined) filters.is_active = is_active === 'true';
      if (is_featured !== undefined) filters.is_featured = is_featured === 'true';

      const result = await SubscriptionService.getAllSubscriptions(filters, page, limit);

      return sendResponse(
        res,
        true,
        'Subscription plans retrieved successfully',
        result.data,
        null,
        result.meta,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Get subscription plan by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getSubscriptionById(req, res) {
    try {
      const { id } = req.params;
      const subscription = await SubscriptionService.getSubscriptionById(id);

      return sendResponse(
        res,
        true,
        'Subscription plan retrieved successfully',
        subscription,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Update subscription plan (Admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async updateSubscription(req, res) {
    try {
      const { id } = req.params;
      const subscription = await SubscriptionService.updateSubscription(id, req.body);

      return sendResponse(
        res,
        true,
        'Subscription plan updated successfully',
        subscription,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Delete subscription plan (Admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async deleteSubscription(req, res) {
    try {
      const { id } = req.params;
      await SubscriptionService.deleteSubscription(id);

      return sendResponse(
        res,
        true,
        'Subscription plan deleted successfully',
        null,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Subscribe user to a plan
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async subscribeUser(req, res) {
    try {
      const userId = req.user.id;
      const { subscription_id, payment_method, transaction_id, auto_renew } = req.body;

      if (!subscription_id) {
        return sendResponse(
          res,
          false,
          'Subscription ID is required',
          null,
          { subscription_id: ['Subscription ID is required'] },
          null,
          400
        );
      }

      const paymentData = {
        payment_method,
        transaction_id,
        auto_renew: auto_renew || false
      };

      const userSubscription = await SubscriptionService.subscribeUser(userId, subscription_id, paymentData);

      return sendResponse(
        res,
        true,
        'Successfully subscribed to plan',
        userSubscription,
        null,
        null,
        201
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Get user's active subscription
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getUserActiveSubscription(req, res) {
    try {
      const userId = req.user.id;
      const subscription = await SubscriptionService.getUserActiveSubscription(userId);

      return sendResponse(
        res,
        true,
        subscription ? 'Active subscription retrieved successfully' : 'No active subscription found',
        subscription,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Get user's subscription history
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getUserSubscriptionHistory(req, res) {
    try {
      const userId = req.user.id;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      const result = await SubscriptionService.getUserSubscriptionHistory(userId, page, limit);

      return sendResponse(
        res,
        true,
        'Subscription history retrieved successfully',
        result.data,
        null,
        result.meta,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Cancel user subscription
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async cancelSubscription(req, res) {
    try {
      const userId = req.user.id;
      const { subscription_id } = req.params;

      const subscription = await SubscriptionService.cancelSubscription(userId, subscription_id);

      return sendResponse(
        res,
        true,
        'Subscription cancelled successfully',
        subscription,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Get user's current usage status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getUserUsageStatus(req, res) {
    try {
      const userId = req.user.id;
      const usageStatus = await SubscriptionService.getUserUsageStatus(userId);

      return sendResponse(
        res,
        true,
        'Usage status retrieved successfully',
        usageStatus,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Get subscription statistics (Admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getSubscriptionStats(req, res) {
    try {
      const stats = await SubscriptionService.getSubscriptionStats();

      return sendResponse(
        res,
        true,
        'Subscription statistics retrieved successfully',
        stats,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Get available subscription plans for current user type
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getAvailableSubscriptions(req, res) {
    try {
      // Determine user type from roles
      const userRoles = req.user.roles || [];
      let userType = 'BUYER'; // default

      if (userRoles.includes('Seller')) {
        userType = 'SELLER';
      }

      const subscriptions = await SubscriptionService.getAvailableSubscriptions(userType);

      return sendResponse(
        res,
        true,
        'Available subscription plans retrieved successfully',
        subscriptions,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Update trial subscription duration (Admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async updateTrialDuration(req, res) {
    try {
      const { duration_days } = req.body;

      const updatedSubscription = await SubscriptionService.updateTrialDuration(duration_days);

      return sendResponse(
        res,
        true,
        'Trial subscription duration updated successfully',
        updatedSubscription,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Assign trial subscription to specific users (Admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async assignTrialToUsers(req, res) {
    try {
      const { user_ids } = req.body;

      const results = await SubscriptionService.assignTrialToUsers(user_ids);

      return sendResponse(
        res,
        true,
        'Trial subscription assignment completed',
        results,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Assign trial subscription to all existing users without active subscriptions (Admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async assignTrialToAllUsers(req, res) {
    try {
      const results = await SubscriptionService.assignTrialToAllUsers();

      return sendResponse(
        res,
        true,
        'Trial subscription assignment to all users completed',
        results,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }
}

module.exports = SubscriptionController;
