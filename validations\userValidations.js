const { query, param, body } = require('express-validator');

const userValidations = {
  // Search users validation
  searchUsers: [
    query('search')
      .optional()
      .isString().withMessage('Search query must be a string')
      .trim(),
    
    query('page')
      .optional()
      .isInt({ min: 1 }).withMessage('Page must be a positive integer')
      .toInt(),
    
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
      .toInt(),
    
    query('role')
      .optional()
      .isString().withMessage('Role must be a string'),
    
    query('status')
      .optional()
      .isString().withMessage('Status must be a string'),
    
    query('is_approved')
      .optional()
      .isBoolean().withMessage('is_approved must be a boolean')
      .toBoolean()
  ],

  // User ID parameter validation
  idParam: [
    param('id')
      .isUUID().withMessage('Invalid user ID')
  ],

  // Update user role validation
  updateUserRole: [
    param('id')
      .isUUID().withMessage('Invalid user ID'),
    
    body('roles')
      .isArray().withMessage('Roles must be an array')
      .notEmpty().withMessage('Roles array cannot be empty')
  ]
};

module.exports = userValidations;
