const { execSync } = require('child_process');
const path = require('path');

// Run Prisma migration
try {
  console.log('Running Prisma migrations...');
  execSync('npx prisma migrate deploy', { stdio: 'inherit' });
  console.log('✅ Migrations applied successfully!');
} catch (error) {
  console.error('❌ Error applying migrations:', error.message);
  process.exit(1);
}

// Generate Prisma client
try {
  console.log('Generating Prisma client...');
  execSync('npx prisma generate', { stdio: 'inherit' });
  console.log('✅ Prisma client generated successfully!');
} catch (error) {
  console.error('❌ Error generating Prisma client:', error.message);
  process.exit(1);
}

console.log('🚀 Database migration completed successfully!');
