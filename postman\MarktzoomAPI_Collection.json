{"info": {"_postman_id": "marktzoom-api-collection", "name": "Marktzoom API Collection", "description": "Complete API collection for Marktzoom backend including Auth, Buyer, and Seller routes with accurate validation-based requests", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "{{baseUrl}}"}], "item": [{"name": "🔐 Auth Routes", "item": [{"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone_number\": \"+*********0\",\n  \"password\": \"Password123!\",\n  \"business_name\": \"John's Business\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}}}, {"name": "Mobile Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone_number\": \"+0*********\",\n  \"password\": \"Password123!\",\n  \"business_name\": \"Jane's Mobile Business\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/mobile/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "mobile", "register"]}}}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"otp\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/verify/otp", "host": ["{{baseUrl}}"], "path": ["api", "auth", "verify", "otp"]}}}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.access_token) {", "        pm.environment.set('token', response.data.access_token);", "        console.log('Token saved to environment');", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}}, {"name": "New User Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"alice_johnson\",\n  \"password\": \"Password123@\",\n  \"user_type\": \"business\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone_number\": \"+1122334455\",\n  \"date_of_birth\": \"1990-01-15\",\n  \"gender\": \"female\",\n  \"address\": \"123 Main St, City, State\",\n  \"city\": \"New York\",\n  \"state\": \"NY\",\n  \"country\": \"USA\",\n  \"zip\": \"10001\",\n  \"business_name\": \"Alice Tech Solutions\",\n  \"business_type\": \"IT\",\n  \"business_address\": \"456 Business Ave, Tech District, NY 10002\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/new-register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "new-register"]}}}, {"name": "New User Login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123@\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/new-login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "new-login"]}}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refresh_token\": \"your_refresh_token_here\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/refresh-token", "host": ["{{baseUrl}}"], "path": ["api", "auth", "refresh-token"]}}}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/forgot-password", "host": ["{{baseUrl}}"], "path": ["api", "auth", "forgot-password"]}}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"reset_token_here\",\n  \"newPassword\": \"NewPassword123!\",\n  \"confirmPassword\": \"NewPassword123!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/reset-password", "host": ["{{baseUrl}}"], "path": ["api", "auth", "reset-password"]}}}, {"name": "Change Password", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"Password123!\",\n  \"newPassword\": \"NewPassword456@\",\n  \"confirmPassword\": \"NewPassword456@\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/change-password", "host": ["{{baseUrl}}"], "path": ["api", "auth", "change-password"]}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"verification_token_here\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/verify-email", "host": ["{{baseUrl}}"], "path": ["api", "auth", "verify-email"]}}}, {"name": "Complete Profile", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "<PERSON>", "type": "text"}, {"key": "last_name", "value": "<PERSON><PERSON>", "type": "text"}, {"key": "phone_number", "value": "+*********0", "type": "text"}, {"key": "country", "value": "USA", "type": "text"}, {"key": "city", "value": "New York", "type": "text"}, {"key": "state", "value": "NY", "type": "text"}, {"key": "zip", "value": "10001", "type": "text"}, {"key": "address", "value": "123 Main Street, Apt 4B", "type": "text"}, {"key": "user_type", "value": "business", "type": "text", "description": "personal or business"}, {"key": "occupation", "value": "Software Developer", "type": "text", "description": "Required for personal users"}, {"key": "date_of_birth", "value": "1990-01-15", "type": "text", "description": "Required for personal users (YYYY-MM-DD)"}, {"key": "gender", "value": "male", "type": "text", "description": "Required for personal users (male/female/other)"}, {"key": "business_name", "value": "John's Tech Solutions", "type": "text", "description": "Required for business users"}, {"key": "business_type", "value": "IT", "type": "text", "description": "Required for business users (IT/Clothing/Restaurant/Retail/Service/Other)"}, {"key": "business_address", "value": "456 Business Ave, Tech District, NY 10002", "type": "text", "description": "Required for business users"}, {"key": "business_website", "value": "https://johnstech.com", "type": "text", "description": "Optional"}, {"key": "business_registration_number", "value": "REG*********", "type": "text", "description": "Optional"}, {"key": "tax_id", "value": "TAX*********", "type": "text", "description": "Optional"}, {"key": "profile_picture", "type": "file", "src": [], "description": "Profile picture file (JPEG, PNG, GIF - Max 5MB)"}, {"key": "business_document", "type": "file", "src": [], "description": "Business registration document"}, {"key": "nid_document", "type": "file", "src": [], "description": "National ID document"}]}, "url": {"raw": "{{baseUrl}}/api/auth/complete-profile", "host": ["{{baseUrl}}"], "path": ["api", "auth", "complete-profile"]}}}, {"name": "Get Profile Status", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/profile-status", "host": ["{{baseUrl}}"], "path": ["api", "auth", "profile-status"]}}}, {"name": "Upload Profile Picture", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "profile_picture", "type": "file", "src": [], "description": "Profile picture file (JPEG, JPG, PNG, GIF - Max 5MB)"}]}, "url": {"raw": "{{baseUrl}}/api/auth/upload-profile-picture", "host": ["{{baseUrl}}"], "path": ["api", "auth", "upload-profile-picture"]}}}]}, {"name": "🛒 Buyer Routes", "item": [{"name": "Profile Management", "item": [{"name": "Get Buyer Profile", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/buyer/profile", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "profile"]}}}, {"name": "Update Buyer Profile", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "<PERSON>", "type": "text"}, {"key": "last_name", "value": "<PERSON><PERSON>", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "phone_number", "value": "+*********0", "type": "text"}, {"key": "gender", "value": "Male", "type": "text"}, {"key": "date_of_birth", "value": "1990-01-15", "type": "text"}, {"key": "address", "value": "123 Main Street, City, State", "type": "text"}, {"key": "father_name", "value": "<PERSON>", "type": "text"}, {"key": "mother_name", "value": "<PERSON>", "type": "text"}, {"key": "national_id_number", "value": "*********", "type": "text"}, {"key": "passport_number", "value": "P*********", "type": "text"}, {"key": "profile_picture", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/api/buyer/profile", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "profile"]}}}]}, {"name": "Categories", "item": [{"name": "Get Categories", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/buyer/categories", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "categories"]}}}, {"name": "Get Category with Subcategories", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/buyer/categories/:id/subcategories", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "categories", ":id", "subcategories"], "variable": [{"key": "id", "value": "category-uuid-here"}]}}}, {"name": "Get Subcategory with Form Fields", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/buyer/subcategories/:id", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "subcategories", ":id"], "variable": [{"key": "id", "value": "subcategory-uuid-here"}]}}}]}, {"name": "Requests Management", "item": [{"name": "Create Request", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Need Web Development Services\",\n  \"short_description\": \"Looking for a professional web developer\",\n  \"description\": \"I need a full-stack web developer to build an e-commerce website with modern features including payment integration, user authentication, and admin panel.\",\n  \"category_id\": \"category-uuid-here\",\n  \"sub_category_id\": \"subcategory-uuid-here\",\n  \"quantity\": 1,\n  \"budget_min\": 1000,\n  \"budget_max\": 5000,\n  \"deadline\": \"2024-12-31\",\n  \"urgency\": \"Medium\",\n  \"request_type\": \"Service\",\n  \"additional_info\": {\n    \"technology_stack\": \"React, Node.js, MongoDB\",\n    \"design_preference\": \"Modern and responsive\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/buyer/requests", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "requests"]}}}, {"name": "Get My Requests", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/buyer/requests?page=1&limit=10&status=Active&category_id=&search=", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "requests"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": "Active"}, {"key": "category_id", "value": ""}, {"key": "search", "value": ""}]}}}, {"name": "Get Request Details", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/buyer/requests/:id", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "requests", ":id"], "variable": [{"key": "id", "value": "request-uuid-here"}]}}}, {"name": "Update Request", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Updated Web Development Services\",\n  \"short_description\": \"Updated description for web developer\",\n  \"description\": \"Updated detailed description with new requirements\",\n  \"quantity\": 1,\n  \"budget_min\": 1500,\n  \"budget_max\": 6000,\n  \"deadline\": \"2024-12-31\",\n  \"urgency\": \"High\",\n  \"additional_info\": {\n    \"technology_stack\": \"React, Node.js, PostgreSQL\",\n    \"design_preference\": \"Modern, responsive, and accessible\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/buyer/requests/:id", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "requests", ":id"], "variable": [{"key": "id", "value": "request-uuid-here"}]}}}, {"name": "Delete Request", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/buyer/requests/:id", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "requests", ":id"], "variable": [{"key": "id", "value": "request-uuid-here"}]}}}]}, {"name": "Offers Management", "item": [{"name": "Get My Offers", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/buyer/my-offers?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "my-offers"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get Processed Offers", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/buyer/processed-offers", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "processed-offers"]}}}, {"name": "Get Offer Details", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/buyer/offers/:id", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "offers", ":id"], "variable": [{"key": "id", "value": "offer-uuid-here"}]}}}]}, {"name": "Cart Management", "item": [{"name": "Add to Cart", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"offer_id\": \"offer-uuid-here\",\n  \"quantity\": 1,\n  \"additional_services\": [\n    {\n      \"service_id\": \"service-uuid-here\",\n      \"quantity\": 1\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/buyer/cart", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "cart"]}}}, {"name": "Get Cart Items", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/buyer/cart", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "cart"]}}}, {"name": "Update Cart Item", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"quantity\": 2,\n  \"additional_services\": [\n    {\n      \"service_id\": \"service-uuid-here\",\n      \"quantity\": 2\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/buyer/cart/:itemId", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "cart", ":itemId"], "variable": [{"key": "itemId", "value": "cart-item-uuid-here"}]}}}, {"name": "Remove Cart Item", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/buyer/cart/:itemId", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "cart", ":itemId"], "variable": [{"key": "itemId", "value": "cart-item-uuid-here"}]}}}, {"name": "Clear Cart", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/buyer/cart", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "cart"]}}}]}, {"name": "Subscriptions", "item": [{"name": "Get Available Subscriptions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/buyer/subscriptions/available", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "subscriptions", "available"]}}}, {"name": "Subscribe to Plan", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"subscription_id\": \"subscription-uuid-here\",\n  \"payment_method\": \"credit_card\",\n  \"transaction_id\": \"txn_*********\",\n  \"auto_renew\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/buyer/subscriptions/subscribe", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "subscriptions", "subscribe"]}}}, {"name": "Get Active Subscription", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/buyer/subscriptions/active", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "subscriptions", "active"]}}}, {"name": "Get Subscription History", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/buyer/subscriptions/history?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "subscriptions", "history"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get Usage Status", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/buyer/subscriptions/usage", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "subscriptions", "usage"]}}}, {"name": "Cancel Subscription", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/api/buyer/subscriptions/:subscription_id/cancel", "host": ["{{baseUrl}}"], "path": ["api", "buyer", "subscriptions", ":subscription_id", "cancel"], "variable": [{"key": "subscription_id", "value": "subscription-uuid-here"}]}}}]}]}, {"name": "🏪 Seller Routes", "item": [{"name": "Profile Management", "item": [{"name": "<PERSON> Seller Profile", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/seller/profile", "host": ["{{baseUrl}}"], "path": ["api", "seller", "profile"]}}}, {"name": "Update Seller Profile", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "<PERSON>", "type": "text"}, {"key": "last_name", "value": "<PERSON>", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "phone_number", "value": "+0*********", "type": "text"}, {"key": "gender", "value": "Female", "type": "text"}, {"key": "date_of_birth", "value": "1985-05-20", "type": "text"}, {"key": "address", "value": "456 Business Ave, Commerce City, State", "type": "text"}, {"key": "father_name", "value": "<PERSON>", "type": "text"}, {"key": "mother_name", "value": "<PERSON>", "type": "text"}, {"key": "national_id_number", "value": "*********", "type": "text"}, {"key": "passport_number", "value": "P*********", "type": "text"}, {"key": "profile_picture", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/api/seller/profile", "host": ["{{baseUrl}}"], "path": ["api", "seller", "profile"]}}}]}, {"name": "Business Information", "item": [{"name": "Get Business Information", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/seller/business-information", "host": ["{{baseUrl}}"], "path": ["api", "seller", "business-information"]}}}, {"name": "Update Business Information", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"business_name\": \"Jane's Tech Solutions\",\n  \"business_type\": \"IT Services\",\n  \"business_address\": \"789 Tech Park, Innovation District, State 12345\",\n  \"business_phone\": \"+1122334455\",\n  \"business_email\": \"<EMAIL>\",\n  \"business_website\": \"https://janestech.com\",\n  \"business_description\": \"We provide comprehensive IT solutions including web development, mobile apps, and digital marketing services.\",\n  \"years_in_business\": 5,\n  \"number_of_employees\": 15,\n  \"business_registration_number\": \"REG*********\",\n  \"tax_id\": \"TAX*********\"\n}"}, "url": {"raw": "{{baseUrl}}/api/seller/business-information", "host": ["{{baseUrl}}"], "path": ["api", "seller", "business-information"]}}}]}, {"name": "Requests & Offers", "item": [{"name": "Get Available Requests", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/seller/requests?page=1&limit=10&category_id=&search=&urgency=&budget_min=&budget_max=", "host": ["{{baseUrl}}"], "path": ["api", "seller", "requests"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "category_id", "value": ""}, {"key": "search", "value": ""}, {"key": "urgency", "value": ""}, {"key": "budget_min", "value": ""}, {"key": "budget_max", "value": ""}]}}}, {"name": "Get Request Details", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/seller/requests/:id", "host": ["{{baseUrl}}"], "path": ["api", "seller", "requests", ":id"], "variable": [{"key": "id", "value": "request-uuid-here"}]}}}, {"name": "Express Interest", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"request_id\": \"request-uuid-here\",\n  \"message\": \"I am very interested in this project and have 5+ years of experience in web development. I can deliver high-quality work within your timeline and budget.\"\n}"}, "url": {"raw": "{{baseUrl}}/api/seller/interest", "host": ["{{baseUrl}}"], "path": ["api", "seller", "interest"]}}}, {"name": "Create Offer", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"request_id\": \"request-uuid-here\",\n  \"price\": 3500,\n  \"delivery_time\": 30,\n  \"delivery_time_unit\": \"days\",\n  \"description\": \"I will develop a complete e-commerce website with all the features you mentioned. This includes:\\n\\n- Modern responsive design\\n- User authentication system\\n- Payment gateway integration\\n- Admin panel\\n- Product management\\n- Order management\\n- SEO optimization\\n\\nI have 5+ years of experience in React and Node.js development.\",\n  \"terms_and_conditions\": \"Payment will be made in milestones: 50% upfront, 50% on completion. Includes 30 days of free support after delivery.\",\n  \"additional_services\": [\n    {\n      \"name\": \"Mobile App Development\",\n      \"price\": 2000,\n      \"description\": \"Additional mobile app for iOS and Android\"\n    },\n    {\n      \"name\": \"SEO Package\",\n      \"price\": 500,\n      \"description\": \"Complete SEO optimization and setup\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/seller/offers", "host": ["{{baseUrl}}"], "path": ["api", "seller", "offers"]}}}, {"name": "Get My Offers", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/seller/offers?page=1&limit=10&status=", "host": ["{{baseUrl}}"], "path": ["api", "seller", "offers"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": ""}]}}}, {"name": "Update Offer", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"price\": 4000,\n  \"delivery_time\": 25,\n  \"delivery_time_unit\": \"days\",\n  \"description\": \"Updated offer description with additional features and improved timeline.\",\n  \"terms_and_conditions\": \"Updated terms: Payment in 3 milestones - 40% upfront, 40% at 50% completion, 20% on final delivery.\"\n}"}, "url": {"raw": "{{baseUrl}}/api/seller/offers/:id", "host": ["{{baseUrl}}"], "path": ["api", "seller", "offers", ":id"], "variable": [{"key": "id", "value": "offer-uuid-here"}]}}}]}, {"name": "Interested Categories", "item": [{"name": "Get Interested Categories", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/seller/interested-categories", "host": ["{{baseUrl}}"], "path": ["api", "seller", "interested-categories"]}}}, {"name": "Add Interested Category", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"category_id\": \"category-uuid-here\"\n}"}, "url": {"raw": "{{baseUrl}}/api/seller/interested-categories", "host": ["{{baseUrl}}"], "path": ["api", "seller", "interested-categories"]}}}, {"name": "Remove Interested Category", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/seller/interested-categories/:categoryId", "host": ["{{baseUrl}}"], "path": ["api", "seller", "interested-categories", ":categoryId"], "variable": [{"key": "categoryId", "value": "category-uuid-here"}]}}}, {"name": "Update Interested Categories", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"category_ids\": [\n    \"category-uuid-1\",\n    \"category-uuid-2\",\n    \"category-uuid-3\"\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/seller/interested-categories", "host": ["{{baseUrl}}"], "path": ["api", "seller", "interested-categories"]}}}]}, {"name": "Interested Subcategories", "item": [{"name": "Get Interested Subcategories", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/seller/interested-subcategories", "host": ["{{baseUrl}}"], "path": ["api", "seller", "interested-subcategories"]}}}, {"name": "Add Interested Subcategory", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"subcategory_id\": \"subcategory-uuid-here\"\n}"}, "url": {"raw": "{{baseUrl}}/api/seller/interested-subcategories", "host": ["{{baseUrl}}"], "path": ["api", "seller", "interested-subcategories"]}}}, {"name": "Remove Interested Subcategory", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/seller/interested-subcategories/:subcategoryId", "host": ["{{baseUrl}}"], "path": ["api", "seller", "interested-subcategories", ":subcategoryId"], "variable": [{"key": "subcategoryId", "value": "subcategory-uuid-here"}]}}}, {"name": "Update Interested Subcategories", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"subcategory_ids\": [\n    \"subcategory-uuid-1\",\n    \"subcategory-uuid-2\",\n    \"subcategory-uuid-3\"\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/seller/interested-subcategories", "host": ["{{baseUrl}}"], "path": ["api", "seller", "interested-subcategories"]}}}, {"name": "Get Subcategories for Interested Categories", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/seller/subcategories-for-interested-categories", "host": ["{{baseUrl}}"], "path": ["api", "seller", "subcategories-for-interested-categories"]}}}]}, {"name": "Subscriptions", "item": [{"name": "Get Available Subscriptions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/seller/subscriptions/available", "host": ["{{baseUrl}}"], "path": ["api", "seller", "subscriptions", "available"]}}}, {"name": "Subscribe to Plan", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"subscription_id\": \"subscription-uuid-here\",\n  \"payment_method\": \"credit_card\",\n  \"transaction_id\": \"txn_*********\",\n  \"auto_renew\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/seller/subscriptions/subscribe", "host": ["{{baseUrl}}"], "path": ["api", "seller", "subscriptions", "subscribe"]}}}, {"name": "Get Active Subscription", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/seller/subscriptions/active", "host": ["{{baseUrl}}"], "path": ["api", "seller", "subscriptions", "active"]}}}, {"name": "Get Subscription History", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/seller/subscriptions/history?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "seller", "subscriptions", "history"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get Usage Status", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/seller/subscriptions/usage", "host": ["{{baseUrl}}"], "path": ["api", "seller", "subscriptions", "usage"]}}}, {"name": "Cancel Subscription", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/api/seller/subscriptions/:subscription_id/cancel", "host": ["{{baseUrl}}"], "path": ["api", "seller", "subscriptions", ":subscription_id", "cancel"], "variable": [{"key": "subscription_id", "value": "subscription-uuid-here"}]}}}]}]}]}