const { query, param, body } = require('express-validator');

// Validation rules for filtering offers
const filterOffers = [
  query('status')
    .optional()
    .isString()
    .withMessage('Status must be a string'),

  query('request_id')
    .optional()
    .isUUID()
    .withMessage('Request ID must be a valid UUID'),

  query('seller_id')
    .optional()
    .isUUID()
    .withMessage('Seller ID must be a valid UUID'),

  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
];

// Validation rules for offer ID parameter
const idParam = [
  param('id')
    .isUUID()
    .withMessage('Offer ID must be a valid UUID'),
];

// Validation rules for updating offer status
const updateOfferStatus = [
  param('id')
    .isUUID()
    .withMessage('Offer ID must be a valid UUID'),

  body('status')
    .isString()
    .notEmpty()
    .withMessage('Status is required')
    .isIn(['Approved', 'Rejected', 'Pending', 'Accepted', 'Completed', 'Cancelled'])
    .withMessage('Status must be one of: Approved, Rejected, Pending, Accepted, Completed, Cancelled'),

  body('reason')
    .optional()
    .isString()
    .withMessage('Reason must be a string')
];

module.exports = {
  filterOffers,
  idParam,
  updateOfferStatus,
};
