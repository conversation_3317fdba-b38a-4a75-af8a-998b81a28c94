# Business Name Column Migration

## Overview

This document describes the migration process for adding the `business_name` column to the `users` table.

## Migration Files Created

### 1. `20250521190000_add_business_name_to_users`

This migration adds the `business_name` column to the users table.

**Purpose**: Allow users to store their business name separately from their personal name.

**SQL Changes**:
```sql
-- Add business_name column to users table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'business_name'
    ) THEN
        ALTER TABLE "users" ADD COLUMN "business_name" TEXT;
    END IF;
END $$;
```

### 2. `20250521191000_enhance_business_name_column`

This migration adds performance optimizations for the `business_name` column.

**Purpose**: Improve query performance when searching or filtering by business name.

**SQL Changes**:
```sql
-- Add index on business_name for better search performance
CREATE INDEX "users_business_name_idx" ON "users"("business_name");

-- Add partial index for non-null business names
CREATE INDEX "users_business_name_not_null_idx" ON "users"("business_name") WHERE "business_name" IS NOT NULL;
```

## Column Details

- **Column Name**: `business_name`
- **Data Type**: `TEXT`
- **Nullable**: `YES` (Optional field)
- **Default Value**: `NULL`
- **Indexes**: 
  - `users_business_name_idx` (Full index)
  - `users_business_name_not_null_idx` (Partial index for non-null values)

## Usage Examples

### 1. Creating a User with Business Name

```javascript
const user = await prisma.users.create({
  data: {
    first_name: "John",
    last_name: "Doe",
    email: "<EMAIL>",
    phone_number: "+1234567890",
    password_hash: "hashed_password",
    business_name: "Doe Enterprises LLC"
  }
});
```

### 2. Updating Business Name

```javascript
const updatedUser = await prisma.users.update({
  where: { id: userId },
  data: {
    business_name: "New Business Name LLC"
  }
});
```

### 3. Searching Users by Business Name

```javascript
const users = await prisma.users.findMany({
  where: {
    business_name: {
      contains: "Enterprise",
      mode: 'insensitive'
    }
  }
});
```

### 4. Finding Users with Business Names

```javascript
const businessUsers = await prisma.users.findMany({
  where: {
    business_name: {
      not: null
    }
  }
});
```

## API Integration

### User Registration/Update Endpoints

The `business_name` field can now be included in user registration and update requests:

```json
{
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "phone_number": "+1234567890",
  "business_name": "Doe Enterprises LLC"
}
```

### Response Format

User responses will now include the `business_name` field:

```json
{
  "id": "uuid",
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "phone_number": "+1234567890",
  "business_name": "Doe Enterprises LLC",
  "created_at": "2025-05-21T19:10:00.000Z",
  "updated_at": "2025-05-21T19:10:00.000Z"
}
```

## Validation Considerations

When implementing frontend and backend validation for the `business_name` field:

1. **Optional Field**: The field is optional, so it can be null or empty
2. **Length Limits**: Consider adding reasonable length limits (e.g., 1-255 characters)
3. **Character Validation**: Allow alphanumeric characters, spaces, and common business symbols
4. **Uniqueness**: Consider if business names should be unique (currently not enforced)

## Performance Notes

- The `users_business_name_idx` index improves performance for exact matches and sorting
- The `users_business_name_not_null_idx` partial index optimizes queries that filter out null values
- Use the `mode: 'insensitive'` option for case-insensitive searches

## Migration Status

✅ **Applied**: Both migration files have been successfully applied to the database.

## Rollback Instructions

If you need to rollback these changes:

```sql
-- Remove indexes
DROP INDEX IF EXISTS "users_business_name_not_null_idx";
DROP INDEX IF EXISTS "users_business_name_idx";

-- Remove column
ALTER TABLE "users" DROP COLUMN IF EXISTS "business_name";
```

**Note**: Always backup your database before performing rollback operations.

## Related Files

- `prisma/models/User.prisma` - Contains the schema definition
- `models/userModel.js` - User model implementation
- `controllers/userController.js` - User controller (may need updates)
- `validations/userValidations.js` - User validation rules (may need updates)
