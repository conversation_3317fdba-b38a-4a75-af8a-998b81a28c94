-- Create business_informations table with all necessary fields and constraints
-- This migration is idempotent and safe for CI/CD deployment

-- Create the business_informations table if it doesn't exist
CREATE TABLE IF NOT EXISTS "business_informations" (
    "id" TEXT NOT NULL,
    "seller_id" TEXT NOT NULL,
    "company_name" TEXT NOT NULL,
    "short_description" TEXT,
    "long_description" TEXT,
    "logo_url" TEXT,
    "banner_url" TEXT,
    "address" TEXT,
    "city" TEXT,
    "state" TEXT,
    "country" TEXT,
    "postal_code" TEXT,
    "phone_number" TEXT,
    "email" TEXT,
    "website_url" TEXT,
    "business_type" TEXT,
    "business_category" TEXT,
    "established_year" INTEGER,
    "employee_count" TEXT,
    "annual_revenue" TEXT,
    "business_license" TEXT,
    "tax_id" TEXT,
    "social_media_links" JSONB,
    "operating_hours" JSONB,
    "services_offered" JSONB,
    "certifications" JSONB,
    "is_verified" BOOLEAN NOT NULL DEFAULT false,
    "verification_status" TEXT NOT NULL DEFAULT 'pending',
    "verification_date" TIMESTAMP(3),
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "is_deleted" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "business_informations_pkey" PRIMARY KEY ("id")
);

-- Add foreign key constraint if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'business_informations_seller_id_fkey'
        AND table_name = 'business_informations'
    ) THEN
        ALTER TABLE "business_informations" 
        ADD CONSTRAINT "business_informations_seller_id_fkey" 
        FOREIGN KEY ("seller_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
    END IF;
END $$;

-- Create indexes for better performance if they don't exist
CREATE INDEX IF NOT EXISTS "business_informations_seller_id_idx" ON "business_informations"("seller_id");
CREATE INDEX IF NOT EXISTS "business_informations_company_name_idx" ON "business_informations"("company_name");
CREATE INDEX IF NOT EXISTS "business_informations_business_type_idx" ON "business_informations"("business_type");
CREATE INDEX IF NOT EXISTS "business_informations_business_category_idx" ON "business_informations"("business_category");
CREATE INDEX IF NOT EXISTS "business_informations_verification_status_idx" ON "business_informations"("verification_status");
CREATE INDEX IF NOT EXISTS "business_informations_is_active_idx" ON "business_informations"("is_active");
CREATE INDEX IF NOT EXISTS "business_informations_is_deleted_idx" ON "business_informations"("is_deleted");
CREATE INDEX IF NOT EXISTS "business_informations_city_idx" ON "business_informations"("city");
CREATE INDEX IF NOT EXISTS "business_informations_country_idx" ON "business_informations"("country");
CREATE INDEX IF NOT EXISTS "business_informations_created_at_idx" ON "business_informations"("created_at");

-- Create composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS "business_informations_seller_active_idx" ON "business_informations"("seller_id", "is_active") WHERE "is_deleted" = false;
CREATE INDEX IF NOT EXISTS "business_informations_verification_active_idx" ON "business_informations"("verification_status", "is_active") WHERE "is_deleted" = false;
CREATE INDEX IF NOT EXISTS "business_informations_type_category_idx" ON "business_informations"("business_type", "business_category") WHERE "is_deleted" = false;

-- Add check constraints for data integrity
DO $$
BEGIN
    -- Check constraint for verification_status
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'business_informations_verification_status_check'
    ) THEN
        ALTER TABLE "business_informations" 
        ADD CONSTRAINT "business_informations_verification_status_check" 
        CHECK ("verification_status" IN ('pending', 'verified', 'rejected'));
    END IF;

    -- Check constraint for established_year
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'business_informations_established_year_check'
    ) THEN
        ALTER TABLE "business_informations" 
        ADD CONSTRAINT "business_informations_established_year_check" 
        CHECK ("established_year" IS NULL OR ("established_year" >= 1800 AND "established_year" <= EXTRACT(YEAR FROM CURRENT_DATE)));
    END IF;

    -- Check constraint for email format (basic)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'business_informations_email_format_check'
    ) THEN
        ALTER TABLE "business_informations" 
        ADD CONSTRAINT "business_informations_email_format_check" 
        CHECK ("email" IS NULL OR "email" ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');
    END IF;
END $$;

-- Create trigger for updating updated_at timestamp
CREATE OR REPLACE FUNCTION update_business_informations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop trigger if exists and create new one
DROP TRIGGER IF EXISTS update_business_informations_updated_at_trigger ON "business_informations";
CREATE TRIGGER update_business_informations_updated_at_trigger
    BEFORE UPDATE ON "business_informations"
    FOR EACH ROW
    EXECUTE FUNCTION update_business_informations_updated_at();

-- Add comments for documentation
COMMENT ON TABLE "business_informations" IS 'Stores business information for sellers';
COMMENT ON COLUMN "business_informations"."seller_id" IS 'Reference to the seller (user) who owns this business';
COMMENT ON COLUMN "business_informations"."company_name" IS 'Official name of the business/company';
COMMENT ON COLUMN "business_informations"."verification_status" IS 'Status of business verification: pending, verified, or rejected';
COMMENT ON COLUMN "business_informations"."social_media_links" IS 'JSON object containing social media links';
COMMENT ON COLUMN "business_informations"."operating_hours" IS 'JSON object containing business operating hours';
COMMENT ON COLUMN "business_informations"."services_offered" IS 'JSON array of services offered by the business';
COMMENT ON COLUMN "business_informations"."certifications" IS 'JSON array of business certifications and licenses';
