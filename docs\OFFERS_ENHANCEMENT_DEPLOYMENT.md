# Offers Enhancement Deployment Guide

## Overview

This document outlines the deployment of the enhanced offers system that includes:

1. **Enhanced offers table** with new columns for standalone offers
2. **Dynamic form fields** support for offers based on subcategories
3. **Public offer APIs** for browsing and filtering offers
4. **Backward compatibility** with existing offer creation for requests

## Database Changes

### New Columns Added to `offers` Table

- `offer_title` (TEXT, nullable) - Title for standalone offers
- `category_id` (TEXT, nullable) - Direct category reference
- `subcategory_id` (TEXT, nullable) - Direct subcategory reference  
- `short_description` (TEXT, nullable) - Brief offer description
- `discount` (DOUBLE PRECISION, nullable) - Discount amount
- `quantity` (INTEGER, default 1) - Available quantity
- `offer_type` (ENUM, default 'service') - Type: digital_product, physical_product, service
- `request_id` (now nullable) - Optional request reference

### New Table: `offer_form_field_values`

Stores dynamic form field values for offers:
- `id` (TEXT, PRIMARY KEY)
- `offer_id` (TEXT, FOREIGN KEY to offers)
- `form_field_id` (TEXT, FOREIGN KEY to subcategory_form_fields)
- `field_value` (TEXT, nullable)
- `created_at`, `updated_at` (TIMESTAMP)

### Missing Columns Fix

Added missing code columns that were in schema but not in database:
- `requests.request_code` (TEXT, unique)
- `offers.offer_code` (TEXT, unique)

## Migration Files

### 1. `20250521000000_add_request_offer_codes/migration.sql`
- Adds missing `request_code` and `offer_code` columns
- Creates unique indexes for code columns
- **Idempotent**: Safe to run multiple times

### 2. `20250522000000_enhance_offers_table/migration.sql`
- Adds all new offer columns
- Creates `offer_form_field_values` table
- Adds foreign key constraints and indexes
- **Idempotent**: Safe to run multiple times

## Deployment Steps

### 1. Pre-Deployment Checks

```bash
# Check current migration status
npx prisma migrate status

# Verify database connection
npx prisma db pull
```

### 2. Deploy Migrations

The CI/CD pipeline will automatically run:

```bash
# Generate Prisma client
npx prisma generate

# Deploy migrations (production safe)
npx prisma migrate deploy
```

### 3. Verify Deployment

After deployment, verify the following:

```sql
-- Check if new columns exist
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'offers' 
AND column_name IN ('offer_title', 'category_id', 'subcategory_id', 'short_description', 'discount', 'quantity', 'offer_type');

-- Check if new table exists
SELECT table_name FROM information_schema.tables WHERE table_name = 'offer_form_field_values';

-- Check if code columns exist
SELECT column_name FROM information_schema.columns 
WHERE table_name IN ('requests', 'offers') 
AND column_name IN ('request_code', 'offer_code');
```

## API Changes

### Enhanced Offer Creation API

**Endpoint**: `POST /api/seller/offers`

**New Behavior**:
- `request_id` is now optional
- When `request_id` provided: category/subcategory inherited from request
- When `request_id` not provided: `category_id` and `subcategory_id` required
- Supports dynamic form fields based on subcategory

**New Fields**:
```json
{
  "request_id": "uuid (optional)",
  "offer_title": "string (optional)",
  "category_id": "uuid (optional, required if no request_id)",
  "subcategory_id": "uuid (optional, required if no request_id)",
  "short_description": "string (optional)",
  "discount": "number (optional)",
  "quantity": "number (optional, default: 1)",
  "offer_type": "enum (optional, default: 'service')",
  "form_fields": [
    {
      "form_field_id": "uuid",
      "field_value": "string"
    }
  ]
}
```

### New Public APIs

#### 1. List Public Offers
**Endpoint**: `GET /api/public/offers`

**Query Parameters**:
- `category_id` - Filter by category
- `subcategory_id` - Filter by subcategory
- `offer_type` - Filter by type (digital_product, physical_product, service)
- `min_price`, `max_price` - Price range filter
- `search` - Search in title/description
- `sort_by` - Sort field (created_at, price, offer_title, delivery_time)
- `sort_order` - Sort direction (asc, desc)
- `page`, `limit` - Pagination

#### 2. Get Offer Details
**Endpoint**: `GET /api/public/offers/:id`

#### 3. Get Offers by Seller
**Endpoint**: `GET /api/public/offers/seller/:sellerId`

#### 4. Get Offers by Category
**Endpoint**: `GET /api/public/offers/category/:categoryId`

#### 5. Get Offers by Subcategory
**Endpoint**: `GET /api/public/offers/subcategory/:subcategoryId`

## Backward Compatibility

✅ **Existing functionality preserved**:
- Request-based offer creation still works
- All existing API endpoints unchanged
- Database queries remain compatible

✅ **New functionality added**:
- Standalone offer creation
- Public offer browsing
- Dynamic form fields

## Error Handling

### Common Issues and Solutions

#### 1. Migration Fails
```bash
# Check migration status
npx prisma migrate status

# Reset if needed (CAUTION: Only in development)
npx prisma migrate reset

# Deploy specific migration
npx prisma migrate deploy
```

#### 2. Missing Columns Error
If you see "column does not exist" errors:
- Ensure migrations ran successfully
- Check migration order (codes migration should run before offers enhancement)
- Verify database connection and permissions

#### 3. Foreign Key Constraint Errors
- Ensure referenced tables (categories, subcategories, subcategory_form_fields) exist
- Check data integrity before migration

## Testing

### 1. Test Offer Creation

```bash
# Test request-based offer (existing functionality)
curl -X POST /api/seller/offers \
  -H "Authorization: Bearer <token>" \
  -d '{"request_id": "uuid", "price": 100, "delivery_time": 7}'

# Test standalone offer (new functionality)
curl -X POST /api/seller/offers \
  -H "Authorization: Bearer <token>" \
  -d '{
    "offer_title": "Web Development Service",
    "category_id": "uuid",
    "subcategory_id": "uuid", 
    "price": 500,
    "delivery_time": 14,
    "offer_type": "service"
  }'
```

### 2. Test Public APIs

```bash
# Test public offer listing
curl /api/public/offers?category_id=uuid&page=1&limit=10

# Test offer details
curl /api/public/offers/uuid
```

## Rollback Plan

If issues occur, rollback steps:

1. **Code Rollback**: Deploy previous version
2. **Database Rollback**: 
   ```sql
   -- Remove new columns (if needed)
   ALTER TABLE offers DROP COLUMN IF EXISTS offer_title;
   ALTER TABLE offers DROP COLUMN IF EXISTS category_id;
   -- ... etc
   
   -- Drop new table
   DROP TABLE IF EXISTS offer_form_field_values;
   ```

## Support

For deployment issues:
1. Check migration logs
2. Verify database permissions
3. Ensure all dependencies are installed
4. Contact development team with specific error messages

## Success Criteria

✅ All migrations applied successfully  
✅ New API endpoints respond correctly  
✅ Existing functionality works unchanged  
✅ Database queries perform well  
✅ No data loss or corruption
