const { body } = require('express-validator');

const updateProfileValidation = [
  body('first_name')
    .optional()
    .isString()
    .withMessage('First name must be a string')
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),

  body('last_name')
    .optional()
    .isString()
    .withMessage('Last name must be a string')
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),

  body('email')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail(),

  body('phone_number')
    .optional()
    .isString()
    .withMessage('Phone number must be a string')
    .matches(/^\+?[0-9]{10,15}$/)
    .withMessage('Please provide a valid phone number'),

  body('gender')
    .optional()
    .isString()
    .withMessage('Gender must be a string')
    .isIn(['Male', 'Female', 'Other', ''])
    .withMessage('Gender must be Male, Female, Other, or empty'),

  body('date_of_birth')
    .optional()
    .isISO8601()
    .withMessage('Date of birth must be a valid date')
    .toDate(),

  body('address')
    .optional()
    .isString()
    .withMessage('Address must be a string')
    .isLength({ max: 255 })
    .withMessage('Address must not exceed 255 characters'),

  body('father_name')
    .optional()
    .isString()
    .withMessage('Father name must be a string')
    .isLength({ max: 100 })
    .withMessage('Father name must not exceed 100 characters'),

  body('mother_name')
    .optional()
    .isString()
    .withMessage('Mother name must be a string')
    .isLength({ max: 100 })
    .withMessage('Mother name must not exceed 100 characters'),

  body('national_id_number')
    .optional()
    .isString()
    .withMessage('National ID number must be a string')
    .isLength({ max: 50 })
    .withMessage('National ID number must not exceed 50 characters'),

  body('passport_number')
    .optional()
    .isString()
    .withMessage('Passport number must be a string')
    .isLength({ max: 50 })
    .withMessage('Passport number must not exceed 50 characters'),
];

module.exports = updateProfileValidation;
