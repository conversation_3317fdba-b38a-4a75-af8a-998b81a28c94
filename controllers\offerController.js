const OfferService = require('../services/offerService');
const sendResponse = require('../utils/sendResponse');
const { validationResult } = require('express-validator');
const { formatValidationErrors } = require('../utils/validationFormatter');

class OfferController {
  /**
   * Get all offers with pagination and filtering
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getAllOffers(req, res) {
    try {
      // Check for validation errors if any
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const laravelStyleErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          laravelStyleErrors.message,
          null,
          laravelStyleErrors.errors,
          null,
          422
        );
      }

      // Extract query parameters
      const { status, request_id, seller_id } = req.query;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      // Build filters
      const filters = {};
      if (status) {
        filters.status = status;
      }
      if (request_id) {
        filters.request_id = request_id;
      }
      if (seller_id) {
        filters.seller_id = seller_id;
      }

      // Get offers from service
      const result = await OfferService.getAllOffers(filters, page, limit);

      return sendResponse(
        res,
        true,
        'Offers retrieved successfully',
        result.data,
        null,
        result.meta,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Get offer by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getOfferById(req, res) {
    try {
      const { id } = req.params;
      const offer = await OfferService.getOfferById(id);

      return sendResponse(
        res,
        true,
        'Offer retrieved successfully',
        offer,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Update offer status (approve/reject)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async updateOfferStatus(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const laravelStyleErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          laravelStyleErrors.message,
          null,
          laravelStyleErrors.errors,
          null,
          422
        );
      }

      const { id } = req.params;
      const { status, reason } = req.body;

      // Update the offer status
      const updatedOffer = await OfferService.updateOfferStatus(
        id,
        status,
        req.user.id,
        reason
      );

      // Determine the success message based on the status
      let message = 'Offer status updated successfully';
      if (status === 'Approved') {
        message = 'Offer approved successfully';
      } else if (status === 'Rejected') {
        message = 'Offer rejected successfully';
      }

      return sendResponse(
        res,
        true,
        message,
        updatedOffer,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }
}

module.exports = OfferController;
