const { PrismaClient } = require('@prisma/client');
const SortOrderHelper = require('../utils/sortOrderHelper');
const prisma = new PrismaClient();

const SubCategoryFormFieldModel = {
  /**
   * Create a new form field for a subcategory
   * @param {Object} data - Form field data
   * @returns {Promise<Object>} Created form field
   */
  async createFormField(data) {
    // Auto-assign sort_order if not provided
    const sortOrder = data.sort_order !== undefined ?
      parseInt(data.sort_order) :
      await SortOrderHelper.getNextFormFieldSortOrder(data.subcategory_id);

    return await prisma.subcategory_form_fields.create({
      data: {
        subcategory_id: data.subcategory_id,
        label_name: data.label_name,
        input_type: data.input_type,
        label_subtitle: data.label_subtitle,
        placeholder: data.placeholder,
        is_required: data.is_required || false,
        options: data.options || [],
        default_value: data.default_value,
        validation_regex: data.validation_regex,
        min_value: data.min_value,
        max_value: data.max_value,
        min_length: data.min_length,
        max_length: data.max_length,
        sort_order: sortOrder
      }
    });
  },

  /**
   * Create multiple form fields for a subcategory
   * @param {Array} formFields - Array of form field data
   * @param {String} subcategoryId - Subcategory ID
   * @returns {Promise<Array>} Created form fields
   */
  async createManyFormFields(formFields, subcategoryId) {
    // Get the starting sort order
    let nextSortOrder = await SortOrderHelper.getNextFormFieldSortOrder(subcategoryId);

    const data = formFields.map((field, index) => ({
      subcategory_id: subcategoryId,
      label_name: field.label_name,
      input_type: field.input_type,
      label_subtitle: field.label_subtitle,
      placeholder: field.placeholder,
      is_required: field.is_required || false,
      options: field.options || [],
      default_value: field.default_value,
      validation_regex: field.validation_regex,
      min_value: field.min_value,
      max_value: field.max_value,
      min_length: field.min_length,
      max_length: field.max_length,
      sort_order: field.sort_order !== undefined ?
        parseInt(field.sort_order) :
        nextSortOrder + index
    }));

    return await prisma.subcategory_form_fields.createMany({
      data
    });
  },

  /**
   * Get all form fields for a subcategory
   * @param {String} subcategoryId - Subcategory ID
   * @returns {Promise<Array>} Form fields
   */
  async getFormFieldsBySubcategoryId(subcategoryId) {
    return await prisma.subcategory_form_fields.findMany({
      where: {
        subcategory_id: subcategoryId
      },
      orderBy: {
        sort_order: 'asc'
      }
    });
  },

  /**
   * Get a form field by ID
   * @param {String} id - Form field ID
   * @returns {Promise<Object>} Form field
   */
  async getFormFieldById(id) {
    return await prisma.subcategory_form_fields.findUnique({
      where: { id }
    });
  },

  /**
   * Update a form field
   * @param {String} id - Form field ID
   * @param {Object} data - Form field data to update
   * @returns {Promise<Object>} Updated form field
   */
  async updateFormField(id, data) {
    // Handle sort order change if provided
    if (data.sort_order !== undefined) {
      await SortOrderHelper.updateFormFieldSortOrder(id, parseInt(data.sort_order));
    }

    // Update the form field (excluding sort_order as it's handled above)
    const updateData = {
      label_name: data.label_name,
      input_type: data.input_type,
      label_subtitle: data.label_subtitle,
      placeholder: data.placeholder,
      is_required: data.is_required,
      options: data.options,
      default_value: data.default_value,
      validation_regex: data.validation_regex,
      min_value: data.min_value,
      max_value: data.max_value,
      min_length: data.min_length,
      max_length: data.max_length,
      updated_at: new Date()
    };

    // Don't include sort_order in updateData since it's handled separately above

    return await prisma.subcategory_form_fields.update({
      where: { id },
      data: updateData
    });
  },

  /**
   * Delete a form field
   * @param {String} id - Form field ID
   * @returns {Promise<Object>} Deleted form field
   */
  async deleteFormField(id) {
    // Get the form field to find its subcategory_id for reordering
    const formField = await prisma.subcategory_form_fields.findUnique({
      where: { id },
      select: { subcategory_id: true }
    });

    if (!formField) {
      throw new Error('Form field not found');
    }

    // Delete the form field
    const result = await prisma.subcategory_form_fields.delete({
      where: { id }
    });

    // Reorder remaining form fields in the same subcategory
    await SortOrderHelper.reorderFormFields(formField.subcategory_id, id);

    return result;
  },

  /**
   * Delete all form fields for a subcategory
   * @param {String} subcategoryId - Subcategory ID
   * @returns {Promise<Object>} Result of deletion
   */
  async deleteFormFieldsBySubcategoryId(subcategoryId) {
    return await prisma.subcategory_form_fields.deleteMany({
      where: {
        subcategory_id: subcategoryId
      }
    });
  }
};

module.exports = SubCategoryFormFieldModel;
