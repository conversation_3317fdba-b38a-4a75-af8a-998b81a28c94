const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

const OfferFormFieldValueModel = {
  /**
   * Create form field values for an offer
   * @param {string} offerId - Offer ID
   * @param {Array} formFields - Array of form field values
   * @returns {Promise<Array>} Created form field values
   */
  async createFormFieldValues(offerId, formFields) {
    if (!formFields || formFields.length === 0) {
      return [];
    }

    const data = formFields.map(field => ({
      offer_id: offerId,
      form_field_id: field.form_field_id,
      field_value: field.field_value || null
    }));

    return await prisma.offer_form_field_values.createMany({
      data
    });
  },

  /**
   * Get form field values for an offer
   * @param {string} offerId - Offer ID
   * @returns {Promise<Array>} Form field values with form field details
   */
  async getFormFieldValuesByOfferId(offerId) {
    return await prisma.offer_form_field_values.findMany({
      where: { offer_id: offerId },
      include: {
        form_field: {
          select: {
            id: true,
            label_name: true,
            input_type: true,
            label_subtitle: true,
            placeholder: true,
            is_required: true,
            options: true,
            default_value: true,
            validation_regex: true,
            min_value: true,
            max_value: true,
            min_length: true,
            max_length: true,
            sort_order: true
          }
        }
      },
      orderBy: {
        form_field: {
          sort_order: 'asc'
        }
      }
    });
  },

  /**
   * Update form field values for an offer
   * @param {string} offerId - Offer ID
   * @param {Array} formFields - Array of form field values
   * @returns {Promise<Array>} Updated form field values
   */
  async updateFormFieldValues(offerId, formFields) {
    // Delete existing form field values
    await prisma.offer_form_field_values.deleteMany({
      where: { offer_id: offerId }
    });

    // Create new form field values
    return await this.createFormFieldValues(offerId, formFields);
  },

  /**
   * Delete form field values for an offer
   * @param {string} offerId - Offer ID
   * @returns {Promise<Object>} Delete result
   */
  async deleteFormFieldValues(offerId) {
    return await prisma.offer_form_field_values.deleteMany({
      where: { offer_id: offerId }
    });
  },

  /**
   * Validate form field values against form field definitions
   * @param {string} subcategoryId - Subcategory ID
   * @param {Array} formFieldValues - Array of form field values
   * @returns {Promise<Object>} Validation result
   */
  async validateFormFieldValues(subcategoryId, formFieldValues) {
    // Get form field definitions for the subcategory
    const formFields = await prisma.subcategory_form_fields.findMany({
      where: { subcategory_id: subcategoryId },
      orderBy: { sort_order: 'asc' }
    });

    const errors = [];
    const validatedValues = [];

    for (const formField of formFields) {
      const providedValue = formFieldValues.find(
        fv => fv.form_field_id === formField.id
      );

      // Check required fields
      if (formField.is_required && (!providedValue || !providedValue.field_value)) {
        errors.push({
          field: formField.label_name,
          message: `${formField.label_name} is required`
        });
        continue;
      }

      if (providedValue && providedValue.field_value) {
        const value = providedValue.field_value;

        // Validate based on input type
        switch (formField.input_type) {
          case 'NUMBER':
            if (isNaN(value)) {
              errors.push({
                field: formField.label_name,
                message: `${formField.label_name} must be a number`
              });
            } else {
              const numValue = parseFloat(value);
              if (formField.min_value && numValue < formField.min_value) {
                errors.push({
                  field: formField.label_name,
                  message: `${formField.label_name} must be at least ${formField.min_value}`
                });
              }
              if (formField.max_value && numValue > formField.max_value) {
                errors.push({
                  field: formField.label_name,
                  message: `${formField.label_name} must be at most ${formField.max_value}`
                });
              }
            }
            break;

          case 'EMAIL':
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
              errors.push({
                field: formField.label_name,
                message: `${formField.label_name} must be a valid email address`
              });
            }
            break;

          case 'SELECT':
          case 'RADIO':
            if (formField.options && formField.options.length > 0) {
              if (!formField.options.includes(value)) {
                errors.push({
                  field: formField.label_name,
                  message: `${formField.label_name} must be one of: ${formField.options.join(', ')}`
                });
              }
            }
            break;

          case 'TEXT':
          case 'TEXTAREA':
            if (formField.min_length && value.length < formField.min_length) {
              errors.push({
                field: formField.label_name,
                message: `${formField.label_name} must be at least ${formField.min_length} characters`
              });
            }
            if (formField.max_length && value.length > formField.max_length) {
              errors.push({
                field: formField.label_name,
                message: `${formField.label_name} must be at most ${formField.max_length} characters`
              });
            }
            break;
        }

        // Custom regex validation
        if (formField.validation_regex) {
          const regex = new RegExp(formField.validation_regex);
          if (!regex.test(value)) {
            errors.push({
              field: formField.label_name,
              message: `${formField.label_name} format is invalid`
            });
          }
        }

        validatedValues.push({
          form_field_id: formField.id,
          field_value: value
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      validatedValues
    };
  }
};

module.exports = OfferFormFieldValueModel;
