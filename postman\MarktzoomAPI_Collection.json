{"info": {"_postman_id": "marktzoom-api-collection", "name": "Marktzoom API Collection", "description": "Complete API collection for Marktzoom backend including Auth, Buyer, and Seller routes", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "{{baseUrl}}"}], "item": [{"name": "Auth Routes", "item": [{"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"confirm_password\": \"password123\",\n  \"user_type\": \"personal\",\n  \"phone_number\": \"1234567890\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}}}, {"name": "Mobile Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"confirm_password\": \"password123\",\n  \"user_type\": \"personal\",\n  \"phone_number\": \"0987654321\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/mobile/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "mobile", "register"]}}}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"otp\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/verify/otp", "host": ["{{baseUrl}}"], "path": ["api", "auth", "verify", "otp"]}}}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.access_token) {", "        pm.environment.set('token', response.data.access_token);", "        console.log('Token saved to environment');", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refresh_token\": \"your_refresh_token_here\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/refresh-token", "host": ["{{baseUrl}}"], "path": ["api", "auth", "refresh-token"]}}}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/forgot-password", "host": ["{{baseUrl}}"], "path": ["api", "auth", "forgot-password"]}}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"reset_token_here\",\n  \"password\": \"newpassword123\",\n  \"confirm_password\": \"newpassword123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/reset-password", "host": ["{{baseUrl}}"], "path": ["api", "auth", "reset-password"]}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"verification_token_here\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/verify-email", "host": ["{{baseUrl}}"], "path": ["api", "auth", "verify-email"]}}}, {"name": "New User Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"confirm_password\": \"password123\",\n  \"user_type\": \"business\",\n  \"phone_number\": \"1122334455\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/new-register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "new-register"]}}}, {"name": "New User Login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/new-login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "new-login"]}}}, {"name": "Logout", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/logout", "host": ["{{baseUrl}}"], "path": ["api", "auth", "logout"]}}}, {"name": "Change Password", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"current_password\": \"password123\",\n  \"new_password\": \"newpassword456\",\n  \"confirm_password\": \"newpassword456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/change-password", "host": ["{{baseUrl}}"], "path": ["api", "auth", "change-password"]}}}, {"name": "Get Authenticated User", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/user", "host": ["{{baseUrl}}"], "path": ["api", "auth", "user"]}}}, {"name": "Get User Profile", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/profile", "host": ["{{baseUrl}}"], "path": ["api", "auth", "profile"]}}}, {"name": "Complete Profile", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "first_name", "value": "<PERSON>", "type": "text"}, {"key": "last_name", "value": "<PERSON><PERSON>", "type": "text"}, {"key": "phone_number", "value": "1234567890", "type": "text"}, {"key": "country", "value": "USA", "type": "text"}, {"key": "city", "value": "New York", "type": "text"}, {"key": "profile_picture", "type": "file", "src": []}, {"key": "nid_document", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/api/auth/complete-profile", "host": ["{{baseUrl}}"], "path": ["api", "auth", "complete-profile"]}}}, {"name": "Get Profile Status", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/profile-status", "host": ["{{baseUrl}}"], "path": ["api", "auth", "profile-status"]}}}, {"name": "Upload Profile Picture", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "profile_picture", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/api/auth/upload-profile-picture", "host": ["{{baseUrl}}"], "path": ["api", "auth", "upload-profile-picture"]}}}]}]}