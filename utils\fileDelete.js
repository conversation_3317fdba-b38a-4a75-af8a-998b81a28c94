const fs = require('fs');
const path = require('path');

/**
 * Delete a file from the server
 * @param {string} filePath - Path to the file to delete (relative to public folder)
 * @returns {boolean} - True if file was deleted, false otherwise
 */
function deleteFile(filePath) {
  try {
    // If the path starts with /public, remove it
    const cleanPath = filePath.replace(/^\/public\//, '');
    
    // Construct the full path
    const fullPath = path.join(__dirname, '..', 'public', cleanPath);
    
    // Check if file exists
    if (fs.existsSync(fullPath)) {
      // Delete the file
      fs.unlinkSync(fullPath);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error deleting file ${filePath}:`, error);
    return false;
  }
}

module.exports = { deleteFile };
