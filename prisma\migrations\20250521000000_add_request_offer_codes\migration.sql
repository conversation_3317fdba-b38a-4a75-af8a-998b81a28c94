-- Add request_code and offer_code columns if they don't exist
-- This migration is idempotent and safe for CI/CD deployment

-- Add request_code column to requests table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'requests' 
        AND column_name = 'request_code'
    ) THEN
        ALTER TABLE "requests" ADD COLUMN "request_code" TEXT;
    END IF;
END $$;

-- Add offer_code column to offers table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'offers' 
        AND column_name = 'offer_code'
    ) THEN
        ALTER TABLE "offers" ADD COLUMN "offer_code" TEXT;
    END IF;
END $$;

-- Create unique indexes for the code columns if they don't exist
CREATE UNIQUE INDEX IF NOT EXISTS "requests_request_code_key" ON "requests"("request_code");
CREATE UNIQUE INDEX IF NOT EXISTS "offers_offer_code_key" ON "offers"("offer_code");

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "requests_request_code_idx" ON "requests"("request_code");
CREATE INDEX IF NOT EXISTS "offers_offer_code_idx" ON "offers"("offer_code");

-- Add comments for documentation
COMMENT ON COLUMN "requests"."request_code" IS 'Auto-generated unique code for the request';
COMMENT ON COLUMN "offers"."offer_code" IS 'Auto-generated unique code for the offer';
