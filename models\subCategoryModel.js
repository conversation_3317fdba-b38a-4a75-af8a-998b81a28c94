const { PrismaClient } = require('@prisma/client');
const SortOrderHelper = require('../utils/sortOrderHelper');
const prisma = new PrismaClient();

const SubCategoryModel = {
  async createSubCategory(data) {
    // Auto-assign sort_order if not provided
    const sortOrder = data.sort_order !== undefined ?
      data.sort_order :
      await SortOrderHelper.getNextSubCategorySortOrder(data.category_id);

    return await prisma.sub_categories.create({
      data: {
        category_id: data.category_id,
        title: data.title,
        description: data.description,
        color: data.color,
        image: data.image,
        thumbnail: data.thumbnail,
        is_featured: data.is_featured || false,
        sort_order: sortOrder,
        seo_title: data.seo_title,
        seo_description: data.seo_description,
        seo_keywords: data.seo_keywords,
        translations: {
          create: data.translations?.map(translation => ({
            language: translation.language,
            title: translation.title,
            description: translation.description,
            seo_title: translation.seo_title,
            seo_description: translation.seo_description,
            seo_keywords: translation.seo_keywords
          })) || []
        }
      },
      include: {
        translations: true,
        category: true,
        form_fields: true
      }
    });
  },

  async findSubCategoryById(id) {
    return await prisma.sub_categories.findUnique({
      where: { id },
      include: {
        translations: true,
        category: true,
        form_fields: {
          orderBy: {
            sort_order: 'asc'
          }
        }
      }
    });
  },

  async updateSubCategory(id, data) {
    // Handle sort order change if provided
    if (data.sort_order !== undefined) {
      await SortOrderHelper.updateSubCategorySortOrder(id, data.sort_order);
    }

    // Handle translations if they exist
    if (data.translations) {
      // Delete existing translations
      await prisma.sub_category_translations.deleteMany({
        where: { sub_category_id: id }
      });

      // Create new translations
      await prisma.sub_category_translations.createMany({
        data: data.translations.map(t => ({
          ...t,
          sub_category_id: id
        }))
      });
    }

    // Update the main subcategory fields (excluding sort_order as it's handled above)
    const updateData = {
      title: data.title,
      description: data.description,
      color: data.color,
      image: data.image,
      thumbnail: data.thumbnail,
      is_featured: data.is_featured,
      seo_title: data.seo_title,
      seo_description: data.seo_description,
      seo_keywords: data.seo_keywords,
      updated_at: new Date()
    };

    // Don't include sort_order in updateData since it's handled separately above

    return await prisma.sub_categories.update({
      where: { id },
      data: updateData,
      include: {
        translations: true,
        category: true,
        form_fields: {
          orderBy: {
            sort_order: 'asc'
          }
        }
      }
    });
  },

  async deleteSubCategory(id) {
    // Get the subcategory to find its category_id for reordering
    const subcategory = await prisma.sub_categories.findUnique({
      where: { id },
      select: { category_id: true }
    });

    if (!subcategory) {
      throw new Error('Subcategory not found');
    }

    // First delete translations
    await prisma.sub_category_translations.deleteMany({
      where: { sub_category_id: id }
    });

    // Then delete the subcategory
    const result = await prisma.sub_categories.delete({
      where: { id },
      include: {
        translations: true,
        form_fields: true
      }
    });

    // Reorder remaining subcategories in the same category
    await SortOrderHelper.reorderSubCategories(subcategory.category_id, id);

    return result;
  }
};

module.exports = SubCategoryModel;