const express = require('express');
const router = express.Router();
const PublicOfferController = require('../controllers/publicOfferController');
const {
  filterPublicOffers,
  offerIdParam,
  sellerIdParam,
  categoryIdParam,
  subcategoryIdParam,
  paginationValidation
} = require('../validations/publicOfferValidations');

/**
 * @route GET /api/public/offers
 * @desc Get public offers with filtering and pagination
 * @access Public
 */
router.get('/', filterPublicOffers, PublicOfferController.getPublicOffers);

/**
 * @route GET /api/public/offers/:id
 * @desc Get offer details by ID
 * @access Public
 */
router.get('/:id', offerIdParam, PublicOfferController.getOfferDetails);

/**
 * @route GET /api/public/offers/seller/:sellerId
 * @desc Get offers by seller
 * @access Public
 */
router.get('/seller/:sellerId', [...sellerIdParam, ...paginationValidation], PublicOfferController.getOffersBySeller);

/**
 * @route GET /api/public/offers/category/:categoryId
 * @desc Get offers by category
 * @access Public
 */
router.get('/category/:categoryId', [...categoryIdParam, ...paginationValidation], PublicOfferController.getOffersByCategory);

/**
 * @route GET /api/public/offers/subcategory/:subcategoryId
 * @desc Get offers by subcategory
 * @access Public
 */
router.get('/subcategory/:subcategoryId', [...subcategoryIdParam, ...paginationValidation], PublicOfferController.getOffersBySubcategory);

module.exports = router;
