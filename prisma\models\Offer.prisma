model offers {
  id                   String   @id @default(uuid())
  offer_code           String?  @unique
  request_id           String?  // Made optional
  seller_id            String
  offer_title          String?
  category_id          String?
  subcategory_id       String?
  short_description    String?
  price                Float
  discount             Float?
  quantity             Int      @default(1)
  delivery_time        Int
  message              String?
  description          String?
  offer_type           offer_type_enum @default(service)
  status               String   @default("Pending")
  created_at           DateTime @default(now())
  updated_at           DateTime @updatedAt
  is_deleted           Boolean  @default(false)

  // Relations
  request              requests? @relation(fields: [request_id], references: [id], name: "RequestOffer")
  seller               users @relation(fields: [seller_id], references: [id], name: "Seller")
  category             categories? @relation(fields: [category_id], references: [id], name: "OfferCategory")
  subcategory          sub_categories? @relation(fields: [subcategory_id], references: [id], name: "OfferSubCategory")

  // Child Relations
  offer_attachments    offer_attachments[]
  offer_status_changes offer_status_changes[]
  offer_negotiations   offer_negotiations[]
  offer_form_field_values offer_form_field_values[]
  orders         orders[] @relation("OfferOrder")
  cart_items     cart_items[] @relation("CartOffer")

  @@index([category_id])
  @@index([subcategory_id])
  @@index([offer_type])
  @@index([status])
}

model offer_attachments {
  id              String   @id @default(uuid())
  offer_id        String
  uploaded_by     String
  file_path       String
  file_type       String
  file_size       Int?
  description     String?
  is_public       Boolean  @default(false)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  is_deleted      Boolean  @default(false)

  // Relations
  offer           offers   @relation(fields: [offer_id], references: [id])
}

model offer_status_changes {
  id              String   @id @default(uuid())
  offer_id        String
  updated_by      String
  status          String   @default("Pending")
  previous_status String?
  reason          String?
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  // Relations
  offer           offers   @relation(fields: [offer_id], references: [id])
  updated_by_user users    @relation(fields: [updated_by], references: [id], name: "OfferStatusUpdator")
}

model offer_negotiations {
  id                     String   @id @default(uuid())
  offer_id               String
  sender_id              String
  recipient_id           String
  message                String?
  proposed_price         Float?
  proposed_delivery_time Int?
  status                 String   @default("Pending")
  created_at             DateTime @default(now())
  updated_at             DateTime @updatedAt

  // Relations
  offer                  offers   @relation(fields: [offer_id], references: [id])
  sender       users    @relation(name: "Sender", fields: [sender_id], references: [id])
  recipient    users    @relation(name: "Recipient", fields: [recipient_id], references: [id])
}


