const { body, validationResult } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

const createRequestValidation = [
  // Required fields

  body('category_id').notEmpty().withMessage('Category ID is required'),
  body('sub_category_id').notEmpty().withMessage('Sub-category ID is required'),
  body('title')
    .notEmpty().withMessage('Title is required')
    .isLength({ max: 255 }).withMessage('Title must be less than 255 characters'),
  
  // Optional fields with validation
  body('short_description').optional().isString().withMessage('Description must be a string'),
  body('description').optional().isString().withMessage('Description must be a string'),
  body('quantity')
    .optional()
    .isInt({ min: 1 }).withMessage('Quantity must be a positive integer')
    .default(1),
  body('budget_min')
    .optional()
    .isFloat({ min: 0 }).withMessage('Minimum budget must be a positive number')
    .default(0),
  body('budget_max')
    .optional()
    .isFloat({ min: 0 }).withMessage('Maximum budget must be a positive number')
    .default(0)
    .custom((value, { req }) => {
      if (value < req.body.budget_min) {
        throw new Error('Maximum budget must be greater than or equal to minimum budget');
      }
      return true;
    }),
  body('deadline')
    .optional()
    .isISO8601().withMessage('Deadline must be a valid date')
    .custom(value => {
      if (new Date(value) < new Date()) {
        throw new Error('Deadline must be in the future');
      }
      return true;
    }),
  body('urgency')
    .optional()
    .isIn(['Low', 'Normal', 'High', 'Urgent']).withMessage('Invalid urgency level')
    .default('Normal'),
  body('status')
    .optional()
    .isIn(['Pending', 'Active', 'Completed', 'Cancelled']).withMessage('Invalid status')
    .default('Pending'),
  body('request_type')
    .optional()
    .isIn(['General', 'Service', 'Product']).withMessage('Invalid request type')
    .default('General'),
  body('location').optional().isString().withMessage('Location must be a string'),
  body('additional_info').optional().isString().withMessage('Additional info must be a string'),
  // body('service_period')
  //   .optional()
  //   .isInt({ min: 1 }).withMessage('Service period must be a positive integer'),
  // body('session_count')
  //   .optional()
  //   .isInt({ min: 1 }).withMessage('Session count must be a positive integer'),
  
  // Validate relational fields exist

  body('category_id').custom(async (value) => {
    const category = await prisma.categories.findUnique({
      where: { id: value }
    });
    if (!category) {
      throw new Error('Category not found');
    }
  }),
  body('sub_category_id').custom(async (value, { req }) => {
    const subCategory = await prisma.sub_categories.findUnique({
      where: { id: value }
    });
    if (!subCategory) {
      throw new Error('Sub-category not found');
    }
    // Optional: Validate sub-category belongs to category
    if (subCategory.category_id !== req.body.category_id) {
      throw new Error('Sub-category does not belong to the specified category');
    }
  }),

  // Handle validation errors
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  }
];

module.exports = createRequestValidation;