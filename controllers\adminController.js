const adminService = require('../services/adminService.js');
const ApiError = require('../utils/apiError');
const sendResponse = require('../utils/sendResponse');
const { validationResult } = require('express-validator');
const { formatValidationErrors } = require('../utils/validationFormatter');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { saveBufferToFile } = require('../utils/fileUpload');

class AdminController {
  // Login as Admin
  async login(req, res) {
    try {
      const { email, password } = req.body;
      if (!email || !password) {
        throw new ApiError(400, 'Email and password are required');
      }

      const admin = await adminService.login(email, password);
      sendResponse(res, true, 'Login successful', admin);
    } catch (err) {
      const status = err.statusCode || 500;

      // Format error response for better client handling
      const errorMessage = err.message;
      const errorData = { general: [errorMessage] };

      sendResponse(res, false, errorMessage, null, errorData, null, status);
    }
  }


  async refreshToken(req, res) {
    try {
      const { token } = req.body;
      if (!token) {
        return sendResponse(res, false, 'Refresh token is required', null, null, null, 400);
      }

      const result = await adminService.refreshToken(token);
      sendResponse(res, true, 'Token refreshed successfully', result);
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  async getAllUsers(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const formattedErrors = {};
        errors.array().forEach(error => {
          formattedErrors[error.path] = error.msg;
        });
        return sendResponse(res, false, 'Validation failed', null, formattedErrors, null, 422);
      }

      const { page = 1, limit = 10, role = 'Buyer', search = '', status, is_approved } = req.query;
      const usersData = await adminService.getAllUsers(page, limit, role, search, status, is_approved);

      sendResponse(
        res,
        true,
        'Users fetched successfully',
        usersData.data,
        null,
        usersData.meta
      );
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }



  // Get specific user details
  async getUserById(req, res) {
    try {
      const user = await adminService.getUserById(req.params.id);
      sendResponse(res, true, 'User fetched successfully', user);
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Get specific user details with roles
  async getUserByIdWithRoles(req, res) {
    try {
      const user = await adminService.getUserByIdWithRoles(req.params.id);
      sendResponse(res, true, 'User with roles fetched successfully', user);
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Update user role (e.g., approve Seller)
  async updateUserRole(req, res) {
    try {
      // Check if we're receiving a single role string or an array of roles
      let roles;
      if (req.body.roles) {
        // If roles is already an array, use it
        roles = req.body.roles;
      } else if (req.body.role) {
        // If we have a single role string, convert it to an array
        roles = [req.body.role];
      } else {
        throw new ApiError(400, 'Roles are required');
      }

      const updatedUser = await adminService.updateUserRole(
        req.params.id,
        roles,
        req.user.id
      );
      sendResponse(res, true, 'User roles updated successfully', updatedUser);
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Update user data
  async updateUser(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const laravelStyleErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          laravelStyleErrors.message,
          null,
          laravelStyleErrors.errors,
          null,
          422
        );
      }

      console.log('Request body:', req.body);
      console.log('Request file:', req.file);

      // Create a copy of the request body to avoid modifying the original
      let userData = { ...req.body };

      // Remove form-specific fields that shouldn't be sent to the database
      delete userData._method;

      // Handle avatar upload if present
      if (req.file) {
        console.log('Processing file upload:', req.file.originalname);

        // Validate image file
        const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedMimeTypes.includes(req.file.mimetype)) {
          return sendResponse(
            res,
            false,
            'Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.',
            null,
            { avatar: ['Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.'] },
            null,
            422
          );
        }

        // Check file size (max 5MB)
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (req.file.size > maxSize) {
          return sendResponse(
            res,
            false,
            'File size exceeds the limit of 5MB.',
            null,
            { avatar: ['File size exceeds the limit of 5MB.'] },
            null,
            422
          );
        }

        // Save the avatar file and get the URL
        userData.profile_picture_url = saveBufferToFile(
          req.file.buffer,
          req.file.originalname,
          'uploads/avatars'
        );
        console.log('Saved file path:', userData.profile_picture_url);
      }

      // Convert date_of_birth to Date object if it's a string
      if (userData.date_of_birth && typeof userData.date_of_birth === 'string') {
        userData.date_of_birth = new Date(userData.date_of_birth);
      }

      const updatedUser = await adminService.updateUser(
        req.params.id,
        userData,
        req.user ? req.user.id : null
      );

      sendResponse(res, true, 'User updated successfully', updatedUser);
    } catch (err) {
      console.error('Error in updateUser:', err);

      // Format API errors in Laravel style
      let statusCode = err.statusCode || 400;

      // Check if it's a validation error
      if (err.field || err.message.includes('validation') ||
          err.message.includes('already been taken') ||
          err.message.includes('already in use') ||
          err.message.includes('invalid')) {
        statusCode = 422; // Unprocessable Entity for validation errors
      }

      const errorResponse = {
        message: err.message || 'An error occurred',
        errors: {}
      };

      // If it's a specific field error
      if (err.field) {
        errorResponse.errors[err.field] = [err.message];
      } else {
        // General error
        errorResponse.errors.general = [err.message || 'An error occurred while updating the user'];
      }

      sendResponse(
        res,
        false,
        errorResponse.message,
        null,
        errorResponse.errors,
        null,
        statusCode
      );
    }
  }

  // Delete user
  async deleteUser(req, res) {
    try {
      await adminService.deleteUser(req.params.id, req.user.id);
      sendResponse(res, true, 'User deleted successfully');
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Get all available roles
  async getAllRoles(req, res) {
    try {
      const roles = await adminService.getAllRoles();
      sendResponse(res, true, 'Roles fetched successfully', roles);
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Update user role (Super Admin only)
  async updateRole(req, res) {
    try {
      const updatedRole = await adminService.updateRole(
        req.params.id,
        req.body.roleName,
        req.user.id
      );
      sendResponse(res, true, 'Role updated successfully', updatedRole);
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Get user activity logs
  async getActivityLogs(req, res) {
    try {
      const logs = await adminService.getActivityLogs();
      sendResponse(res, true, 'Activity logs fetched successfully', logs);
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Get all categories
  async getCategories(req, res) {
    try {
      const categories = await adminService.getCategories();
      sendResponse(res, true, 'Categories fetched successfully', categories);
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Create a new category
  async createCategory(req, res) {
    try {
      const category = await adminService.createCategory(
        req.body.name,
        req.user.id
      );
      sendResponse(res, true, 'Category created successfully', category, null, null, 201);
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Update a category
  async updateCategory(req, res) {
    try {
      const updatedCategory = await adminService.updateCategory(
        req.params.id,
        req.body.name,
        req.user.id
      );
      sendResponse(res, true, 'Category updated successfully', updatedCategory);
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Delete a category
  async deleteCategory(req, res) {
    try {
      await adminService.deleteCategory(req.params.id, req.user.id);
      sendResponse(res, true, 'Category deleted successfully');
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Get all subcategories of a category
  async getSubcategories(req, res) {
    try {
      const subcategories = await adminService.getSubcategories(req.params.id);
      sendResponse(res, true, 'Subcategories fetched successfully', subcategories);
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Create a new subcategory
  async createSubcategory(req, res) {
    try {
      const subcategory = await adminService.createSubcategory(
        req.body.name,
        req.params.id,
        req.user.id
      );
      sendResponse(res, true, 'Subcategory created successfully', subcategory, null, null, 201);
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Update a subcategory
  async updateSubcategory(req, res) {
    try {
      const updatedSubcategory = await adminService.updateSubcategory(
        req.params.id,
        req.body.name,
        req.user.id
      );
      sendResponse(res, true, 'Subcategory updated successfully', updatedSubcategory);
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Delete a subcategory
  async deleteSubcategory(req, res) {
    try {
      await adminService.deleteSubcategory(req.params.id, req.user.id);
      sendResponse(res, true, 'Subcategory deleted successfully');
    } catch (err) {
      sendResponse(res, false, err.message, null, err, null, 400);
    }
  }

  // Create a new user
  async createUser(req, res) {
    try {
      console.log('Request body:', req.body);
      console.log('Request file:', req.file);

      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const laravelStyleErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          laravelStyleErrors.message,
          null,
          laravelStyleErrors.errors,
          null,
          422
        );
      }
      let data = req.body;

      // Handle avatar upload if present
      if (req.file) {
        console.log('Processing file upload:', req.file.originalname);
        // Save the avatar file and get the URL
        data.profile_picture_url = saveBufferToFile(
          req.file.buffer,
          req.file.originalname,
          'uploads/avatars'
        );
        console.log('Saved file path:', data.profile_picture_url);
      }

      const user = await adminService.createUser(data, req.user.id);

      // Remove sensitive data
      if (user) {
        delete user.password_hash;
      }

      const roleName = req.body.role || 'User';
      sendResponse(res, true, `${roleName} created successfully`, user, null, null, 201);
    } catch (err) {
      // Format API errors in Laravel style too
      // Use 422 for validation errors, otherwise use the error's status code or 400
      let statusCode = 400;

      // Check if it's a validation error
      if (err.field || err.message.includes('validation') ||
          err.message.includes('already been taken') ||
          err.message.includes('already in use') ||
          err.message.includes('invalid')) {
        statusCode = 422; // Unprocessable Entity for validation errors
      } else if (err.statusCode) {
        statusCode = err.statusCode;
      }

      const errorResponse = {
        message: err.message || 'An error occurred',
        errors: {}
      };

      // If it's a specific field error (like email already exists)
      if (err.field) {
        errorResponse.errors[err.field] = [err.message];
      } else {
        // General error
        errorResponse.errors.general = [err.message];
      }

      sendResponse(res, false, err.message, null, err, null, status);
    }
  }

  // Update user password
  async updateUserPassword(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const formattedErrors = formatValidationErrors(errors.array());
      return sendResponse(res, false, "Validation failed", null, formattedErrors.errors, null, 422);
    }

    try {
      const userId = req.params.id;
      const { password } = req.body;
      const adminId = req.user.id;

      const result = await adminService.updateUserPassword(userId, password, adminId);
      sendResponse(res, true, "User password updated successfully", result);
    } catch (err) {
      const status = err.statusCode || 500;
      sendResponse(res, false, err.message, null, err, null, status);
    }
  }
}

module.exports = new AdminController();