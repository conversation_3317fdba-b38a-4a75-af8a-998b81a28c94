const { body, param } = require('express-validator');

const bulkUploadValidations = {
  // Validate bulk upload file
  uploadSubcategoriesFile: [
    // File validation is handled by multer middleware
    // Additional validation can be added here if needed
  ],

  // Validate template download request
  downloadTemplate: [
    param('format')
      .isIn(['csv', 'excel'])
      .withMessage('Format must be either csv or excel')
  ],

  // Validate bulk upload preview request
  previewUpload: [
    // File validation is handled by multer middleware
    // The actual data validation is done in the controller using CSVProcessor
  ]
};

module.exports = bulkUploadValidations;
