const { body } = require('express-validator');

const confirmOrderValidation = [
  // Billing Information
  body('billing_info.full_name')
    .notEmpty()
    .withMessage('Full name is required')
    .isString()
    .withMessage('Full name must be a string')
    .isLength({ min: 3, max: 100 })
    .withMessage('Full name must be between 3 and 100 characters'),
  
  body('billing_info.email')
    .notEmpty()
    .withMessage('Email is required')
    .isEmail()
    .withMessage('Invalid email format'),
  
  body('billing_info.address')
    .optional()
    .isString()
    .withMessage('Address must be a string'),
  
  body('billing_info.city')
    .notEmpty()
    .withMessage('City is required')
    .isString()
    .withMessage('City must be a string'),
  
  body('billing_info.state')
    .notEmpty()
    .withMessage('State is required')
    .isString()
    .withMessage('State must be a string'),
  
  // Card Information
  body('card_info.card_number')
    .notEmpty()
    .withMessage('Card number is required')
    .isCreditCard()
    .withMessage('Invalid credit card number'),
  
  body('card_info.expiry_month')
    .notEmpty()
    .withMessage('Expiry month is required')
    .isInt({ min: 1, max: 12 })
    .withMessage('Expiry month must be between 1 and 12'),
  
  body('card_info.expiry_year')
    .notEmpty()
    .withMessage('Expiry year is required')
    .isInt({ min: new Date().getFullYear(), max: new Date().getFullYear() + 20 })
    .withMessage('Invalid expiry year'),
  
  body('card_info.cvv')
    .notEmpty()
    .withMessage('CVV is required')
    .isInt()
    .withMessage('CVV must be a number')
    .isLength({ min: 3, max: 4 })
    .withMessage('CVV must be 3 or 4 digits'),
  
  // Order Information
  body('cart_id')
    .notEmpty()
    .withMessage('Cart ID is required')
    .isUUID()
    .withMessage('Invalid cart ID format'),
  
  body('amount')
    .notEmpty()
    .withMessage('Amount is required')
    .isFloat({ min: 0.01 })
    .withMessage('Amount must be a positive number'),
];

module.exports = confirmOrderValidation;
