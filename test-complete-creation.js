const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

async function testCompleteCreation() {
  try {
    console.log('🧪 Testing Complete Subcategory and Form Fields Creation...\n');

    // Step 1: Get admin token
    console.log('1️⃣ Getting admin token...');
    
    const loginResponse = await axios.post('http://localhost:5000/api/admin/login', {
      email: '<EMAIL>',
      password: 'NewAdmin2024!'
    });

    if (!loginResponse.data.success) {
      console.log('❌ Admin login failed');
      return;
    }

    const token = loginResponse.data.data.accessToken;
    console.log('✅ Admin token obtained');

    // Step 2: Create proper CSV with category_title
    console.log('\n2️⃣ Creating proper CSV with category_title...');
    
    // Your data with the missing category_title added
    const csvContent = `category_title,subcategory_title,subcategory_description,subcategory_color,is_featured,seo_title,seo_description,seo_keywords,label_name,input_type,label_subtitle,placeholder,is_required,options,default_value,validation_regex,min_value,max_value,min_length,max_length
Technology,Web Development,Professional web development services,#3498db,true,Web Development Services,Professional web development services for your business,web development website programming,,,,,,,,,,,
,,,,,,,,Preferred Language,text,,Enter preferred programming language,true,,,,,,,2,30
,,,,,,,,Experience Level,select,,,true,"Beginner,Intermediate,Expert",Intermediate,,,,,`;

    fs.writeFileSync('complete-test.csv', csvContent);
    console.log('✅ CSV created with proper structure');
    console.log('   - First row: Subcategory data with category_title = "Technology"');
    console.log('   - Second row: Form field "Preferred Language" (TEXT)');
    console.log('   - Third row: Form field "Experience Level" (SELECT)');

    // Step 3: Upload and process
    console.log('\n3️⃣ Uploading and processing...');
    
    const formData = new FormData();
    formData.append('file', fs.createReadStream('complete-test.csv'));

    try {
      const uploadResponse = await axios.post('http://localhost:5000/api/admin/bulk-upload/subcategories', formData, {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${token}`
        },
        timeout: 30000
      });

      console.log('✅ Upload successful!');
      console.log('\n📊 Results:');
      console.log(`   Status: ${uploadResponse.status}`);
      console.log(`   Success: ${uploadResponse.data.success}`);
      console.log(`   Message: ${uploadResponse.data.message}`);

      if (uploadResponse.data.data) {
        const data = uploadResponse.data.data;
        
        console.log('\n📈 Summary:');
        console.log(`   Total Processed: ${data.summary.total_processed}`);
        console.log(`   Subcategories Created: ${data.summary.subcategories_created}`);
        console.log(`   Form Fields Created: ${data.summary.form_fields_created}`);
        console.log(`   Errors: ${data.summary.errors_count}`);

        // Show created subcategory details
        if (data.created_subcategories.length > 0) {
          console.log('\n🏗️ Created Subcategory:');
          const subcategory = data.created_subcategories[0];
          console.log(`   ID: ${subcategory.id}`);
          console.log(`   Title: ${subcategory.title}`);
          console.log(`   Description: ${subcategory.description}`);
          console.log(`   Color: ${subcategory.color}`);
          console.log(`   Featured: ${subcategory.is_featured}`);
          console.log(`   SEO Title: ${subcategory.seo_title}`);
          console.log(`   Category: ${subcategory.category?.title || 'N/A'}`);
        }

        // Show created form fields details
        if (data.created_form_fields.length > 0) {
          console.log('\n📝 Created Form Fields:');
          data.created_form_fields.forEach((field, index) => {
            console.log(`   ${index + 1}. ${field.label_name}`);
            console.log(`      Type: ${field.input_type}`);
            console.log(`      Required: ${field.is_required}`);
            console.log(`      Placeholder: ${field.placeholder || 'N/A'}`);
            if (field.options && field.options.length > 0) {
              console.log(`      Options: ${field.options.join(', ')}`);
            }
            if (field.default_value) {
              console.log(`      Default: ${field.default_value}`);
            }
            if (field.min_length || field.max_length) {
              console.log(`      Length: ${field.min_length || 'N/A'} - ${field.max_length || 'N/A'}`);
            }
            console.log(`      Sort Order: ${field.sort_order}`);
            console.log('');
          });
        }

        // Show any errors
        if (data.errors.length > 0) {
          console.log('\n⚠️ Errors:');
          data.errors.forEach((error, index) => {
            console.log(`   ${index + 1}. ${error}`);
          });
        }
      }

    } catch (uploadError) {
      if (uploadError.response) {
        console.log('❌ Upload failed');
        console.log(`   Status: ${uploadError.response.status}`);
        console.log(`   Message: ${uploadError.response.data.message}`);
        
        if (uploadError.response.data.data) {
          const errorData = uploadError.response.data.data;
          if (errorData.errors) {
            console.log('\n⚠️ Errors:');
            errorData.errors.forEach((error, index) => {
              console.log(`   ${index + 1}. ${error}`);
            });
          }
        }
        
        // Check if it's a duplicate error (expected)
        if (uploadError.response.status === 400 && 
            uploadError.response.data.message.includes('already exists')) {
          console.log('\n💡 This is expected if the subcategory already exists');
          console.log('   Try with a different subcategory name to test creation');
        }
      } else {
        console.log('❌ Request failed:', uploadError.message);
      }
    }

    // Step 4: Clean up
    console.log('\n4️⃣ Cleaning up...');
    fs.unlinkSync('complete-test.csv');
    console.log('✅ Test file cleaned up');

    console.log('\n🎉 Complete creation test finished!');
    console.log('\n📋 What was tested:');
    console.log('   ✅ Proper CSV format with category_title');
    console.log('   ✅ Subcategory creation with all fields');
    console.log('   ✅ Multiple form fields creation');
    console.log('   ✅ Different input types (TEXT, SELECT)');
    console.log('   ✅ Form field options and validation');
    console.log('   ✅ Proper relationships (form fields → subcategory)');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    // Clean up on error
    try {
      fs.unlinkSync('complete-test.csv');
    } catch (cleanupError) {
      // Ignore cleanup errors
    }
  }
}

testCompleteCreation();
