# Business Information API - Form Data Payloads

## Overview

The Business Information API now supports form data uploads for logo and banner files. This document provides the exact payloads and responses for all endpoints.

## Important Notes

- **Content-Type**: Use `multipart/form-data` for POST and PUT requests
- **File Fields**: `logo` and `banner` for file uploads
- **JSON Fields**: Send as JSON strings for complex data (arrays/objects)
- **Boole<PERSON> Fields**: Send as string `"true"` or `"false"`
- **Number Fields**: Send as string, will be converted automatically

## API Endpoints with Form Data

### 1. POST /api/seller/business-information - Create Business Info

#### Request (Form Data)
```
Content-Type: multipart/form-data

Fields:
company_name: "Tech Solutions Inc"
short_description: "We provide innovative technology solutions for businesses"
long_description: "Tech Solutions Inc is a leading technology company that specializes in providing comprehensive software development, cloud solutions, and IT consulting services."
logo: [FILE] (image file for logo)
banner: [FILE] (image file for banner)
address: "123 Tech Street, Suite 456"
city: "San Francisco"
state: "California"
country: "USA"
postal_code: "94105"
phone_number: "******-555-0123"
email: "<EMAIL>"
website_url: "https://www.techsolutions.com"
business_type: "Technology"
business_category: "Software Development"
established_year: "2020"
employee_count: "11-50"
annual_revenue: "$1M-$10M"
business_license: "BL-CA-*********"
tax_id: "TAX-*********"
social_media_links: '{"facebook":"https://facebook.com/techsolutions","twitter":"https://twitter.com/techsolutions","linkedin":"https://linkedin.com/company/tech-solutions"}'
operating_hours: '{"monday":"9:00 AM - 6:00 PM","tuesday":"9:00 AM - 6:00 PM","wednesday":"9:00 AM - 6:00 PM","thursday":"9:00 AM - 6:00 PM","friday":"9:00 AM - 6:00 PM","saturday":"10:00 AM - 2:00 PM","sunday":"Closed"}'
services_offered: '["Web Development","Mobile App Development","Cloud Solutions","IT Consulting","DevOps Services"]'
certifications: '["ISO 9001:2015 Quality Management","AWS Certified Solutions Architect","Microsoft Gold Partner"]'
is_active: "true"
```

#### Success Response (201 Created)
```json
{
  "success": true,
  "message": "Business information created successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440001",
    "seller_id": "550e8400-e29b-41d4-a716-446655440000",
    "business_name": "Tech Solutions Inc",
    "short_description": "We provide innovative technology solutions for businesses",
    "long_description": "Tech Solutions Inc is a leading technology company...",
    "logo_url": "/public/uploads/business/logos/550e8400-e29b-41d4-a716-446655440001.png",
    "banner_url": "/public/uploads/business/banners/550e8400-e29b-41d4-a716-446655440002.jpg",
    "address": "123 Tech Street, Suite 456",
    "city": "San Francisco",
    "state": "California",
    "country": "USA",
    "postal_code": "94105",
    "phone_number": "******-555-0123",
    "email": "<EMAIL>",
    "website_url": "https://www.techsolutions.com",
    "business_type": "Technology",
    "business_category": "Software Development",
    "established_year": 2020,
    "employee_count": "11-50",
    "annual_revenue": "$1M-$10M",
    "business_license": "BL-CA-*********",
    "tax_id": "TAX-*********",
    "social_media_links": {
      "facebook": "https://facebook.com/techsolutions",
      "twitter": "https://twitter.com/techsolutions",
      "linkedin": "https://linkedin.com/company/tech-solutions"
    },
    "operating_hours": {
      "monday": "9:00 AM - 6:00 PM",
      "tuesday": "9:00 AM - 6:00 PM",
      "wednesday": "9:00 AM - 6:00 PM",
      "thursday": "9:00 AM - 6:00 PM",
      "friday": "9:00 AM - 6:00 PM",
      "saturday": "10:00 AM - 2:00 PM",
      "sunday": "Closed"
    },
    "services_offered": [
      "Web Development",
      "Mobile App Development",
      "Cloud Solutions",
      "IT Consulting",
      "DevOps Services"
    ],
    "certifications": [
      "ISO 9001:2015 Quality Management",
      "AWS Certified Solutions Architect",
      "Microsoft Gold Partner"
    ],
    "is_verified": false,
    "verification_status": "pending",
    "verification_date": null,
    "is_active": true,
    "is_deleted": false,
    "created_at": "2024-01-15T10:30:00.000Z",
    "updated_at": "2024-01-15T10:30:00.000Z",
    "seller": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "first_name": "John",
      "last_name": "Doe",
      "email": "<EMAIL>"
    }
  }
}
```

### 2. GET /api/seller/business-information - Get All Business Info

#### Request
```
GET /api/seller/business-information?page=1&limit=10&includeInactive=false
```

#### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Business information retrieved successfully",
  "data": {
    "data": [
      {
        "id": "550e8400-e29b-41d4-a716-446655440001",
        "seller_id": "550e8400-e29b-41d4-a716-446655440000",
        "business_name": "Tech Solutions Inc",
        "short_description": "We provide innovative technology solutions",
        "logo_url": "/public/uploads/business/logos/550e8400-e29b-41d4-a716-446655440001.png",
        "banner_url": "/public/uploads/business/banners/550e8400-e29b-41d4-a716-446655440002.jpg",
        "address": "123 Tech Street, Suite 456",
        "city": "San Francisco",
        "state": "California",
        "country": "USA",
        "postal_code": "94105",
        "phone_number": "******-555-0123",
        "email": "<EMAIL>",
        "website_url": "https://www.techsolutions.com",
        "business_type": "Technology",
        "business_category": "Software Development",
        "established_year": 2020,
        "employee_count": "11-50",
        "annual_revenue": "$1M-$10M",
        "business_license": "BL-CA-*********",
        "tax_id": "TAX-*********",
        "social_media_links": {
          "facebook": "https://facebook.com/techsolutions",
          "twitter": "https://twitter.com/techsolutions"
        },
        "operating_hours": {
          "monday": "9:00 AM - 6:00 PM",
          "tuesday": "9:00 AM - 6:00 PM"
        },
        "services_offered": [
          "Web Development",
          "Mobile App Development"
        ],
        "certifications": [
          "ISO 9001:2015",
          "AWS Certified"
        ],
        "is_verified": true,
        "verification_status": "verified",
        "verification_date": "2024-01-16T09:15:00.000Z",
        "is_active": true,
        "is_deleted": false,
        "created_at": "2024-01-15T10:30:00.000Z",
        "updated_at": "2024-01-16T09:15:00.000Z",
        "seller": {
          "id": "550e8400-e29b-41d4-a716-446655440000",
          "first_name": "John",
          "last_name": "Doe",
          "email": "<EMAIL>"
        }
      }
    ],
    "meta": {
      "total": 1,
      "page": 1,
      "limit": 10,
      "totalPages": 1
    }
  }
}
```

### 3. GET /api/seller/business-information/:id - Get Specific Business Info

#### Request
```
GET /api/seller/business-information/550e8400-e29b-41d4-a716-446655440001
```

#### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Business information retrieved successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440001",
    "seller_id": "550e8400-e29b-41d4-a716-446655440000",
    "business_name": "Tech Solutions Inc",
    "short_description": "We provide innovative technology solutions for businesses",
    "long_description": "Tech Solutions Inc is a leading technology company...",
    "logo_url": "/public/uploads/business/logos/550e8400-e29b-41d4-a716-446655440001.png",
    "banner_url": "/public/uploads/business/banners/550e8400-e29b-41d4-a716-446655440002.jpg",
    "address": "123 Tech Street, Suite 456",
    "city": "San Francisco",
    "state": "California",
    "country": "USA",
    "postal_code": "94105",
    "phone_number": "******-555-0123",
    "email": "<EMAIL>",
    "website_url": "https://www.techsolutions.com",
    "business_type": "Technology",
    "business_category": "Software Development",
    "established_year": 2020,
    "employee_count": "11-50",
    "annual_revenue": "$1M-$10M",
    "business_license": "BL-CA-*********",
    "tax_id": "TAX-*********",
    "social_media_links": {
      "facebook": "https://facebook.com/techsolutions",
      "twitter": "https://twitter.com/techsolutions",
      "linkedin": "https://linkedin.com/company/tech-solutions"
    },
    "operating_hours": {
      "monday": "9:00 AM - 6:00 PM",
      "tuesday": "9:00 AM - 6:00 PM",
      "wednesday": "9:00 AM - 6:00 PM",
      "thursday": "9:00 AM - 6:00 PM",
      "friday": "9:00 AM - 6:00 PM",
      "saturday": "10:00 AM - 2:00 PM",
      "sunday": "Closed"
    },
    "services_offered": [
      "Web Development",
      "Mobile App Development",
      "Cloud Solutions",
      "IT Consulting",
      "DevOps Services"
    ],
    "certifications": [
      "ISO 9001:2015 Quality Management",
      "AWS Certified Solutions Architect",
      "Microsoft Gold Partner"
    ],
    "is_verified": true,
    "verification_status": "verified",
    "verification_date": "2024-01-16T09:15:00.000Z",
    "is_active": true,
    "is_deleted": false,
    "created_at": "2024-01-15T10:30:00.000Z",
    "updated_at": "2024-01-16T09:15:00.000Z",
    "seller": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "first_name": "John",
      "last_name": "Doe",
      "email": "<EMAIL>"
    }
  }
}
```

### 4. PUT /api/seller/business-information/:id - Update Business Info

#### Request (Form Data)
```
Content-Type: multipart/form-data

Fields (all optional):
business_name: "Updated Tech Solutions Inc"
short_description: "Updated description"
logo: [FILE] (new logo file - optional)
banner: [FILE] (new banner file - optional)
address: "456 Updated Street"
city: "Los Angeles"
established_year: "2019"
employee_count: "51-200"
is_active: "true"
```

#### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Business information updated successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440001",
    "business_name": "Updated Tech Solutions Inc",
    "logo_url": "/public/uploads/business/logos/550e8400-e29b-41d4-a716-446655440003.png",
    "banner_url": "/public/uploads/business/banners/550e8400-e29b-41d4-a716-446655440004.jpg",
    "is_active": true,
    "updated_at": "2024-01-17T14:22:00.000Z"
  }
}
```

### 5. DELETE /api/seller/business-information/:id - Delete Business Info

#### Request
```
DELETE /api/seller/business-information/550e8400-e29b-41d4-a716-446655440001
```

#### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Business information deleted successfully",
  "data": null
}
```

## Error Responses

### Validation Error (422)
```json
{
  "success": false,
  "message": "Validation failed",
  "error": {
    "business_name": [
      "Business name is required"
    ],
    "is_active": [
      "is_active must be a boolean"
    ]
  }
}
```

## Frontend Integration Example

```javascript
// Create FormData
const formData = new FormData();

// Add text fields
formData.append('business_name', 'Tech Solutions Inc');
formData.append('short_description', 'We provide tech solutions');

// Add files
formData.append('logo', logoFile); // File object
formData.append('banner', bannerFile); // File object

// Add JSON fields as strings
formData.append('social_media_links', JSON.stringify({
  facebook: 'https://facebook.com/techsolutions',
  twitter: 'https://twitter.com/techsolutions'
}));

// Add boolean as string
formData.append('is_active', 'true');

// Send request
fetch('/api/seller/business-information', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token
  },
  body: formData
});
```