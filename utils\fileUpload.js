const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

function saveBufferToFile(buffer, originalname, folder) {
  const uploadPath = path.join(__dirname, '..', 'public', folder); // adjust if needed
  if (!fs.existsSync(uploadPath)) {
    fs.mkdirSync(uploadPath, { recursive: true });
  }

  const extension = path.extname(originalname);
  const filename = `${uuidv4()}${extension}`;
  const fullPath = path.join(uploadPath, filename);
  fs.writeFileSync(fullPath, buffer);

  return `/public/${folder}/${filename}`; // or just `/uploads/...` if you're serving it via static route
}

module.exports = { saveBufferToFile };
