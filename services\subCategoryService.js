const SubCategory = require('../models/subCategoryModel');

class SubCategoryService {
    static async get(id) {
        return await SubCategory.findSubCategoryById(id);
    }

    static async create(data) {
        return await SubCategory.createSubCategory(data);
    }

    static async update(id, data) {
        return await SubCategory.updateSubCategory(id, data);
    }

    static async delete(id) {
        return await SubCategory.deleteSubCategory(id);
    }

    static async exists(id) {
        try {
            const subcategory = await SubCategory.findSubCategoryById(id);
            return !!subcategory;
        } catch (error) {
            return false;
        }
    }
}

module.exports = SubCategoryService;