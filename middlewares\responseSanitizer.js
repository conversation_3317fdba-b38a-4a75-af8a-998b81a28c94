const sensitiveFields = [
    'password_hash',
    'refresh_token',
    'email_verification_token',
    'password_reset_token',
    'password_reset_expires_at',
  ];
  
  function sanitizeResponse(data) {
    if (Array.isArray(data)) {
      return data.map(item => sanitizeResponse(item));
    } else if (data && typeof data === 'object') {
      const sanitized = { ...data };
      sensitiveFields.forEach(field => {
        delete sanitized[field];
      });
      // Recursively sanitize nested objects
      Object.keys(sanitized).forEach(key => {
        if (typeof sanitized[key] === 'object') {
          sanitized[key] = sanitizeResponse(sanitized[key]);
        }
      });
      return sanitized;
    }
    return data;
  }
  
  module.exports = (req, res, next) => {
    const originalSend = res.send;
    res.send = function (body) {
      if (body && typeof body === 'object') {
        body = sanitizeResponse(body);
      }
      originalSend.call(this, body);
    };
    next();
  };