const { body } = require('express-validator');

const addCategoryInterestValidation = [
  body('categoryIds')
    .isArray()
    .withMessage('Category IDs must be an array')
    .notEmpty()
    .withMessage('At least one category ID is required')
    .custom(value => {
      if (!Array.isArray(value)) return false;
      return value.every(id => typeof id === 'string' && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id));
    })
    .withMessage('All category IDs must be valid UUIDs')
];

const addSubcategoryInterestValidation = [
  body('subcategoryIds')
    .isArray()
    .withMessage('Subcategory IDs must be an array')
    .notEmpty()
    .withMessage('At least one subcategory ID is required')
    .custom(value => {
      if (!Array.isArray(value)) return false;
      return value.every(id => typeof id === 'string' && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id));
    })
    .withMessage('All subcategory IDs must be valid UUIDs')
];

const updateCategoriesValidation = [
  body('categoryIds')
    .isArray()
    .withMessage('Category IDs must be an array')
    .notEmpty()
    .withMessage('At least one category ID is required')
    .custom(value => {
      if (!Array.isArray(value)) return false;
      return value.every(id => typeof id === 'string' && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id));
    })
    .withMessage('All category IDs must be valid UUIDs')
];

const updateSubcategoriesValidation = [
  body('subcategoryIds')
    .isArray()
    .withMessage('Subcategory IDs must be an array')
    .notEmpty()
    .withMessage('At least one subcategory ID is required')
    .custom(value => {
      if (!Array.isArray(value)) return false;
      return value.every(id => typeof id === 'string' && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id));
    })
    .withMessage('All subcategory IDs must be valid UUIDs')
];

module.exports = {
  addCategoryInterestValidation,
  addSubcategoryInterestValidation,
  updateCategoriesValidation,
  updateSubcategoriesValidation
};
