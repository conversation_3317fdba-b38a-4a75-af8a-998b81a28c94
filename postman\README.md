# Marktzoom API Postman Collection

This directory contains comprehensive Postman collection and environment files for testing the Marktzoom API endpoints.

## Files Included

1. **MarktzoomAPI_Collection.json** - Complete API collection with all endpoints
2. **MarktzoomAPI_Environment.json** - Environment variables for local development
3. **README.md** - This documentation file

## How to Import

### Import Collection
1. Open Postman
2. Click "Import" button
3. Select `MarktzoomAPI_Collection.json`
4. The collection will be imported with all endpoints organized by categories

### Import Environment
1. In Postman, go to "Environments" tab
2. Click "Import" button
3. Select `MarktzoomAPI_Environment.json`
4. Select the "Marktzoom API Environment" from the environment dropdown

## Environment Variables

The environment includes the following variables:

- **baseUrl**: `http://localhost:5000` (your API base URL)
- **token**: JWT access token (automatically set after login)
- **refresh_token**: JWT refresh token
- **user_id**: Current user ID
- **buyer_id**: Buyer profile ID
- **seller_id**: Seller profile ID
- **request_id**: Request ID for testing
- **offer_id**: Offer ID for testing
- **category_id**: Category ID for testing
- **subcategory_id**: Subcategory ID for testing
- **order_id**: Order ID for testing

## Collection Structure

### 🔐 Auth Routes
- **Register** - Create new user account
- **Mobile Register** - Mobile-specific registration
- **Verify OTP** - Verify email/phone OTP
- **Login** - User authentication (auto-saves token)
- **New User Register** - Enhanced registration with full profile
- **New User Login** - Enhanced login
- **Refresh Token** - Refresh JWT token
- **Forgot Password** - Request password reset
- **Reset Password** - Reset password with token
- **Change Password** - Change password (authenticated)
- **Verify Email** - Verify email address
- **Logout** - User logout
- **Get Authenticated User** - Get current user info
- **Get User Profile** - Get detailed profile

### 🛒 Buyer Routes

#### Profile Management
- **Get Buyer Profile** - Retrieve buyer profile
- **Update Buyer Profile** - Update profile with form data

#### Categories
- **Get Categories** - List all categories
- **Get Category with Subcategories** - Get category details
- **Get Subcategory with Form Fields** - Get subcategory form fields

#### Requests Management
- **Create Request** - Create new service/product request
- **Get My Requests** - List buyer's requests with filters
- **Get Request Details** - Get specific request details
- **Update Request** - Update existing request
- **Delete Request** - Delete request

### 🏪 Seller Routes

#### Profile Management
- **Get Seller Profile** - Retrieve seller profile
- **Update Seller Profile** - Update profile with form data

#### Business Information
- **Get Business Information** - Get business details
- **Update Business Information** - Update business info

#### Requests & Offers
- **Get Available Requests** - Browse available requests
- **Get Request Details** - View request details
- **Express Interest** - Show interest in a request
- **Create Offer** - Submit offer for a request
- **Get My Offers** - List seller's offers
- **Update Offer** - Modify existing offer

## Usage Instructions

### 1. Start Your API Server
Make sure your Marktzoom API server is running on `http://localhost:5000`

### 2. Authentication Flow
1. Use **Register** or **New User Register** to create an account
2. Use **Login** or **New User Login** to authenticate
3. The login request automatically saves the JWT token to environment
4. All subsequent requests will use this token automatically

### 3. Testing Buyer Flow
1. Login as a buyer
2. Use **Get Categories** to see available categories
3. Use **Create Request** to post a new request
4. Use **Get My Requests** to see your requests

### 4. Testing Seller Flow
1. Login as a seller
2. Use **Get Available Requests** to browse requests
3. Use **Express Interest** to show interest
4. Use **Create Offer** to submit an offer

### 5. File Uploads
For endpoints that require file uploads (profile pictures, documents):
1. Select the request
2. Go to "Body" tab
3. Select "form-data"
4. For file fields, change type from "Text" to "File"
5. Click "Select Files" to choose your file

## Request Examples

### Sample Registration
```json
{
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "phone_number": "+**********",
  "password": "Password123!",
  "business_name": "John's Business"
}
```

### Sample Request Creation
```json
{
  "title": "Need Web Development Services",
  "short_description": "Looking for a professional web developer",
  "description": "Detailed project description...",
  "category_id": "category-uuid-here",
  "sub_category_id": "subcategory-uuid-here",
  "quantity": 1,
  "budget_min": 1000,
  "budget_max": 5000,
  "deadline": "2024-12-31",
  "urgency": "Medium",
  "request_type": "Service"
}
```

### Sample Offer Creation
```json
{
  "request_id": "request-uuid-here",
  "price": 3500,
  "delivery_time": 30,
  "delivery_time_unit": "days",
  "description": "Detailed offer description...",
  "terms_and_conditions": "Payment terms and conditions..."
}
```

## Tips

1. **Auto Token Management**: The login requests automatically save tokens to environment variables
2. **UUID Placeholders**: Replace `uuid-here` placeholders with actual IDs from your responses
3. **Query Parameters**: Modify query parameters in GET requests as needed
4. **File Uploads**: Use form-data for endpoints requiring file uploads
5. **Error Handling**: Check response status and error messages for debugging

## Environment Setup for Different Stages

You can create multiple environments for different deployment stages:

- **Local Development**: `http://localhost:5000`
- **Staging**: `https://staging-api.marktzoom.com`
- **Production**: `https://api.marktzoom.com`

Simply duplicate the environment and change the `baseUrl` value.

## Support

If you encounter any issues with the API endpoints or need additional endpoints added to the collection, please refer to the API documentation or contact the development team.
