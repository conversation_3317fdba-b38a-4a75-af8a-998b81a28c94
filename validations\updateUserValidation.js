const { body } = require('express-validator');
const UserModel = require('../models/userModel');

const updateUserValidation = [
  body('first_name')
    .optional()
    .isString()
    .withMessage('First name must be a string')
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),

  body('last_name')
    .optional()
    .isString()
    .withMessage('Last name must be a string')
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),

  body('phone_number')
    .optional()
    .isString()
    .withMessage('Phone number must be a string')
    .matches(/^\+?[0-9]{10,15}$/)
    .withMessage('Phone number must be a valid format')
    .custom(async (value, { req }) => {
      if (!value) return true;
      
      const existingUser = await UserModel.findUserByPhone(value);
      if (existingUser && existingUser.id !== req.params.id) {
        throw new Error('The phone number has already been taken');
      }
      return true;
    }),

  body('email')
    .optional()
    .isEmail()
    .withMessage('Email must be a valid email address')
    .custom(async (value, { req }) => {
      if (!value) return true;
      
      const existingUser = await UserModel.findUserByEmail(value);
      if (existingUser && existingUser.id !== req.params.id) {
        throw new Error('The email has already been taken');
      }
      return true;
    }),

  body('address')
    .optional()
    .isString()
    .withMessage('Address must be a string'),

  body('gender')
    .optional()
    .isIn(['male', 'female', 'other', ''])
    .withMessage('Gender must be male, female, other, or empty'),

  body('date_of_birth')
    .optional()
    .isISO8601()
    .withMessage('Date of birth must be a valid date')
    .custom(value => {
      if (!value) return true;
      
      const date = new Date(value);
      const now = new Date();
      const minAge = new Date(now.getFullYear() - 18, now.getMonth(), now.getDate());
      if (date > minAge) {
        throw new Error('User must be at least 18 years old');
      }
      return true;
    }),

  body('father_name')
    .optional()
    .isString()
    .withMessage('Father name must be a string')
    .isLength({ max: 50 })
    .withMessage('Father name must not exceed 50 characters'),

  body('mother_name')
    .optional()
    .isString()
    .withMessage('Mother name must be a string')
    .isLength({ max: 50 })
    .withMessage('Mother name must not exceed 50 characters'),

  body('role')
    .optional()
    .isString()
    .withMessage('Role must be a string')
    .isIn(['Buyer', 'Seller', 'Supplier', 'Admin'])
    .withMessage('Role must be Buyer, Seller, Supplier, or Admin'),

  body('status')
    .optional()
    .isString()
    .withMessage('Status must be a string')
    .isIn(['active', 'inactive', 'suspended', 'pending'])
    .withMessage('Status must be Active, Inactive, Suspended, or Pending'),

  body('is_approved')
    .optional()
    .isBoolean()
    .withMessage('is_approved must be a boolean'),
];

module.exports = updateUserValidation;
