const fs = require("fs");
const path = require("path");

const prismaDir = path.join(__dirname, "../prisma");
const baseSchemaPath = path.join(prismaDir, "base.prisma");
const modelsDir = path.join(prismaDir, "models");
const outputSchemaPath = path.join(prismaDir, "schema.prisma");

function mergePrismaSchemas() {
  try {
    // Read base schema
    let schemaContent = fs.readFileSync(baseSchemaPath, "utf8");

    // Read all model files and append them
    const modelFiles = fs.readdirSync(modelsDir);
    modelFiles.forEach((file) => {
      const filePath = path.join(modelsDir, file);
      const modelContent = fs.readFileSync(filePath, "utf8");
      schemaContent += `\n\n${modelContent}`;
    });

    // Write to schema.prisma
    fs.writeFileSync(outputSchemaPath, schemaContent);
    console.log("✅ Prisma schema merged successfully!");
  } catch (error) {
    console.error("❌ Error merging Prisma schemas:", error);
  }
}

// Run the function
mergePrismaSchemas();
