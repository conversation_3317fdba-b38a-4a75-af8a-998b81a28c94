const CartModel = require('../models/cartModel');
const ApiError = require('../utils/apiError');

class CartService {
  /**
   * Add an offer to the cart
   * @param {string} buyerId - Buyer ID
   * @param {string} offerId - Offer ID
   * @param {number} quantity - Quantity to add
   * @returns {Promise<Object>} Cart item
   */
  static async addToCart(buyerId, offerId, quantity = 1) {
    // try {
      return await CartModel.addToCart(buyerId, offerId, quantity);
    // } catch (error) {
    //   if (error instanceof ApiError) {
    //     throw error;
    //   }
    //   throw new ApiError(400, `Failed to add item to cart: ${error.message}`);
    // }
  }

  /**
   * Get all items in a buyer's cart
   * @param {string} buyerId - Buyer ID
   * @returns {Promise<Object>} Cart with items
   */
  static async getCartItems(buyerId) {
    try {
      return await CartModel.getCartItems(buyerId);
    } catch (error) {
      throw new ApiError(500, `Failed to get cart items: ${error.message}`);
    }
  }

  /**
   * Update a cart item
   * @param {string} buyerId - Buyer ID
   * @param {string} cartItemId - Cart item ID
   * @param {number} quantity - New quantity
   * @returns {Promise<Object>} Updated cart item
   */
  static async updateCartItem(buyerId, cartItemId, quantity) {
    try {
      return await CartModel.updateCartItem(buyerId, cartItemId, quantity);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(400, `Failed to update cart item: ${error.message}`);
    }
  }

  /**
   * Remove an item from the cart
   * @param {string} buyerId - Buyer ID
   * @param {string} cartItemId - Cart item ID
   * @returns {Promise<Object>} Result of deletion
   */
  static async removeCartItem(buyerId, cartItemId) {
    try {
      return await CartModel.removeCartItem(buyerId, cartItemId);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(400, `Failed to remove cart item: ${error.message}`);
    }
  }

  /**
   * Clear all items from a buyer's cart
   * @param {string} buyerId - Buyer ID
   * @returns {Promise<Object>} Result of deletion
   */
  static async clearCart(buyerId) {
    try {
      return await CartModel.clearCart(buyerId);
    } catch (error) {
      throw new ApiError(500, `Failed to clear cart: ${error.message}`);
    }
  }
}

module.exports = CartService;
