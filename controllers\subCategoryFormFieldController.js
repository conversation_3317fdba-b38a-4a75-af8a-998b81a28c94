const SubCategoryFormFieldService = require('../services/subCategoryFormFieldService');
const SubCategoryService = require('../services/subCategoryService');
const { validationResult } = require('express-validator');
const { formatValidationErrors } = require('../utils/validationFormatter');
const sendResponse = require('../utils/sendResponse');

class SubCategoryFormFieldController {
  /**
   * Create a new form field for a subcategory
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async createFormField(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const formattedErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          formattedErrors.message,
          null,
          formattedErrors.errors,
          null,
          422
        );
      }

      // Check if subcategory exists
      const subcategoryId = req.params.subcategoryId;
      const subcategory = await SubCategoryService.get(subcategoryId);
      if (!subcategory) {
        return sendResponse(
          res,
          false,
          'Subcategory not found',
          null,
          null,
          null,
          404
        );
      }

      // Create form field
      const formFieldData = {
        ...req.body,
        subcategory_id: subcategoryId
      };

      const formField = await SubCategoryFormFieldService.createFormField(formFieldData);
      return sendResponse(
        res,
        true,
        'Form field created successfully',
        formField,
        null,
        null,
        201
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message || 'Failed to create form field',
        null,
        error,
        null,
        500
      );
    }
  }

  /**
   * Create multiple form fields for a subcategory
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async createManyFormFields(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const formattedErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          formattedErrors.message,
          null,
          formattedErrors.errors,
          null,
          422
        );
      }

      // Check if subcategory exists
      const subcategoryId = req.params.subcategoryId;
      const subcategory = await SubCategoryService.get(subcategoryId);
      if (!subcategory) {
        return sendResponse(
          res,
          false,
          'Subcategory not found',
          null,
          null,
          null,
          404
        );
      }

      // Create form fields
      const { form_fields } = req.body;
      const result = await SubCategoryFormFieldService.createManyFormFields(form_fields, subcategoryId);
      
      // Get the created form fields
      const formFields = await SubCategoryFormFieldService.getFormFieldsBySubcategoryId(subcategoryId);
      
      return sendResponse(
        res,
        true,
        'Form fields created successfully',
        formFields,
        null,
        null,
        201
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message || 'Failed to create form fields',
        null,
        error,
        null,
        500
      );
    }
  }

  /**
   * Get all form fields for a subcategory
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getFormFieldsBySubcategoryId(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const formattedErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          formattedErrors.message,
          null,
          formattedErrors.errors,
          null,
          422
        );
      }

      const subcategoryId = req.params.subcategoryId;
      const formFields = await SubCategoryFormFieldService.getFormFieldsBySubcategoryId(subcategoryId);
      
      return sendResponse(
        res,
        true,
        'Form fields retrieved successfully',
        formFields,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message || 'Failed to retrieve form fields',
        null,
        error,
        null,
        500
      );
    }
  }

  /**
   * Get a form field by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getFormFieldById(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const formattedErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          formattedErrors.message,
          null,
          formattedErrors.errors,
          null,
          422
        );
      }

      const formFieldId = req.params.id;
      const formField = await SubCategoryFormFieldService.getFormFieldById(formFieldId);
      
      if (!formField) {
        return sendResponse(
          res,
          false,
          'Form field not found',
          null,
          null,
          null,
          404
        );
      }
      
      return sendResponse(
        res,
        true,
        'Form field retrieved successfully',
        formField,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message || 'Failed to retrieve form field',
        null,
        error,
        null,
        500
      );
    }
  }

  /**
   * Update a form field
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async updateFormField(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const formattedErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          formattedErrors.message,
          null,
          formattedErrors.errors,
          null,
          422
        );
      }

      const formFieldId = req.params.id;
      const formField = await SubCategoryFormFieldService.getFormFieldById(formFieldId);
      
      if (!formField) {
        return sendResponse(
          res,
          false,
          'Form field not found',
          null,
          null,
          null,
          404
        );
      }
      
      const updatedFormField = await SubCategoryFormFieldService.updateFormField(formFieldId, req.body);
      
      return sendResponse(
        res,
        true,
        'Form field updated successfully',
        updatedFormField,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message || 'Failed to update form field',
        null,
        error,
        null,
        500
      );
    }
  }

  /**
   * Delete a form field
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async deleteFormField(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const formattedErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          formattedErrors.message,
          null,
          formattedErrors.errors,
          null,
          422
        );
      }

      const formFieldId = req.params.id;
      const formField = await SubCategoryFormFieldService.getFormFieldById(formFieldId);
      
      if (!formField) {
        return sendResponse(
          res,
          false,
          'Form field not found',
          null,
          null,
          null,
          404
        );
      }
      
      await SubCategoryFormFieldService.deleteFormField(formFieldId);
      
      return sendResponse(
        res,
        true,
        'Form field deleted successfully',
        null,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message || 'Failed to delete form field',
        null,
        error,
        null,
        500
      );
    }
  }

  /**
   * Delete all form fields for a subcategory
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async deleteFormFieldsBySubcategoryId(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const formattedErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          formattedErrors.message,
          null,
          formattedErrors.errors,
          null,
          422
        );
      }

      const subcategoryId = req.params.subcategoryId;
      await SubCategoryFormFieldService.deleteFormFieldsBySubcategoryId(subcategoryId);
      
      return sendResponse(
        res,
        true,
        'Form fields deleted successfully',
        null,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message || 'Failed to delete form fields',
        null,
        error,
        null,
        500
      );
    }
  }
}

module.exports = SubCategoryFormFieldController;
