model subcategory_form_fields {
  id              String         @id @default(uuid())
  subcategory_id  String
  label_name      String
  input_type      form_field_type @default(TEXT)
  label_subtitle  String?
  placeholder     String?
  is_required     Boolean        @default(false)
  options         String[]       @default([]) // For dropdown, radio, checkbox types
  default_value   String?
  validation_regex String?
  min_value       Int?
  max_value       Int?
  min_length      Int?
  max_length      Int?
  sort_order      Int            @default(0)
  created_at      DateTime       @default(now())
  updated_at      DateTime       @updatedAt

  // Relations
  subcategory     sub_categories @relation(fields: [subcategory_id], references: [id], onDelete: Cascade)
  offer_form_field_values offer_form_field_values[]

  @@index([subcategory_id])
  @@index([sort_order])
}

enum form_field_type {
  TEXT
  TEXTAREA
  NUMBER
  EMAIL
  PASSWORD
  DATE
  TIME
  DATETIME
  CHECKBOX
  RADIO
  SELECT
  MULTISELECT
  FILE
  PHONE
  URL
  COLOR
  RANGE
}
