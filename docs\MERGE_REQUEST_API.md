# MERGE REQUEST API DOCUMENTATION

## Overview
This API allows admins to create a new request by merging multiple existing requests. The `is_merged` field is used to identify merged requests in both list and detail views.

## Base URL
`/api/admin/requests`

## Authentication
Bearer Token (Admin role required)

---

## 1. POST /requests/merge
**Description:** Create a new request by merging multiple existing requests (Admin only)

**Endpoint:** `POST /api/admin/requests/merge`

**Headers:**
```
Authorization: Bearer <admin_token>
Content-Type: application/json
```

**Payload:**
```json
{
  "title": "New Merged Request Title",
  "short_description": "Brief description of merged request",
  "description": "Detailed description of the merged request",
  "category_id": "uuid",
  "sub_category_id": "uuid",
  "quantity": 5,
  "budget_min": 100.00,
  "budget_max": 1000.00,
  "deadline": "2024-02-01T00:00:00.000Z",
  "urgency": "High",
  "request_type": "Service",
  "location": "New York, USA",
  "additional_info": "Additional information",
  "custom_fields": {},
  "request_ids_to_merge": ["uuid1", "uuid2", "uuid3"]
}
```

**Response (Success):**
```json
{
  "success": true,
  "message": "Successfully created new request by merging 3 request(s)",
  "data": {
    "id": "new-merged-request-uuid",
    "request_code": "REQ-*************",
    "title": "New Merged Request Title",
    "short_description": "Brief description of merged request",
    "description": "Detailed description of the merged request",
    "quantity": 5,
    "budget_min": 100.00,
    "budget_max": 1000.00,
    "deadline": "2024-02-01T00:00:00.000Z",
    "urgency": "High",
    "status": "Merged",
    "is_merged": true,
    "buyer": {
      "id": "admin-uuid",
      "first_name": "Admin",
      "last_name": "User",
      "email": "<EMAIL>"
    },
    "category": {
      "id": "category-uuid",
      "name": "Technology"
    },
    "sub_category": {
      "id": "subcategory-uuid",
      "name": "Web Development"
    },
    "parent_merged_requests": [
      {
        "id": "merge-record-uuid",
        "merged_item": {
          "id": "original-request-uuid-1",
          "request_code": "REQ-*************",
          "title": "Original Request 1",
          "status": "Merged",
          "is_merged": true,
          "buyer": {
            "id": "buyer-uuid",
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>"
          }
        },
        "merged_by_user": {
          "id": "admin-uuid",
          "first_name": "Admin",
          "last_name": "User",
          "email": "<EMAIL>"
        }
      }
    ],
    "created_at": "2024-01-15T10:30:00.000Z",
    "updated_at": "2024-01-15T10:30:00.000Z"
  }
}
```

---

## 2. GET /requests/:id (Enhanced)
**Description:** Get request details including merged child requests and `is_merged` field

**Endpoint:** `GET /api/admin/requests/{request_id}`

**Response (Merged Request):**
```json
{
  "success": true,
  "message": "Request retrieved successfully",
  "data": {
    "id": "request-uuid",
    "request_code": "REQ-*************",
    "title": "Request Title",
    "status": "Merged",
    "is_merged": true,
    "parent_merged_requests": [
      // Array of merged child requests
    ],
    // ... other request fields
  }
}
```

**Response (Regular Request):**
```json
{
  "success": true,
  "message": "Request retrieved successfully",
  "data": {
    "id": "request-uuid",
    "request_code": "REQ-*************",
    "title": "Regular Request",
    "status": "Pending",
    "is_merged": false,
    "parent_merged_requests": [],
    // ... other request fields
  }
}
```

---

## 3. GET /requests (Enhanced)
**Description:** Get requests list with `is_merged` field for filtering

**Endpoint:** `GET /api/admin/requests?page=1&limit=10`

**Query Parameters:**
- `page`: Page number (optional, default: 1)
- `limit`: Items per page (optional, default: 10)
- `status`: Filter by status (optional)
- `search`: Search in title/description (optional)

**Response:**
```json
{
  "success": true,
  "message": "Requests retrieved successfully",
  "data": [
    {
      "id": "request-uuid-1",
      "request_code": "REQ-*************",
      "title": "Merged Request",
      "status": "Merged",
      "is_merged": true,
      "buyer": {
        "id": "admin-uuid",
        "first_name": "Admin",
        "last_name": "User"
      },
      // ... other fields
    },
    {
      "id": "request-uuid-2",
      "request_code": "REQ-*************",
      "title": "Regular Request",
      "status": "Pending",
      "is_merged": false,
      "buyer": {
        "id": "buyer-uuid",
        "first_name": "John",
        "last_name": "Doe"
      },
      // ... other fields
    }
  ],
  "meta": {
    "total": 50,
    "page": 1,
    "limit": 10,
    "totalPages": 5
  }
}
```

---

## Admin Panel Usage

### Filter Merged Requests
```javascript
// Get only merged requests
const mergedRequests = await fetch('/api/admin/requests?status=Merged');

// Or filter by is_merged field in frontend
const mergedOnly = requests.filter(req => req.is_merged === true);
const regularOnly = requests.filter(req => req.is_merged === false);
```

### Display Merged Status
```javascript
// Show merged badge in admin panel
if (request.is_merged) {
  return <Badge color="purple">Merged</Badge>;
}
```

---

## Database Schema Changes

### Added Field
```prisma
model requests {
  // ... existing fields
  is_merged Boolean @default(false)
  // ... other fields
}
```

### Relationships
```prisma
model requests {
  parent_merged_requests request_merged_items[] @relation("ParentRequest")
  merged_requests        request_merged_items[] @relation("MergedItem")
}

model request_merged_items {
  parent_request requests @relation(name: "ParentRequest", fields: [request_id], references: [id])
  merged_item    requests @relation(name: "MergedItem", fields: [merged_item_id], references: [id])
}
```

---

## Business Rules

1. Only admins can create merged requests
2. All original requests must exist and not be deleted
3. Original requests cannot already be merged
4. When requests are merged:
   - New parent request: `status: "Merged"`, `is_merged: true`
   - Original requests: `status: "Merged"`, `is_merged: true`
   - Admin becomes the buyer of the new request
   - Original buyer information is preserved in child requests
5. The `is_merged` field is returned in both list and detail APIs
6. Use `parent_merged_requests` array to access child requests

---

## Error Responses

**Validation Error (400):**
```json
{
  "success": false,
  "message": "Validation failed",
  "error": {
    "title": ["Title is required"],
    "category_id": ["Category ID is required"]
  }
}
```

**Request Not Found (404):**
```json
{
  "success": false,
  "message": "Request uuid1 not found",
  "error": {
    "general": ["Request uuid1 not found"]
  }
}
```

**Already Merged (400):**
```json
{
  "success": false,
  "message": "Request uuid1 is already merged",
  "error": {
    "general": ["Request uuid1 is already merged"]
  }
}
```
