const { PrismaClient } = require('@prisma/client');
const CodeGeneratorService = require('../services/codeGeneratorService');
const prisma = new PrismaClient();

const PublicOfferModel = {
  /**
   * Get public offers with filtering and pagination
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @param {Object} sorting - Sorting criteria
   * @returns {Promise<Object>} Paginated offers
   */
  async getPublicOffers(filters = {}, page = 1, limit = 10, sorting = {}) {
    const whereClause = {
      is_deleted: false,
      status: 'Active', // Only show active offers
    };

    // Apply filters
    if (filters.category_id) {
      whereClause.category_id = filters.category_id;
    }

    if (filters.subcategory_id) {
      whereClause.subcategory_id = filters.subcategory_id;
    }

    if (filters.offer_type) {
      whereClause.offer_type = filters.offer_type;
    }

    // Price range filter
    if (filters.min_price || filters.max_price) {
      whereClause.price = {};
      if (filters.min_price) {
        whereClause.price.gte = filters.min_price;
      }
      if (filters.max_price) {
        whereClause.price.lte = filters.max_price;
      }
    }

    // Search filter
    if (filters.search) {
      whereClause.OR = [
        {
          offer_title: {
            contains: filters.search,
            mode: 'insensitive'
          }
        },
        {
          short_description: {
            contains: filters.search,
            mode: 'insensitive'
          }
        },
        {
          description: {
            contains: filters.search,
            mode: 'insensitive'
          }
        }
      ];
    }

    // Build sorting
    const orderBy = {};
    const sortBy = sorting.sort_by || 'created_at';
    const sortOrder = sorting.sort_order || 'desc';
    orderBy[sortBy] = sortOrder;

    // Get total count
    const totalCount = await prisma.offers.count({
      where: whereClause,
    });

    // Get paginated offers
    const offers = await prisma.offers.findMany({
      where: whereClause,
      skip: (page - 1) * limit,
      take: limit,
      orderBy,
      include: {
        seller: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            profile_picture_url: true,
            business_informations: {
              where: {
                is_deleted: false,
                is_active: true
              },
              select: {
                id: true,
                company_name: true,
                description: true,
                logo: true,
                banner: true
              },
              take: 1
            }
          },
        },
        category: {
          select: {
            id: true,
            title: true,
            description: true,
            color: true,
            image: true,
            thumbnail: true
          },
        },
        subcategory: {
          select: {
            id: true,
            title: true,
            description: true,
            color: true,
            image: true,
            thumbnail: true
          },
        },
        offer_attachments: {
          where: {
            is_deleted: false,
            is_public: true
          },
          select: {
            id: true,
            file_path: true,
            file_type: true,
            description: true
          }
        },
        offer_form_field_values: {
          include: {
            form_field: {
              select: {
                id: true,
                label_name: true,
                input_type: true,
                label_subtitle: true,
                sort_order: true
              }
            }
          },
          orderBy: {
            form_field: {
              sort_order: 'asc'
            }
          }
        }
      },
    });

    // Add codes to offers
    const offersWithCodes = CodeGeneratorService.addCodesToOffers(offers);

    return {
      data: offersWithCodes,
      meta: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
    };
  },

  /**
   * Get offer details by ID
   * @param {string} offerId - Offer ID
   * @returns {Promise<Object>} Offer details
   */
  async getOfferDetails(offerId) {
    const offer = await prisma.offers.findFirst({
      where: {
        id: offerId,
        is_deleted: false,
        status: 'Active'
      },
      include: {
        seller: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            profile_picture_url: true,
            created_at: true,
            business_informations: {
              where: {
                is_deleted: false,
                is_active: true
              },
              select: {
                id: true,
                company_name: true,
                description: true,
                logo: true,
                banner: true,
                address: true,
                website: true,
                phone: true,
                email: true
              },
              take: 1
            }
          },
        },
        category: {
          select: {
            id: true,
            title: true,
            description: true,
            color: true,
            image: true,
            thumbnail: true
          },
        },
        subcategory: {
          select: {
            id: true,
            title: true,
            description: true,
            color: true,
            image: true,
            thumbnail: true
          },
        },
        offer_attachments: {
          where: {
            is_deleted: false,
            is_public: true
          },
          select: {
            id: true,
            file_path: true,
            file_type: true,
            file_size: true,
            description: true
          }
        },
        offer_form_field_values: {
          include: {
            form_field: {
              select: {
                id: true,
                label_name: true,
                input_type: true,
                label_subtitle: true,
                placeholder: true,
                options: true,
                sort_order: true
              }
            }
          },
          orderBy: {
            form_field: {
              sort_order: 'asc'
            }
          }
        }
      },
    });

    if (!offer) {
      return null;
    }

    // Add code to offer
    return CodeGeneratorService.addCodeToOffer(offer);
  },

  /**
   * Get offers by seller
   * @param {string} sellerId - Seller ID
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated offers
   */
  async getOffersBySeller(sellerId, page = 1, limit = 10) {
    const whereClause = {
      seller_id: sellerId,
      is_deleted: false,
      status: 'Active'
    };

    // Get total count
    const totalCount = await prisma.offers.count({
      where: whereClause,
    });

    // Get paginated offers
    const offers = await prisma.offers.findMany({
      where: whereClause,
      skip: (page - 1) * limit,
      take: limit,
      orderBy: { created_at: 'desc' },
      include: {
        category: {
          select: {
            id: true,
            title: true,
            description: true,
            color: true,
            image: true,
            thumbnail: true
          },
        },
        subcategory: {
          select: {
            id: true,
            title: true,
            description: true,
            color: true,
            image: true,
            thumbnail: true
          },
        },
        offer_attachments: {
          where: {
            is_deleted: false,
            is_public: true
          },
          select: {
            id: true,
            file_path: true,
            file_type: true,
            description: true
          }
        }
      },
    });

    // Add codes to offers
    const offersWithCodes = CodeGeneratorService.addCodesToOffers(offers);

    return {
      data: offersWithCodes,
      meta: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
    };
  },

  /**
   * Get offers by category
   * @param {string} categoryId - Category ID
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated offers
   */
  async getOffersByCategory(categoryId, page = 1, limit = 10) {
    const whereClause = {
      category_id: categoryId,
      is_deleted: false,
      status: 'Active'
    };

    // Get total count
    const totalCount = await prisma.offers.count({
      where: whereClause,
    });

    // Get paginated offers
    const offers = await prisma.offers.findMany({
      where: whereClause,
      skip: (page - 1) * limit,
      take: limit,
      orderBy: { created_at: 'desc' },
      include: {
        seller: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            profile_picture_url: true,
          },
        },
        subcategory: {
          select: {
            id: true,
            title: true,
            description: true,
          },
        },
        offer_attachments: {
          where: {
            is_deleted: false,
            is_public: true
          },
          select: {
            id: true,
            file_path: true,
            file_type: true,
            description: true
          }
        }
      },
    });

    // Add codes to offers
    const offersWithCodes = CodeGeneratorService.addCodesToOffers(offers);

    return {
      data: offersWithCodes,
      meta: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
    };
  },

  /**
   * Get offers by subcategory
   * @param {string} subcategoryId - Subcategory ID
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated offers
   */
  async getOffersBySubcategory(subcategoryId, page = 1, limit = 10) {
    const whereClause = {
      subcategory_id: subcategoryId,
      is_deleted: false,
      status: 'Active'
    };

    // Get total count
    const totalCount = await prisma.offers.count({
      where: whereClause,
    });

    // Get paginated offers
    const offers = await prisma.offers.findMany({
      where: whereClause,
      skip: (page - 1) * limit,
      take: limit,
      orderBy: { created_at: 'desc' },
      include: {
        seller: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            profile_picture_url: true,
          },
        },
        category: {
          select: {
            id: true,
            title: true,
            description: true,
          },
        },
        offer_attachments: {
          where: {
            is_deleted: false,
            is_public: true
          },
          select: {
            id: true,
            file_path: true,
            file_type: true,
            description: true
          }
        }
      },
    });

    // Add codes to offers
    const offersWithCodes = CodeGeneratorService.addCodesToOffers(offers);

    return {
      data: offersWithCodes,
      meta: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
    };
  },

  /**
   * Get offer statistics
   * @returns {Promise<Object>} Offer statistics
   */
  async getOfferStatistics() {
    const totalOffers = await prisma.offers.count({
      where: {
        is_deleted: false,
        status: 'Active'
      }
    });

    const offersByType = await prisma.offers.groupBy({
      by: ['offer_type'],
      where: {
        is_deleted: false,
        status: 'Active'
      },
      _count: {
        id: true
      }
    });

    const offersByCategory = await prisma.offers.groupBy({
      by: ['category_id'],
      where: {
        is_deleted: false,
        status: 'Active',
        category_id: {
          not: null
        }
      },
      _count: {
        id: true
      },
      take: 10
    });

    return {
      total_offers: totalOffers,
      offers_by_type: offersByType,
      offers_by_category: offersByCategory
    };
  }
};

module.exports = PublicOfferModel;
