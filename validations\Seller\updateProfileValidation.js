const { body } = require('express-validator');

const updateProfileValidation = [
  body('first_name')
    .optional()
    .isString()
    .withMessage('First name must be a string')
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),

  body('last_name')
    .optional()
    .isString()
    .withMessage('Last name must be a string')
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),

  body('phone_number')
    .optional()
    .isString()
    .withMessage('Phone number must be a string')
    .matches(/^\+?[0-9]{10,15}$/)
    .withMessage('Phone number must be a valid format'),

  body('address')
    .optional()
    .isString()
    .withMessage('Address must be a string'),

  body('gender')
    .optional()
    .isIn(['male', 'female', 'other', ''])
    .withMessage('Gender must be male, female, other, or empty'),

  body('date_of_birth')
    .optional()
    .isISO8601()
    .withMessage('Date of birth must be a valid date')
    .custom(value => {
      const date = new Date(value);
      const now = new Date();
      const minAge = new Date(now.getFullYear() - 18, now.getMonth(), now.getDate());
      if (date > minAge) {
        throw new Error('You must be at least 18 years old');
      }
      return true;
    }),
];

module.exports = updateProfileValidation;
