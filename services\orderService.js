const OrderModel = require('../models/orderModel');
const ApiError = require('../utils/apiError');

class OrderService {
  /**
   * Create a new order from cart
   * @param {string} buyerId - Buyer ID
   * @param {string} cartId - Cart ID
   * @param {Object} billingInfo - Billing information
   * @param {Object} cardInfo - Card information
   * @returns {Promise<Object>} Created order
   */
  static async createOrderFromCart(buyerId, cartId, billingInfo, cardInfo) {
    try {
      // In a real application, you would process the payment here
      // using a payment gateway like Stripe, PayPal, etc.
      // For now, we'll just create the order with a pending payment status

      // Sanitize billing info to include only necessary fields
      const sanitizedBillingInfo = {
        full_name: billingInfo.full_name,
        email: billingInfo.email,
        address: billingInfo.address || '',
        city: billingInfo.city,
        state: billingInfo.state,
        amount: billingInfo.amount || cardInfo.amount // Use either one depending on where it's provided
      };

      // Create the order
      const order = await OrderModel.createOrderFromCart(
        buyerId,
        cartId,
        sanitizedBillingInfo,
        cardInfo
      );

      return order;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(400, `Failed to create order: ${error.message}`);
    }
  }

  /**
   * Get orders for a buyer
   * @param {string} buyerId - Buyer ID
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated orders
   */
  static async getBuyerOrders(buyerId, filters = {}, page = 1, limit = 10) {
    try {
      return await OrderModel.getBuyerOrders(buyerId, filters, page, limit);
    } catch (error) {
      throw new ApiError(500, `Failed to get buyer orders: ${error.message}`);
    }
  }
  /**
   * Get all orders for admin
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated orders
   */
  static async getAllOrders(filters = {}, page = 1, limit = 10) {
    try {
      return await OrderModel.getAllOrders(filters, page, limit);
    } catch (error) {
      throw new ApiError(500, `Failed to get orders: ${error.message}`);
    }
  }

  /**
   * Get order details by ID
   * @param {string} orderId - Order ID
   * @returns {Promise<Object>} Order details
   */
  static async getOrderById(orderId) {
    try {
      const order = await OrderModel.getOrderById(orderId);
      if (!order) {
        throw new ApiError(404, 'Order not found');
      }
      return order;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, `Failed to get order details: ${error.message}`);
    }
  }
}

module.exports = OrderService;
