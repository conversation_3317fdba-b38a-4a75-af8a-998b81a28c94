model sub_categories {
  id            String     @id @default(uuid())
  category_id   String     @db.VarChar(50)
  title         String     
  description   String?
  color         String?    // Hex color code for subcategory
  image         String?    // URL to main subcategory image
  thumbnail     String?    // URL to thumbnail image
  created_at    DateTime   @default(now())
  updated_at    DateTime   @updatedAt
  is_deleted    Boolean    @default(false)
  is_featured   <PERSON>olean    @default(false)
  sort_order    Int?       // For custom sorting within parent category

  // Relations
  category      categories @relation(fields: [category_id], references: [id], name: "Category")
  requests      requests[] @relation("RequestSubCategory")
  offers        offers[] @relation("OfferSubCategory")
  products      products[] @relation("ProductSubCategory")
  services      services[] @relation("ServiceSubCategory")
  translations  sub_category_translations[]
  form_fields   subcategory_form_fields[]

  // Metadata
  seo_title       String?
  seo_description String?
  seo_keywords    String?

  // Indexes for better performance
  @@index([category_id])
  @@index([sort_order])
}

model sub_category_translations {
  id              String   @id @default(uuid())
  sub_category_id String
  language        String   // e.g., 'en', 'bn', 'fr'
  title           String
  description     String?
  seo_title       String?
  seo_description String?
  seo_keywords    String?

  // Relations
  sub_category    sub_categories @relation(fields: [sub_category_id], references: [id])

  @@unique([sub_category_id, language]) // Prevent duplicate translations
}