const { body } = require('express-validator');

const bulkRequestActionValidation = [
  body('request_ids')
    .notEmpty()
    .withMessage('Request IDs are required')
    .isArray({ min: 1 })
    .withMessage('Request IDs must be a non-empty array')
    .custom((value) => {
      // Check if all elements are valid UUIDs
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      const invalidIds = value.filter(id => !uuidRegex.test(id));
      
      if (invalidIds.length > 0) {
        throw new Error(`Invalid request IDs: ${invalidIds.join(', ')}`);
      }
      
      // Check for duplicates
      const uniqueIds = [...new Set(value)];
      if (uniqueIds.length !== value.length) {
        throw new Error('Duplicate request IDs are not allowed');
      }
      
      // Limit the number of requests that can be processed at once
      if (value.length > 100) {
        throw new Error('Cannot process more than 100 requests at once');
      }
      
      return true;
    }),

  body('action')
    .notEmpty()
    .withMessage('Action is required')
    .isIn(['approve', 'reject', 'delete'])
    .withMessage('Action must be one of: approve, reject, delete'),

  body('note')
    .optional()
    .isString()
    .withMessage('Note must be a string')
    .isLength({ max: 1000 })
    .withMessage('Note must not exceed 1000 characters')
    .custom((value, { req }) => {
      // Note is required for approve and reject actions
      if ((req.body.action === 'approve' || req.body.action === 'reject') && !value) {
        throw new Error('Note is required for approve and reject actions');
      }
      return true;
    })
];

module.exports = {
  bulkRequestActionValidation
};
