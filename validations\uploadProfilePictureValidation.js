const { body } = require('express-validator');

const uploadProfilePictureValidation = [
  // Custom validation for file upload
  (req, res, next) => {
    // Check if file is provided
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: "Validation failed",
        errors: {
          profile_picture: ["Profile picture file is required"]
        }
      });
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(req.file.mimetype)) {
      return res.status(400).json({
        success: false,
        message: "Validation failed",
        errors: {
          profile_picture: ["Invalid file type. Only JPEG, JPG, PNG, and GIF files are allowed"]
        }
      });
    }

    // Validate file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (req.file.size > maxSize) {
      return res.status(400).json({
        success: false,
        message: "Validation failed",
        errors: {
          profile_picture: ["File size too large. Maximum size is 5MB"]
        }
      });
    }

    // Validate file name
    if (!req.file.originalname || req.file.originalname.trim() === '') {
      return res.status(400).json({
        success: false,
        message: "Validation failed",
        errors: {
          profile_picture: ["Invalid file name"]
        }
      });
    }

    next();
  }
];

module.exports = { uploadProfilePictureValidation };
