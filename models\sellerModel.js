const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const CodeGeneratorService = require('../services/codeGeneratorService');
const BusinessInformationModel = require('./businessInformationModel');

class SellerModel {
  /**
   * Get assigned requests for a seller
   * @param {string} sellerId - Seller ID
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated assigned requests
   */
  static async getAssignedRequests(sellerId, filters = {}, page = 1, limit = 10) {
    const whereClause = {
      seller_id: sellerId,
    };

    // Apply status filter if provided
    if (filters.status) {
      whereClause.status = filters.status;
    }

    // Get total count of assigned requests
    const totalCount = await prisma.request_assigned_sellers.count({
      where: whereClause,
    });

    // Get paginated assigned requests
    const assignedRequests = await prisma.request_assigned_sellers.findMany({
      where: whereClause,
      skip: (page - 1) * limit,
      take: limit,
      orderBy: { assigned_at: 'desc' },
      include: {
        request: {
          include: {
            buyer: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
                email: true,
                profile_picture_url: true,
              },
            },
            category: true,
            sub_category: true,
          },
        },
        seller: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            profile_picture_url: true,
          },
        },
        assigner: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
          },
        },
      },
    });

    // Add combined name for buyers, sellers, and assigners
    const assignedRequestsWithNames = assignedRequests.map(assignment => ({
      ...assignment,
      request: {
        ...assignment.request,
        buyer: {
          ...assignment.request.buyer,
          name: `${assignment.request.buyer.first_name} ${assignment.request.buyer.last_name}`
        }
      },
      seller: {
        ...assignment.seller,
        name: `${assignment.seller.first_name} ${assignment.seller.last_name}`
      },
      assigner: assignment.assigner ? {
        ...assignment.assigner,
        name: `${assignment.assigner.first_name} ${assignment.assigner.last_name}`
      } : null
    }));

    return {
      data: assignedRequestsWithNames,
      meta: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
    };
  }

  /**
   * Accept an assigned request
   * @param {string} assignmentId - Assignment ID
   * @param {string} sellerId - Seller ID
   * @returns {Promise<Object>} Updated assignment
   */
  static async acceptAssignedRequest(assignmentId, sellerId) {
    // Check if the assignment exists and belongs to the seller
    const assignment = await prisma.request_assigned_sellers.findFirst({
      where: {
        id: assignmentId,
        seller_id: sellerId,
      },
      include: {
        request: true,
      },
    });

    if (!assignment) {
      throw new Error('Assignment not found or does not belong to this seller');
    }

    if (assignment.status !== 'Pending') {
      throw new Error(`Cannot accept assignment with status '${assignment.status}'`);
    }

    // Update the assignment status to 'Accepted'
    const updatedAssignment = await prisma.request_assigned_sellers.update({
      where: { id: assignmentId },
      data: { status: 'Accepted' },
      include: {
        request: {
          include: {
            buyer: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
                email: true,
                profile_picture_url: true,
              },
            },
            category: true,
            sub_category: true,
          },
        },
        seller: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            profile_picture_url: true,
          },
        },
        assigner: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
          },
        },
      },
    });

    // Add combined name fields
    const assignmentWithNames = {
      ...updatedAssignment,
      request: {
        ...updatedAssignment.request,
        buyer: {
          ...updatedAssignment.request.buyer,
          name: `${updatedAssignment.request.buyer.first_name} ${updatedAssignment.request.buyer.last_name}`
        }
      },
      seller: {
        ...updatedAssignment.seller,
        name: `${updatedAssignment.seller.first_name} ${updatedAssignment.seller.last_name}`
      },
      assigner: updatedAssignment.assigner ? {
        ...updatedAssignment.assigner,
        name: `${updatedAssignment.assigner.first_name} ${updatedAssignment.assigner.last_name}`
      } : null
    };

    return assignmentWithNames;
  }

  /**
   * Reject an assigned request
   * @param {string} assignmentId - Assignment ID
   * @param {string} sellerId - Seller ID
   * @param {string} reason - Rejection reason
   * @returns {Promise<Object>} Updated assignment
   */
  static async rejectAssignedRequest(assignmentId, sellerId, reason) {
    // Check if the assignment exists and belongs to the seller
    const assignment = await prisma.request_assigned_sellers.findFirst({
      where: {
        id: assignmentId,
        seller_id: sellerId,
      },
    });

    if (!assignment) {
      throw new Error('Assignment not found or does not belong to this seller');
    }

    if (assignment.status !== 'Pending') {
      throw new Error(`Cannot reject assignment with status '${assignment.status}'`);
    }

    // Update the assignment status to 'Rejected'
    const updatedAssignment = await prisma.request_assigned_sellers.update({
      where: { id: assignmentId },
      data: {
        status: 'Rejected',
        notes: reason || 'No reason provided'
      },
      include: {
        request: {
          include: {
            buyer: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
                email: true,
                profile_picture_url: true,
              },
            },
            category: true,
            sub_category: true,
          },
        },
        seller: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            profile_picture_url: true,
          },
        },
        assigner: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
          },
        },
      },
    });

    // Add combined name fields
    const assignmentWithNames = {
      ...updatedAssignment,
      request: {
        ...updatedAssignment.request,
        buyer: {
          ...updatedAssignment.request.buyer,
          name: `${updatedAssignment.request.buyer.first_name} ${updatedAssignment.request.buyer.last_name}`
        }
      },
      seller: {
        ...updatedAssignment.seller,
        name: `${updatedAssignment.seller.first_name} ${updatedAssignment.seller.last_name}`
      },
      assigner: updatedAssignment.assigner ? {
        ...updatedAssignment.assigner,
        name: `${updatedAssignment.assigner.first_name} ${updatedAssignment.assigner.last_name}`
      } : null
    };

    return assignmentWithNames;
  }

  /**
   * Create an offer for a request
   * @param {Object} offerData - Offer data
   * @param {string} sellerId - Seller ID
   * @returns {Promise<Object>} Created offer
   */
  static async createOffer(offerData, sellerId) {
    const OfferFormFieldValueModel = require('./offerFormFieldValueModel');

    // If request_id is provided, check assignment and existing offers
    if (offerData.request_id) {
      // Check if the request exists
      const request = await prisma.requests.findUnique({
        where: { id: offerData.request_id },
      });

      if (!request) {
        throw new Error('Request not found');
      }

      // Check if the seller is assigned to this request and has accepted it
      const assignment = await prisma.request_assigned_sellers.findFirst({
        where: {
          request_id: offerData.request_id,
          seller_id: sellerId,
          status: 'Accepted',
        },
      });

      if (!assignment) {
        throw new Error('You must be assigned to and have accepted this request to create an offer');
      }

      // Check if the seller already has an active offer for this request
      const existingOffer = await prisma.offers.findFirst({
        where: {
          request_id: offerData.request_id,
          seller_id: sellerId,
          is_deleted: false,
        },
      });

      if (existingOffer) {
        throw new Error('You already have an offer for this request');
      }
    }

    // If no request_id, category_id and subcategory_id are required
    if (!offerData.request_id && (!offerData.category_id || !offerData.subcategory_id)) {
      throw new Error('Category ID and Subcategory ID are required when not responding to a request');
    }

    // Get category and subcategory from request if request_id is provided
    let categoryId = offerData.category_id;
    let subcategoryId = offerData.subcategory_id;

    if (offerData.request_id) {
      const request = await prisma.requests.findUnique({
        where: { id: offerData.request_id },
        select: { category_id: true, sub_category_id: true }
      });

      if (request) {
        categoryId = request.category_id;
        subcategoryId = request.sub_category_id;
      }
    }

    // Validate form fields if provided
    let validatedFormFields = [];
    if (offerData.form_fields && offerData.form_fields.length > 0 && subcategoryId) {
      const validation = await OfferFormFieldValueModel.validateFormFieldValues(
        subcategoryId,
        offerData.form_fields
      );

      if (!validation.isValid) {
        throw new Error(`Form validation failed: ${validation.errors.map(e => e.message).join(', ')}`);
      }

      validatedFormFields = validation.validatedValues;
    }

    // Create the offer
    const offer = await prisma.offers.create({
      data: {
        request_id: offerData.request_id || null,
        seller_id: sellerId,
        offer_title: offerData.offer_title,
        category_id: categoryId,
        subcategory_id: subcategoryId,
        short_description: offerData.short_description,
        price: offerData.price,
        discount: offerData.discount,
        quantity: offerData.quantity || 1,
        delivery_time: offerData.delivery_time,
        message: offerData.message,
        description: offerData.description,
        offer_type: offerData.offer_type || 'service',
        status: 'Pending',
        // Create offer status change record
        offer_status_changes: {
          create: {
            status: 'Pending',
            updated_by: sellerId,
          },
        },
      },
      include: {
        request: offerData.request_id ? {
          include: {
            buyer: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
                email: true,
              },
            },
          },
        } : undefined,
        category: {
          select: {
            id: true,
            title: true,
            description: true,
          },
        },
        subcategory: {
          select: {
            id: true,
            title: true,
            description: true,
          },
        },
        seller: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            profile_picture_url: true,
          },
        },
        offer_status_changes: {
          orderBy: { created_at: 'desc' },
          take: 1,
        },
      },
    });

    // Create form field values if provided
    if (validatedFormFields.length > 0) {
      await OfferFormFieldValueModel.createFormFieldValues(offer.id, validatedFormFields);
    }

    // Generate and add the offer code
    return CodeGeneratorService.addCodeToOffer(offer);
  }

  /**
   * Get request details for a seller
   * @param {string} requestId - Request ID
   * @param {string} sellerId - Seller ID
   * @returns {Promise<Object>} Request details
   */
  static async getRequestDetailsForSeller(requestId, sellerId) {
    // Check if the seller is assigned to this request
    const assignment = await prisma.request_assigned_sellers.findFirst({
      where: {
        request_id: requestId,
        seller_id: sellerId,
      },
      include: {
        assigner: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
          },
        },
      },
    });

    if (!assignment) {
      throw new Error('You are not assigned to this request');
    }

    // Get the request details without buyer and other sellers information
    const request = await prisma.requests.findUnique({
      where: { id: requestId },
      include: {
        category: true,
        sub_category: true,
        request_attachments: true,
        request_statuses: {
          orderBy: { created_at: 'desc' },
          take: 1,
        },
        assigned_sellers: {
          where: {
            seller_id: sellerId, // Only include the current seller's assignment
          },
          include: {
            assigner: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
              },
            },
          },
        },
      },
    });

    if (!request) {
      throw new Error('Request not found');
    }

    // Get the seller's offer for this request, if any
    const offer = await prisma.offers.findFirst({
      where: {
        request_id: requestId,
        seller_id: sellerId,
        is_deleted: false,
      },
      include: {
        offer_status_changes: {
          orderBy: { created_at: 'desc' },
          take: 1,
        },
        offer_attachments: {
          where: {
            is_deleted: false,
          },
        },
        offer_negotiations: {
          orderBy: { created_at: 'desc' },
        },
      },
    });

    // Format the assignment data
    const assignmentData = {
      id: assignment.id,
      request_id: assignment.request_id,
      assigned_at: assignment.assigned_at,
      status: assignment.status,
      notes: assignment.notes,
      assigner: assignment.assigner ? {
        ...assignment.assigner,
        name: `${assignment.assigner.first_name} ${assignment.assigner.last_name}`,
      } : null,
    };

    // Format offer data if it exists
    let formattedOffer = null;
    if (offer) {
      formattedOffer = {
        id: offer.id,
        price: offer.price,
        delivery_time: offer.delivery_time,
        message: offer.message,
        description: offer.description,
        status: offer.status,
        created_at: offer.created_at,
        updated_at: offer.updated_at,
        attachments: offer.offer_attachments,
        status_history: offer.offer_status_changes,
        negotiations: offer.offer_negotiations,
      };
    }

    // Format the assigned seller data - only include assignment info, not seller info
    const formattedAssignedSeller = request.assigned_sellers.length > 0 ? {
      id: request.assigned_sellers[0].id,
      request_id: request.assigned_sellers[0].request_id,
      assigned_at: request.assigned_sellers[0].assigned_at,
      status: request.assigned_sellers[0].status,
      notes: request.assigned_sellers[0].notes,
      assigner: request.assigned_sellers[0].assigner ? {
        ...request.assigned_sellers[0].assigner,
        name: `${request.assigned_sellers[0].assigner.first_name} ${request.assigned_sellers[0].assigner.last_name}`,
      } : null,
    } : null;

    // Add assignment data and offer to request
    const requestWithAssignment = {
      ...request,
      assigned_seller: formattedAssignedSeller, // Include only the assignment info
      assignment: assignmentData, // Keep the old format for backward compatibility
      offer: formattedOffer,
    };

    // Generate and add the request code
    const requestWithCode = CodeGeneratorService.addCodeToRequest(requestWithAssignment);

    return requestWithCode;
  }

  /**
   * Get offers created by a seller
   * @param {string} sellerId - Seller ID
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated offers
   */
  static async getSellerOffers(sellerId, filters = {}, page = 1, limit = 10) {
    const whereClause = {
      seller_id: sellerId,
      is_deleted: false,
    };

    // Apply status filter if provided
    if (filters.status) {
      whereClause.status = filters.status;
    }

    // Apply request_id filter if provided
    if (filters.request_id) {
      whereClause.request_id = filters.request_id;
    }

    // Get total count of offers
    const totalCount = await prisma.offers.count({
      where: whereClause,
    });

    // Get paginated offers
    const offers = await prisma.offers.findMany({
      where: whereClause,
      skip: (page - 1) * limit,
      take: limit,
      orderBy: { created_at: 'desc' },
      include: {
        request: {
          include: {
            buyer: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
                email: true,
                profile_picture_url: true,
              },
            },
            category: true,
            sub_category: true,
          },
        },
        offer_status_changes: {
          orderBy: { created_at: 'desc' },
          take: 1,
        },
      },
    });

    // Add codes to all offers
    const offersWithCodes = CodeGeneratorService.addCodesToOffers(offers);

    return {
      data: offersWithCodes,
      meta: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
    };
  }

  /**
   * Find a user by email
   * @param {string} email - User email
   * @returns {Promise<Object|null>} User object or null if not found
   */
  static async findUserByEmail(email) {
    return await prisma.users.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
      },
    });
  }

  /**
   * Find a user by phone number
   * @param {string} phone_number - User phone number
   * @returns {Promise<Object|null>} User object or null if not found
   */
  static async findUserByPhone(phone_number) {
    return await prisma.users.findUnique({
      where: { phone_number },
      select: {
        id: true,
        phone_number: true,
      },
    });
  }

  /**
   * Update an offer
   * @param {string} offerId - Offer ID
   * @param {string} sellerId - Seller ID
   * @param {Object} offerData - Offer data to update
   * @returns {Promise<Object>} Updated offer
   */
  static async updateOffer(offerId, sellerId, offerData) {
    // Check if the offer exists and belongs to the seller
    const existingOffer = await prisma.offers.findFirst({
      where: {
        id: offerId,
        seller_id: sellerId,
        is_deleted: false,
      },
    });

    if (!existingOffer) {
      throw new Error('Offer not found or does not belong to this seller');
    }

    // Check if the offer can be updated (only pending offers can be updated)
    if (existingOffer.status !== 'Pending') {
      throw new Error(`Cannot update offer with status '${existingOffer.status}'. Only pending offers can be updated.`);
    }

    // Create a status change record if status is being updated
    let statusChangeData = null;
    if (offerData.status && offerData.status !== existingOffer.status) {
      statusChangeData = {
        offer_status_changes: {
          create: {
            status: offerData.status,
            previous_status: existingOffer.status,
            updated_by: sellerId,
          },
        },
      };
    }

    // Update the offer
    const updatedOffer = await prisma.offers.update({
      where: { id: offerId },
      data: {
        ...offerData,
        ...(statusChangeData || {}),
      },
      include: {
        request: {
          include: {
            buyer: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
                email: true,
              },
            },
            category: true,
            sub_category: true,
          },
        },
        seller: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            profile_picture_url: true,
          },
        },
        assigned_seller: true, // Include the assigned seller relationship
        offer_status_changes: {
          orderBy: { created_at: 'desc' },
          take: 1,
        },
      },
    });

    // Generate and add the offer code
    return CodeGeneratorService.addCodeToOffer(updatedOffer);
  }

  /**
   * Get seller profile by ID
   * @param {string} sellerId - Seller ID
   * @returns {Promise<Object>} Seller profile with business information
   */
  static async getSellerProfile(sellerId) {
    const user = await prisma.users.findUnique({
      where: {
        id: sellerId,
        roles: {
          some: {
            role: {
              name: 'Seller'
            }
          }
        }
      },
      select: {
        id: true,
        first_name: true,
        last_name: true,
        email: true,
        phone_number: true,
        profile_picture_url: true,
        gender: true,
        date_of_birth: true,
        address: true,
        is_email_verified: true,
        is_approved: true,
        status: true,
        created_at: true,
        updated_at: true,
        roles: {
          include: {
            role: true
          }
        },
        business_informations: {
          where: {
            is_deleted: false,
            is_active: true
          },
          orderBy: {
            created_at: 'desc'
          }
        }
      }
    });

    if (!user) return null;

    // Transform roles to simple array
    user.roles = user.roles.map(userRole => userRole.role.name);

    // Get seller statistics
    const stats = await SellerModel.getSellerStatistics(sellerId);

    return {
      ...user,
      stats
    };
  }

  /**
   * Get seller statistics
   * @param {string} sellerId - Seller ID
   * @returns {Promise<Object>} Seller statistics
   */
  static async getSellerStatistics(sellerId) {
    // Get count of assigned requests
    const assignedRequestsCount = await prisma.request_assigned_sellers.count({
      where: {
        seller_id: sellerId
      }
    });

    // Get count of offers created
    const offersCount = await prisma.offers.count({
      where: {
        seller_id: sellerId,
        is_deleted: false
      }
    });

    // Get count of completed transactions
    const completedTransactions = await prisma.offers.count({
      where: {
        seller_id: sellerId,
        status: 'Completed',
        is_deleted: false
      }
    });

    // Get count of business information
    const businessCount = await prisma.business_informations.count({
      where: {
        seller_id: sellerId,
        is_deleted: false
      }
    });

    return {
      assigned_requests: assignedRequestsCount,
      offers_created: offersCount,
      completed_transactions: completedTransactions,
      business_informations: businessCount
    };
  }

  /**
   * Update seller profile
   * @param {string} sellerId - Seller ID
   * @param {Object} profileData - Profile data to update
   * @returns {Promise<Object>} Updated seller profile
   */
  static async updateSellerProfile(sellerId, profileData) {
    // Convert date_of_birth to Date object if it's a string
    if (profileData.date_of_birth && typeof profileData.date_of_birth === 'string') {
      profileData.date_of_birth = new Date(profileData.date_of_birth);
    }

    // Update the user
    const updatedUser = await prisma.users.update({
      where: { id: sellerId },
      data: profileData,
      select: {
        id: true,
        first_name: true,
        last_name: true,
        email: true,
        phone_number: true,
        profile_picture_url: true,
        gender: true,
        date_of_birth: true,
        address: true,
        created_at: true,
        updated_at: true,
        roles: {
          include: {
            role: true
          }
        }
      }
    });

    // Transform roles to simple array
    updatedUser.roles = updatedUser.roles.map(userRole => userRole.role.name);

    return updatedUser;
  }

  // Business Information Methods

  /**
   * Create business information for a seller
   * @param {string} sellerId - Seller ID
   * @param {Object} businessData - Business information data
   * @returns {Promise<Object>} Created business information
   */
  static async createBusinessInformation(sellerId, businessData) {
    return await BusinessInformationModel.createBusinessInformation(businessData, sellerId);
  }

  /**
   * Get all business information for a seller
   * @param {string} sellerId - Seller ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} List of business information
   */
  static async getSellerBusinessInformations(sellerId, options = {}) {
    return await BusinessInformationModel.getBusinessInformationsBySeller(sellerId, options);
  }

  /**
   * Get business information by ID
   * @param {string} businessId - Business information ID
   * @param {string} sellerId - Seller ID
   * @returns {Promise<Object|null>} Business information
   */
  static async getBusinessInformationById(businessId, sellerId) {
    return await BusinessInformationModel.getBusinessInformationById(businessId, sellerId);
  }

  /**
   * Update business information
   * @param {string} businessId - Business information ID
   * @param {string} sellerId - Seller ID
   * @param {Object} updateData - Update data
   * @returns {Promise<Object>} Updated business information
   */
  static async updateBusinessInformation(businessId, sellerId, updateData) {
    return await BusinessInformationModel.updateBusinessInformation(businessId, updateData, sellerId);
  }

  /**
   * Delete business information
   * @param {string} businessId - Business information ID
   * @param {string} sellerId - Seller ID
   * @returns {Promise<Object>} Deleted business information
   */
  static async deleteBusinessInformation(businessId, sellerId) {
    return await BusinessInformationModel.deleteBusinessInformation(businessId, sellerId);
  }
}

module.exports = SellerModel;
