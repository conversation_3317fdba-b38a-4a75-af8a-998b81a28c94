# Password Validation Documentation

## Overview

Strong password validation has been implemented across all authentication endpoints to ensure user account security. All passwords must meet specific complexity requirements.

## Password Requirements

### Minimum Requirements
- **Minimum Length**: 8 characters
- **Lowercase Letter**: At least 1 (a-z)
- **Uppercase Letter**: At least 1 (A-Z)
- **Number**: At least 1 (0-9)
- **Special Character**: At least 1 (!@#$%^&*)

### Regular Expression
```javascript
/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])[A-Za-z\d!@#$%^&*]/
```

### Validation Message
```
"Password must contain at least 1 lowercase letter, 1 uppercase letter, 1 number (0-9), and 1 special character (!@#$%^&*)"
```

## Affected Endpoints

### 1. User Registration
**Endpoint**: `POST /api/auth/register`

**Payload**:
```json
{
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "email": "<EMAIL>",
  "password": "MySecure123!",
  "phone_number": "+**********",
  "role": "Buyer"
}
```

**Password Validation Error Response**:
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "password": [
      "Password must contain at least 1 lowercase letter, 1 uppercase letter, 1 number (0-9), and 1 special character (!@#$%^&*)"
    ]
  }
}
```

### 2. Admin Create User
**Endpoint**: `POST /api/admin/users`

**Payload**:
```json
{
  "first_name": "Jane",
  "last_name": "Smith",
  "email": "<EMAIL>",
  "password": "AdminPass123!",
  "phone_number": "+1987654321",
  "role": "Admin"
}
```

### 3. Forgot Password
**Endpoint**: `POST /api/auth/forgot-password`

**Payload**:
```json
{
  "email": "<EMAIL>"
}
```

**Validation**: Email format validation only (no password validation needed)

### 4. Reset Password
**Endpoint**: `POST /api/auth/reset-password`

**Payload**:
```json
{
  "token": "reset_token_from_email",
  "newPassword": "NewSecure123!",
  "confirmPassword": "NewSecure123!"
}
```

**Validation Rules**:
- `token`: Required, minimum 10 characters
- `newPassword`: Must meet password complexity requirements
- `confirmPassword`: Must match `newPassword`

**Error Response Example**:
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "newPassword": [
      "Password must contain at least 1 lowercase letter, 1 uppercase letter, 1 number (0-9), and 1 special character (!@#$%^&*)"
    ],
    "confirmPassword": [
      "Password confirmation does not match the new password"
    ]
  }
}
```

### 5. Change Password
**Endpoint**: `POST /api/auth/change-password`

**Headers**: `Authorization: Bearer <jwt_token>`

**Payload**:
```json
{
  "currentPassword": "OldPassword123!",
  "newPassword": "NewSecure456@",
  "confirmPassword": "NewSecure456@"
}
```

**Validation Rules**:
- `currentPassword`: Required
- `newPassword`: Must meet complexity requirements and be different from current password
- `confirmPassword`: Must match `newPassword`

## Password Examples

### ✅ Valid Passwords
- `MySecure123!`
- `Password1@`
- `StrongPass9#`
- `Complex2024$`
- `SecureKey8%`

### ❌ Invalid Passwords
- `password` (no uppercase, number, or special character)
- `PASSWORD123` (no lowercase or special character)
- `MyPassword` (no number or special character)
- `Pass123` (less than 8 characters)
- `MySecure123` (no special character)
- `mysecure123!` (no uppercase)
- `MYSECURE123!` (no lowercase)

## Implementation Details

### Validation Files Updated

1. **`validations/registerValidation.js`**
   - Updated password validation for user registration

2. **`validations/createUserValidation.js`**
   - Updated password validation for admin user creation

3. **`validations/Auth/resetPasswordValidation.js`** (New)
   - Reset password validation
   - Forgot password validation
   - Change password validation

### Controller Updates

**`controllers/authController.js`**
- Added validation error handling to:
  - `forgotPassword()` method
  - `resetPassword()` method
  - `changePassword()` method

### Route Updates

**`routes/authRoutes.js`**
- Added validation middleware to:
  - `/forgot-password` route
  - `/reset-password` route
  - `/change-password` route

## Frontend Integration

### Registration Form Validation
```javascript
function validatePassword(password) {
  const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])[A-Za-z\d!@#$%^&*]/;
  
  if (password.length < 8) {
    return "Password must be at least 8 characters long";
  }
  
  if (!regex.test(password)) {
    return "Password must contain at least 1 lowercase letter, 1 uppercase letter, 1 number (0-9), and 1 special character (!@#$%^&*)";
  }
  
  return null; // Valid password
}

// Usage in form
const passwordError = validatePassword(formData.password);
if (passwordError) {
  setErrors({ password: passwordError });
  return;
}
```

### Password Strength Indicator
```javascript
function getPasswordStrength(password) {
  let score = 0;
  
  if (password.length >= 8) score++;
  if (/[a-z]/.test(password)) score++;
  if (/[A-Z]/.test(password)) score++;
  if (/\d/.test(password)) score++;
  if (/[!@#$%^&*]/.test(password)) score++;
  
  const strength = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
  return {
    score: score,
    label: strength[score] || 'Very Weak',
    isValid: score === 5
  };
}
```

### Reset Password Form
```javascript
async function resetPassword(token, newPassword, confirmPassword) {
  try {
    const response = await fetch('/api/auth/reset-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: token,
        newPassword: newPassword,
        confirmPassword: confirmPassword
      })
    });

    const result = await response.json();
    
    if (!result.success) {
      // Handle validation errors
      if (result.errors) {
        Object.keys(result.errors).forEach(field => {
          console.error(`${field}: ${result.errors[field].join(', ')}`);
        });
      }
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Reset password error:', error);
    return false;
  }
}
```

## Security Benefits

1. **Brute Force Protection**: Complex passwords are harder to crack
2. **Dictionary Attack Prevention**: Special characters prevent common word attacks
3. **Account Security**: Strong passwords protect user accounts
4. **Compliance**: Meets industry standards for password security
5. **User Education**: Encourages good password practices

## Testing

### Valid Password Test Cases
```bash
# Test registration with valid password
curl -X POST http://localhost:5000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "first_name": "Test",
    "last_name": "User",
    "email": "<EMAIL>",
    "password": "TestPass123!",
    "phone_number": "+**********",
    "role": "Buyer"
  }'
```

### Invalid Password Test Cases
```bash
# Test registration with weak password
curl -X POST http://localhost:5000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "first_name": "Test",
    "last_name": "User",
    "email": "<EMAIL>",
    "password": "weak",
    "phone_number": "+**********",
    "role": "Buyer"
  }'
```

## Migration Notes

### Existing Users
- Existing users with weak passwords can still log in
- They will be prompted to update their password on next password change
- No forced password reset required

### Database Considerations
- No database schema changes required
- Password validation is enforced at the application level
- Existing password hashes remain valid

## Best Practices

1. **Client-Side Validation**: Implement matching validation on frontend
2. **Password Strength Indicator**: Show users password strength in real-time
3. **Clear Error Messages**: Provide specific guidance on password requirements
4. **Security Education**: Inform users about password security best practices
5. **Regular Updates**: Encourage users to update passwords periodically
