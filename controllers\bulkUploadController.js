const CSVProcessor = require('../utils/csvProcessor');
const CategoryModel = require('../models/categoryModel');
const SubCategoryModel = require('../models/subCategoryModel');
const SubCategoryFormFieldModel = require('../models/subCategoryFormFieldModel');
const sendResponse = require('../utils/sendResponse');
const { validationResult } = require('express-validator');

const BulkUploadController = {
  /**
   * Download template file for bulk upload
   */
  async downloadTemplate(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendResponse(res, false, 'Validation failed', null, errors.array(), null, 400);
    }

    try {
      const { format } = req.params;

      if (format === 'csv') {
        const csvContent = CSVProcessor.generateCSVTemplate();
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename="subcategories-bulk-upload-template.csv"');
        return res.send(csvContent);
      } else if (format === 'excel') {
        const excelBuffer = CSVProcessor.generateExcelTemplate();
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', 'attachment; filename="subcategories-bulk-upload-template.xlsx"');
        return res.send(excelBuffer);
      }

      return sendResponse(res, false, 'Invalid format specified', null, null, null, 400);
    } catch (error) {
      console.error('Error generating template:', error);
      return sendResponse(res, false, 'Failed to generate template', null, error);
    }
  },

  /**
   * Preview bulk upload data without saving
   */
  async previewUpload(req, res) {
    try {
      if (!req.file) {
        return sendResponse(res, false, 'No file uploaded', null, null, null, 400);
      }

      // Get category_id from request body (optional for preview)
      const { category_id } = req.body;
      let category = null;

      // If category_id is provided, validate it exists
      if (category_id) {
        category = await CategoryModel.findCategoryById(category_id, {});
        if (!category) {
          return sendResponse(res, false, `Category with ID '${category_id}' not found`, null, null, null, 400);
        }
      }

      const file = req.file;
      const fileExtension = file.originalname.split('.').pop().toLowerCase();

      let parsedData;
      if (fileExtension === 'csv') {
        parsedData = await CSVProcessor.parseCSV(file.buffer);
      } else if (fileExtension === 'xlsx' || fileExtension === 'xls') {
        parsedData = CSVProcessor.parseExcel(file.buffer);
      } else {
        return sendResponse(res, false, 'Invalid file format. Only CSV and Excel files are supported.', null, null, null, 400);
      }

      if (!parsedData || parsedData.length === 0) {
        return sendResponse(res, false, 'File is empty or contains no valid data', null, null, null, 400);
      }

      // Validate the data (without category_title requirement)
      const validation = CSVProcessor.validateSubcategoryDataWithoutCategory(parsedData);

      // Process the data to count subcategories and form fields
      let subcategoriesCount = 0;
      let formFieldsCount = 0;

      if (validation.isValid && validation.validData.length > 0) {
        // Process the data to get counts
        const processedData = CSVProcessor.processSubcategoryDataWithoutCategory(
          validation.validData,
          category ? category.title : 'Preview Category'
        );

        subcategoriesCount = processedData.length;
        formFieldsCount = processedData.reduce((total, row) => {
          return total + (row.form_fields ? row.form_fields.length : 0);
        }, 0);
      }

      const response = {
        file_info: {
          filename: file.originalname,
          size: file.size,
          type: fileExtension
        },
        validation_summary: {
          total_rows: validation.totalRows,
          valid_rows: validation.validRows,
          invalid_rows: validation.totalRows - validation.validRows,
          is_valid: validation.isValid
        },
        creation_summary: {
          subcategories_to_create: subcategoriesCount,
          form_fields_to_create: formFieldsCount,
          category_info: category ? {
            id: category.id,
            title: category.title
          } : null
        },
        errors: validation.errors,
        preview_data: validation.validData.slice(0, 5) // Show first 5 valid rows as preview
      };

      return sendResponse(res, true, 'File preview generated successfully', response);
    } catch (error) {
      console.error('Error previewing upload:', error);
      return sendResponse(res, false, 'Failed to preview upload', null, {
        message: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      }, null, 500);
    }
  },

  /**
   * Process and save bulk upload data
   */
  async processUpload(req, res) {
    try {
      if (!req.file) {
        return sendResponse(res, false, 'No file uploaded', null, null, null, 400);
      }

      // Get category_id from request body
      const { category_id } = req.body;
      if (!category_id) {
        return sendResponse(res, false, 'Category ID is required', null, null, null, 400);
      }

      // Validate that category exists
      const category = await CategoryModel.findCategoryById(category_id, {});
      if (!category) {
        return sendResponse(res, false, `Category with ID '${category_id}' not found`, null, null, null, 400);
      }

      const file = req.file;
      const fileExtension = file.originalname.split('.').pop().toLowerCase();

      let parsedData;
      if (fileExtension === 'csv') {
        parsedData = await CSVProcessor.parseCSV(file.buffer);
      } else if (fileExtension === 'xlsx' || fileExtension === 'xls') {
        parsedData = CSVProcessor.parseExcel(file.buffer);
      } else {
        return sendResponse(res, false, 'Invalid file format. Only CSV and Excel files are supported.', null, null, null, 400);
      }

      if (!parsedData || parsedData.length === 0) {
        return sendResponse(res, false, 'File is empty or contains no valid data', null, null, null, 400);
      }

      // Validate the data (without category_title requirement)
      const validation = CSVProcessor.validateSubcategoryDataWithoutCategory(parsedData);
      if (!validation.isValid) {
        return sendResponse(res, false, 'Validation failed', {
          validation_summary: {
            total_rows: validation.totalRows,
            valid_rows: validation.validRows,
            invalid_rows: validation.totalRows - validation.validRows
          },
          errors: validation.errors
        }, null, null, 400);
      }

      // Process the data (without category_title)
      const processedData = CSVProcessor.processSubcategoryDataWithoutCategory(validation.validData, category.title);

      const results = {
        created_subcategories: [],
        created_form_fields: [],
        errors: [],
        summary: {
          total_processed: 0,
          subcategories_created: 0,
          form_fields_created: 0,
          errors_count: 0
        }
      };

      // Process each row
      for (const row of processedData) {
        try {
          results.summary.total_processed++;

          // Create subcategory (category is already validated)
          const subcategoryData = {
            ...row.subcategory,
            category_id: category.id
          };

          const createdSubcategory = await SubCategoryModel.createSubCategory(subcategoryData);
          results.created_subcategories.push(createdSubcategory);
          results.summary.subcategories_created++;

          // Create form fields if any
          if (row.form_fields && row.form_fields.length > 0) {
            for (const formField of row.form_fields) {
              const formFieldData = {
                ...formField,
                subcategory_id: createdSubcategory.id
              };

              const createdFormField = await SubCategoryFormFieldModel.createFormField(formFieldData);
              results.created_form_fields.push(createdFormField);
              results.summary.form_fields_created++;
            }
          }

        } catch (error) {
          console.error(`Error processing row for subcategory '${row.subcategory.title}':`, error);
          results.errors.push(`Failed to create subcategory '${row.subcategory.title}': ${error.message}`);
          results.summary.errors_count++;
        }
      }

      const isSuccess = results.summary.subcategories_created > 0;
      const message = isSuccess
        ? `Bulk upload completed. Created ${results.summary.subcategories_created} subcategories and ${results.summary.form_fields_created} form fields.`
        : 'Bulk upload failed. No subcategories were created.';

      return sendResponse(res, isSuccess, message, results, null, null, isSuccess ? 201 : 400);

    } catch (error) {
      console.error('Error processing upload:', error);
      return sendResponse(res, false, 'Failed to process upload', null, {
        message: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      }, null, 500);
    }
  }
};

module.exports = BulkUploadController;
