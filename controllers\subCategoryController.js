const SubcategoryService = require('../services/subCategoryService');
const SubCategoryFormFieldService = require('../services/subCategoryFormFieldService');
const { validationResult } = require('express-validator');
const { saveBufferToFile } = require('../utils/fileUpload');
const sendResponse = require('../utils/sendResponse');
const { formatValidationErrors } = require('../utils/validationFormatter');

class SubCategoryController {

  static parseSubCategoryFormData(data) {
    const parsed = {
      ...data,
      is_featured: data.is_featured === '1',
      translations: data.translations ? JSON.parse(data.translations) : [],
      form_fields: data.form_fields ? JSON.parse(data.form_fields) : [],
    };

    // Only include sort_order if it's provided and valid
    if (data.sort_order && data.sort_order !== '') {
      parsed.sort_order = parseInt(data.sort_order);
    }

    return parsed;
  }

  static async createSubcategory(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      const formattedErrors = {};

      errors.array().forEach(error => {
        if (!formattedErrors[error.path]) {
          formattedErrors[error.path] = [];
        }
        formattedErrors[error.path] = error.msg;
      });

      return sendResponse(res, false, "Validation failed", null, formattedErrors, null, 422);
    }

    try {
      const parsedData = SubCategoryController.parseSubCategoryFormData(req.body);
      parsedData.category_id = req.params.id; // Set the category_id from route param

      const files = req.files;

      if (files?.image?.[0]) {
        parsedData.image = saveBufferToFile(files.image[0].buffer, files.image[0].originalname, 'uploads/subcategories');
      }

      if (files?.thumbnail?.[0]) {
        parsedData.thumbnail = saveBufferToFile(files.thumbnail[0].buffer, files.thumbnail[0].originalname, 'uploads/subcategories');
      }

      // Create the subcategory
      const subcategory = await SubcategoryService.create(parsedData);

      // Create form fields if provided
      if (parsedData.form_fields && parsedData.form_fields.length > 0) {
        await SubCategoryFormFieldService.createManyFormFields(parsedData.form_fields, subcategory.id);
      }

      // Get the subcategory with form fields
      const subcategoryWithFormFields = await SubcategoryService.get(subcategory.id);
      const formFields = await SubCategoryFormFieldService.getFormFieldsBySubcategoryId(subcategory.id);

      // Combine the data
      const responseData = {
        ...subcategoryWithFormFields,
        form_fields: formFields
      };

      return sendResponse(res, true, 'Subcategory created successfully', responseData, null, null, 201);
    } catch (error) {
      return sendResponse(res, false, error.message || 'Failed to create subcategory', null, error, null, 500);
    }
  }

  static async getSubcategory(req, res) {
    try {
      const subcategory = await SubcategoryService.get(req.params.id);
      if (!subcategory) {
        return sendResponse(res, false, 'Subcategory not found', null, null, null, 404);
      }

      // Get form fields for the subcategory
      const formFields = await SubCategoryFormFieldService.getFormFieldsBySubcategoryId(req.params.id);

      // Combine the data
      const responseData = {
        ...subcategory,
        form_fields: formFields
      };

      return sendResponse(res, true, 'Subcategory retrieved successfully', responseData);
    } catch (error) {
      return sendResponse(res, false, error.message || 'Failed to fetch subcategory', null, error, null, 500);
    }
  }

  static async updateSubcategory(req, res) {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      const formattedErrors = {};

      errors.array().forEach(error => {
        if (!formattedErrors[error.path]) {
          formattedErrors[error.path] = [];
        }
        formattedErrors[error.path] = error.msg;
      });

      return sendResponse(res, false, "Validation failed", null, formattedErrors, null, 422);
    }

    try {
      const parsedData = SubCategoryController.parseSubCategoryFormData(req.body);
      const files = req.files;

      if (files?.image?.[0]) {
        parsedData.image = saveBufferToFile(files.image[0].buffer, files.image[0].originalname, 'uploads/subcategories');
      }

      if (files?.thumbnail?.[0]) {
        parsedData.thumbnail = saveBufferToFile(files.thumbnail[0].buffer, files.thumbnail[0].originalname, 'uploads/subcategories');
      }

      // Check if subcategory exists
      const existingSubcategory = await SubcategoryService.get(req.params.id);
      if (!existingSubcategory) {
        return sendResponse(res, false, 'Subcategory not found', null, null, null, 404);
      }

      // Update the subcategory
      const subcategory = await SubcategoryService.update(req.params.id, parsedData);

      // Handle form fields if provided
      if (parsedData.form_fields) {
        // Delete existing form fields
        await SubCategoryFormFieldService.deleteFormFieldsBySubcategoryId(req.params.id);

        // Create new form fields if any
        if (parsedData.form_fields.length > 0) {
          await SubCategoryFormFieldService.createManyFormFields(parsedData.form_fields, req.params.id);
        }
      }

      // Get the updated subcategory with form fields
      const updatedSubcategory = await SubcategoryService.get(req.params.id);
      const formFields = await SubCategoryFormFieldService.getFormFieldsBySubcategoryId(req.params.id);

      // Combine the data
      const responseData = {
        ...updatedSubcategory,
        form_fields: formFields
      };

      return sendResponse(res, true, 'Subcategory updated successfully', responseData);
    } catch (error) {
      return sendResponse(res, false, error.message || 'Failed to update subcategory', null, error, null, 500);
    }
  }

  static async deleteSubcategory(req, res) {
    try {
      // Check if subcategory exists
      const subcategory = await SubcategoryService.get(req.params.id);
      if (!subcategory) {
        return sendResponse(res, false, 'Subcategory not found', null, null, null, 404);
      }

      // Delete form fields first (this should happen automatically due to onDelete: Cascade, but let's be explicit)
      await SubCategoryFormFieldService.deleteFormFieldsBySubcategoryId(req.params.id);

      // Delete the subcategory
      await SubcategoryService.delete(req.params.id);

      return sendResponse(res, true, 'Subcategory and associated form fields deleted successfully');
    } catch (error) {
      return sendResponse(res, false, error.message || 'Failed to delete subcategory', null, error, null, 500);
    }
  }

  /**
   * Get form fields for a subcategory
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getSubcategoryFormFields(req, res) {
    try {
      // Check if subcategory exists
      const subcategory = await SubcategoryService.get(req.params.id);
      if (!subcategory) {
        return sendResponse(res, false, 'Subcategory not found', null, null, null, 404);
      }

      // Get form fields
      const formFields = await SubCategoryFormFieldService.getFormFieldsBySubcategoryId(req.params.id);

      return sendResponse(res, true, 'Form fields retrieved successfully', formFields);
    } catch (error) {
      return sendResponse(res, false, error.message || 'Failed to retrieve form fields', null, error, null, 500);
    }
  }
}

module.exports = SubCategoryController;