MERGE REQUEST API DOCUMENTATION
===============================

Base URL: /api/admin/requests
Authentication: <PERSON><PERSON> (Admin role required)

===============================

1. POST /requests/merge
   Description: Merge multiple requests into one main request (Admin only)
   
   Endpoint: POST /api/admin/requests/merge
   
   Headers:
   - Authorization: Bearer <admin_token>
   - Content-Type: application/json
   
   Payload:
   {
     "main_request_id": "uuid",           // Required: UUID of the main request
     "request_ids_to_merge": [            // Required: Array of request UUIDs to merge
       "uuid1",
       "uuid2",
       "uuid3"
     ]
   }
   
   Validation Rules:
   - main_request_id: Required, must be valid UUID
   - request_ids_to_merge: Required, must be non-empty array of valid UUIDs
   - main_request_id cannot be included in request_ids_to_merge
   - No duplicate IDs allowed in request_ids_to_merge
   - All requests must exist and not be already merged
   
   Response (Success):
   {
     "success": true,
     "message": "Successfully merged 3 request(s) into main request",
     "data": {
       "id": "main-request-uuid",
       "request_code": "REQ-*************",
       "title": "Main Request Title",
       "short_description": "Main request description",
       "description": "Detailed description of main request",
       "quantity": 1,
       "budget_min": 100.00,
       "budget_max": 500.00,
       "deadline": "2024-02-01T00:00:00.000Z",
       "urgency": "High",
       "status": "Merged",
       "buyer": {
         "id": "buyer-uuid",
         "first_name": "John",
         "last_name": "Doe",
         "email": "<EMAIL>",
         "profile_picture_url": "/uploads/profiles/image.jpg"
       },
       "category": {
         "id": "category-uuid",
         "name": "Technology",
         "description": "Technology services"
       },
       "sub_category": {
         "id": "subcategory-uuid",
         "name": "Web Development",
         "description": "Web development services"
       },
       "merged_requests": [
         {
           "id": "merge-record-uuid",
           "request_id": "main-request-uuid",
           "merged_item_id": "child-request-uuid-1",
           "merged_by": "admin-uuid",
           "created_at": "2024-01-15T10:30:00.000Z",
           "merged_item": {
             "id": "child-request-uuid-1",
             "request_code": "REQ-*************",
             "title": "Child Request 1",
             "short_description": "First merged request",
             "description": "Details of first merged request",
             "quantity": 2,
             "budget_min": 50.00,
             "budget_max": 200.00,
             "deadline": "2024-01-25T00:00:00.000Z",
             "urgency": "Normal",
             "status": "Merged",
             "created_at": "2024-01-10T08:00:00.000Z",
             "buyer": {
               "id": "buyer2-uuid",
               "first_name": "Jane",
               "last_name": "Smith",
               "email": "<EMAIL>",
               "profile_picture_url": "/uploads/profiles/image2.jpg"
             },
             "category": {
               "id": "category-uuid",
               "name": "Technology",
               "description": "Technology services"
             },
             "sub_category": {
               "id": "subcategory-uuid",
               "name": "Web Development",
               "description": "Web development services"
             }
           },
           "merged_by_user": {
             "id": "admin-uuid",
             "first_name": "Admin",
             "last_name": "User",
             "email": "<EMAIL>"
           }
         },
         {
           "id": "merge-record-uuid-2",
           "request_id": "main-request-uuid",
           "merged_item_id": "child-request-uuid-2",
           "merged_by": "admin-uuid",
           "created_at": "2024-01-15T10:30:00.000Z",
           "merged_item": {
             "id": "child-request-uuid-2",
             "request_code": "REQ-*************",
             "title": "Child Request 2",
             "short_description": "Second merged request",
             "description": "Details of second merged request",
             "quantity": 1,
             "budget_min": 75.00,
             "budget_max": 300.00,
             "deadline": "2024-01-30T00:00:00.000Z",
             "urgency": "Low",
             "status": "Merged",
             "created_at": "2024-01-12T14:30:00.000Z",
             "buyer": {
               "id": "buyer3-uuid",
               "first_name": "Bob",
               "last_name": "Johnson",
               "email": "<EMAIL>",
               "profile_picture_url": "/uploads/profiles/image3.jpg"
             },
             "category": {
               "id": "category-uuid",
               "name": "Technology",
               "description": "Technology services"
             },
             "sub_category": {
               "id": "subcategory-uuid",
               "name": "Web Development",
               "description": "Web development services"
             }
           },
           "merged_by_user": {
             "id": "admin-uuid",
             "first_name": "Admin",
             "last_name": "User",
             "email": "<EMAIL>"
           }
         }
       ],
       "created_at": "2024-01-08T12:00:00.000Z",
       "updated_at": "2024-01-15T10:30:00.000Z"
     }
   }

===============================

2. GET /requests/:id (Updated to include merged requests)
   Description: Get request details including merged child requests
   
   Endpoint: GET /api/admin/requests/{request_id}
   
   Headers:
   - Authorization: Bearer <admin_token>
   
   URL Parameters:
   - request_id: UUID (required)
   
   Payload: None
   
   Response (Success - Main Request with Merged Children):
   {
     "success": true,
     "message": "Request retrieved successfully",
     "data": {
       "id": "main-request-uuid",
       "request_code": "REQ-*************",
       "title": "Main Request Title",
       "status": "Merged",
       // ... other request fields ...
       "merged_requests": [
         // Array of merged child requests as shown above
       ],
       // ... other request relations (buyer, category, etc.) ...
     }
   }
   
   Response (Success - Regular Request):
   {
     "success": true,
     "message": "Request retrieved successfully",
     "data": {
       "id": "request-uuid",
       "request_code": "REQ-*************",
       "title": "Regular Request",
       "status": "Pending",
       // ... other request fields ...
       "merged_requests": [],  // Empty array for non-merged requests
       // ... other request relations ...
     }
   }

===============================

ERROR RESPONSES:

Validation Error (400):
{
  "success": false,
  "message": "Validation failed",
  "error": {
    "main_request_id": ["Main request ID is required"],
    "request_ids_to_merge": ["Request IDs to merge must be a non-empty array"]
  }
}

Request Not Found (404):
{
  "success": false,
  "message": "Main request not found",
  "error": {
    "general": ["Main request not found"]
  }
}

Business Logic Error (400):
{
  "success": false,
  "message": "Request uuid1 is already merged",
  "error": {
    "general": ["Request uuid1 is already merged"]
  }
}

Unauthorized (401):
{
  "message": "Access Denied"
}

Forbidden (403):
{
  "message": "Access denied. Insufficient permissions."
}

Server Error (500):
{
  "success": false,
  "message": "Failed to merge requests: Database connection error",
  "error": {
    "general": ["Failed to merge requests: Database connection error"]
  }
}

===============================

BUSINESS RULES:

1. Only admins can merge requests
2. Main request must exist and not be deleted
3. All child requests must exist and not be deleted
4. Child requests cannot already be merged
5. Main request cannot be included in the list of requests to merge
6. No duplicate request IDs allowed
7. When requests are merged:
   - Child requests status changes to "Merged"
   - Main request status changes to "Merged"
   - Merge records are created in request_merged_items table
   - Status update records are created for tracking
8. Merged requests appear in the merged_requests array when fetching request details
