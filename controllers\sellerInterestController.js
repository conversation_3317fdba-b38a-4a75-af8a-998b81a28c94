const { validationResult } = require('express-validator');
const SellerInterestService = require('../services/sellerInterestService');
const sendResponse = require('../utils/sendResponse');
const { formatValidationErrors } = require('../utils/validationFormatter');

class SellerInterestController {
  /**
   * Get seller interested categories
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getInterestedCategories(req, res) {
    try {
      const sellerId = req.user.id;
      const categories = await SellerInterestService.getInterestedCategories(sellerId);

      return sendResponse(
        res,
        true,
        'Seller interested categories retrieved successfully',
        categories,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Get seller interested subcategories
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getInterestedSubcategories(req, res) {
    try {
      const sellerId = req.user.id;
      const subcategories = await SellerInterestService.getInterestedSubcategories(sellerId);

      return sendResponse(
        res,
        true,
        'Seller interested subcategories retrieved successfully',
        subcategories,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Add seller interested categories
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async addInterestedCategory(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const laravelStyleErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          laravelStyleErrors.message,
          null,
          laravelStyleErrors.errors,
          null,
          422
        );
      }

      const sellerId = req.user.id;
      const { categoryIds } = req.body;

      // Use the updateInterestedCategories method which already handles arrays
      const result = await SellerInterestService.updateInterestedCategories(sellerId, categoryIds);

      return sendResponse(
        res,
        true,
        'Category interests added successfully',
        result,
        null,
        null,
        201
      );
    } catch (error) {
      let statusCode = error.statusCode || 500;

      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        statusCode
      );
    }
  }

  /**
   * Add seller interested subcategories
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async addInterestedSubcategory(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const laravelStyleErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          laravelStyleErrors.message,
          null,
          laravelStyleErrors.errors,
          null,
          422
        );
      }

      const sellerId = req.user.id;
      const { subcategoryIds } = req.body;

      // Use the updateInterestedSubcategories method which already handles arrays
      const result = await SellerInterestService.updateInterestedSubcategories(sellerId, subcategoryIds);

      return sendResponse(
        res,
        true,
        'Subcategory interests added successfully',
        result,
        null,
        null,
        201
      );
    } catch (error) {
      let statusCode = error.statusCode || 500;

      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        statusCode
      );
    }
  }

  /**
   * Remove seller interested category
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async removeInterestedCategory(req, res) {
    try {
      const sellerId = req.user.id;
      const { categoryId } = req.params;

      await SellerInterestService.removeInterestedCategory(sellerId, categoryId);

      return sendResponse(
        res,
        true,
        'Category interest removed successfully',
        null,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Remove seller interested subcategory
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async removeInterestedSubcategory(req, res) {
    try {
      const sellerId = req.user.id;
      const { subcategoryId } = req.params;

      await SellerInterestService.removeInterestedSubcategory(sellerId, subcategoryId);

      return sendResponse(
        res,
        true,
        'Subcategory interest removed successfully',
        null,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Update seller interested categories
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async updateInterestedCategories(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const laravelStyleErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          laravelStyleErrors.message,
          null,
          laravelStyleErrors.errors,
          null,
          422
        );
      }

      const sellerId = req.user.id;
      const { categoryIds } = req.body;

      const result = await SellerInterestService.updateInterestedCategories(sellerId, categoryIds);

      return sendResponse(
        res,
        true,
        'Interested categories updated successfully',
        result,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Update seller interested subcategories
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async updateInterestedSubcategories(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const laravelStyleErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          laravelStyleErrors.message,
          null,
          laravelStyleErrors.errors,
          null,
          422
        );
      }

      const sellerId = req.user.id;
      const { subcategoryIds } = req.body;

      const result = await SellerInterestService.updateInterestedSubcategories(sellerId, subcategoryIds);

      return sendResponse(
        res,
        true,
        'Interested subcategories updated successfully',
        result,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Get subcategories for categories that the seller is interested in
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getSubcategoriesForInterestedCategories(req, res) {
    try {
      const sellerId = req.user.id;
      const subcategories = await SellerInterestService.getSubcategoriesForInterestedCategories(sellerId);

      return sendResponse(
        res,
        true,
        'Subcategories for interested categories retrieved successfully',
        subcategories,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }
}

module.exports = SellerInterestController;
