# Bulk Upload API Documentation

This API allows administrators to upload CSV or Excel files to create multiple subcategories and their associated dynamic form fields in bulk.

## Overview

The bulk upload feature supports:
- Creating multiple subcategories at once
- Adding dynamic form fields to each subcategory
- Validation of data before processing
- Preview functionality to check data before saving
- Support for both CSV and Excel file formats

## API Endpoints

### 1. Download Template

Download a template file for bulk upload.

**Endpoint:** `GET /api/admin/bulk-upload/template/:format`

**Parameters:**
- `format` (path parameter): Either `csv` or `excel`

**Headers:**
- `Authorization: Bearer <admin_token>`

**Response:**
- Returns the template file for download

**Example:**
```bash
curl -X GET "http://localhost:3000/api/admin/bulk-upload/template/csv" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  --output template.csv
```

### 2. Preview Upload

Preview the data from uploaded file without saving to database.

**Endpoint:** `POST /api/admin/bulk-upload/preview`

**Headers:**
- `Authorization: Bearer <admin_token>`
- `Content-Type: multipart/form-data`

**Body:**
- `file`: CSV or Excel file

**Response:**
```json
{
  "success": true,
  "message": "File preview generated successfully",
  "data": {
    "file_info": {
      "filename": "subcategories.csv",
      "size": 2048,
      "type": "csv"
    },
    "validation_summary": {
      "total_rows": 10,
      "valid_rows": 8,
      "invalid_rows": 2,
      "is_valid": false
    },
    "errors": [
      "Row 3: category_title is required",
      "Row 7: invalid input_type 'INVALID_TYPE'"
    ],
    "preview_data": [
      {
        "category_title": "Technology",
        "subcategory_title": "Web Development",
        "subcategory_description": "Professional web development services",
        "subcategory_color": "#3498db",
        "is_featured": "true",
        "seo_title": "Web Development Services",
        "seo_description": "Professional web development services for your business",
        "seo_keywords": "web development, website, programming",
        "form_fields": "[{\"label_name\":\"Project Type\",\"input_type\":\"SELECT\",\"options\":[\"E-commerce\",\"Blog\"]}]"
      }
    ]
  }
}
```

### 3. Process Upload

Process and save the uploaded data to database.

**Endpoint:** `POST /api/admin/bulk-upload/subcategories`

**Headers:**
- `Authorization: Bearer <admin_token>`
- `Content-Type: multipart/form-data`

**Body:**
- `file`: CSV or Excel file

**Response:**
```json
{
  "success": true,
  "message": "Bulk upload completed. Created 5 subcategories and 12 form fields.",
  "data": {
    "created_subcategories": [
      {
        "id": "uuid-1",
        "title": "Web Development",
        "category_id": "category-uuid",
        "description": "Professional web development services",
        "color": "#3498db",
        "is_featured": true,
        "created_at": "2024-01-01T00:00:00.000Z"
      }
    ],
    "created_form_fields": [
      {
        "id": "field-uuid-1",
        "subcategory_id": "uuid-1",
        "label_name": "Project Type",
        "input_type": "SELECT",
        "options": ["E-commerce", "Blog", "Corporate"],
        "is_required": true,
        "sort_order": 1
      }
    ],
    "errors": [],
    "summary": {
      "total_processed": 5,
      "subcategories_created": 5,
      "form_fields_created": 12,
      "errors_count": 0
    }
  }
}
```

## File Format

### CSV/Excel Columns

| Column | Required | Description | Example |
|--------|----------|-------------|---------|
| `category_title` | Yes | Name of the parent category | "Technology" |
| `subcategory_title` | Yes | Name of the subcategory | "Web Development" |
| `subcategory_description` | No | Description of the subcategory | "Professional web development services" |
| `subcategory_color` | No | Hex color code | "#3498db" |
| `is_featured` | No | Whether subcategory is featured | "true" or "false" |
| `seo_title` | No | SEO title | "Web Development Services" |
| `seo_description` | No | SEO description | "Professional web development services" |
| `seo_keywords` | No | SEO keywords | "web development, website, programming" |
| `form_fields` | No | JSON array of form fields | See Form Fields section |

### Form Fields JSON Structure

The `form_fields` column should contain a JSON array of form field objects:

```json
[
  {
    "label_name": "Project Type",
    "input_type": "SELECT",
    "label_subtitle": "Select the type of website you need",
    "placeholder": "",
    "is_required": true,
    "options": ["E-commerce", "Blog", "Corporate", "Portfolio"],
    "sort_order": 1
  },
  {
    "label_name": "Budget Range",
    "input_type": "SELECT",
    "label_subtitle": "Select your budget range",
    "is_required": true,
    "options": ["$500-$1000", "$1000-$5000", "$5000-$10000", "$10000+"],
    "sort_order": 2
  },
  {
    "label_name": "Project Description",
    "input_type": "TEXTAREA",
    "label_subtitle": "Describe your project requirements",
    "placeholder": "Enter detailed project description...",
    "is_required": true,
    "min_length": 50,
    "max_length": 1000,
    "sort_order": 3
  }
]
```

### Form Field Properties

| Property | Required | Type | Description |
|----------|----------|------|-------------|
| `label_name` | Yes | String | Display name of the field |
| `input_type` | Yes | String | Type of input (see Input Types) |
| `label_subtitle` | No | String | Subtitle/help text |
| `placeholder` | No | String | Placeholder text |
| `is_required` | No | Boolean | Whether field is required |
| `options` | Conditional | Array | Required for SELECT, RADIO, CHECKBOX, MULTISELECT |
| `default_value` | No | String | Default value |
| `validation_regex` | No | String | Regex pattern for validation |
| `min_value` | No | Number | Minimum value (for NUMBER, RANGE) |
| `max_value` | No | Number | Maximum value (for NUMBER, RANGE) |
| `min_length` | No | Number | Minimum length (for TEXT, TEXTAREA) |
| `max_length` | No | Number | Maximum length (for TEXT, TEXTAREA) |
| `sort_order` | No | Number | Display order (auto-assigned if not provided) |

### Supported Input Types

- `TEXT` - Single line text input
- `TEXTAREA` - Multi-line text input
- `NUMBER` - Numeric input
- `EMAIL` - Email input
- `PASSWORD` - Password input
- `DATE` - Date picker
- `TIME` - Time picker
- `DATETIME` - Date and time picker
- `CHECKBOX` - Multiple selection checkboxes
- `RADIO` - Single selection radio buttons
- `SELECT` - Dropdown select
- `MULTISELECT` - Multiple selection dropdown
- `FILE` - File upload
- `PHONE` - Phone number input
- `URL` - URL input
- `COLOR` - Color picker
- `RANGE` - Range slider

## Validation Rules

1. **Category must exist**: The `category_title` must match an existing category in the database
2. **Unique subcategory titles**: Subcategory titles must be unique within the same category
3. **Required fields**: `category_title` and `subcategory_title` are mandatory
4. **Valid input types**: Form field `input_type` must be one of the supported types
5. **Options for select types**: SELECT, RADIO, CHECKBOX, MULTISELECT types must have `options` array
6. **Valid JSON**: `form_fields` column must contain valid JSON
7. **File format**: Only CSV and Excel (.xlsx, .xls) files are supported

## Error Handling

The API provides detailed error messages for validation failures:

- Row-level errors specify the exact row number and issue
- Field-level errors for form fields include field index
- Summary includes total rows processed and error counts
- Preview mode shows errors without saving data

## Demo Files

Demo template files are available in the `demo-files` directory:
- `subcategories-bulk-upload-template.csv`
- `subcategories-bulk-upload-template.xlsx`

These files contain sample data showing the correct format and structure.

## Usage Examples

### 1. Download Template
```bash
curl -X GET "http://localhost:3000/api/admin/bulk-upload/template/csv" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  --output template.csv
```

### 2. Preview Upload
```bash
curl -X POST "http://localhost:3000/api/admin/bulk-upload/preview" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -F "file=@subcategories.csv"
```

### 3. Process Upload
```bash
curl -X POST "http://localhost:3000/api/admin/bulk-upload/subcategories" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -F "file=@subcategories.csv"
```

## Best Practices

1. **Always preview first**: Use the preview endpoint to validate data before processing
2. **Check existing categories**: Ensure all referenced categories exist in the database
3. **Validate JSON**: Test form_fields JSON structure before upload
4. **Use templates**: Start with the provided template files
5. **Batch processing**: For large files, consider splitting into smaller batches
6. **Backup data**: Always backup your database before bulk operations
