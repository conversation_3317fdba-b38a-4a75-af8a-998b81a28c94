const userService = require('../services/userService');
const sendResponse = require('../utils/sendResponse');

// Get all users
exports.getUsers = async (req, res) => {
    try {
        const users = await userService.getAllUsers();
        sendResponse(res, true, 'Users retrieved successfully', users);
    } catch (error) {
        sendResponse(res, false, 'Failed to retrieve users', null, error, null, 500);
    }
};

// Get a single user by ID
exports.getUserById = async (req, res) => {
    try {
        const user = await userService.getUserById(req.params.id);
        if (!user) {
            return sendResponse(res, false, 'User not found', null, null, null, 404);
        }
        sendResponse(res, true, 'User retrieved successfully', user);
    } catch (error) {
        sendResponse(res, false, error.message, null, error, null, 500);
    }
};

// Create a new user
exports.createUser = async (req, res) => {
    try {
        const user = await userService.createUser(req.body);
        sendResponse(res, true, 'User created successfully', user, null, null, 201);
    } catch (error) {
        sendResponse(res, false, error.message, null, error, null, 400);
    }
};

// Update a user by ID
exports.updateUser = async (req, res) => {
    try {
        const user = await userService.updateUser(req.params.id, req.body);
        if (!user) {
            return sendResponse(res, false, 'User not found', null, null, null, 404);
        }
        sendResponse(res, true, 'User updated successfully', user);
    } catch (error) {
        sendResponse(res, false, error.message, null, error, null, 400);
    }
};

// Partially update a user by ID
exports.partialUpdateUser = async (req, res) => {
    try {
        const user = await userService.partialUpdateUser(req.params.id, req.body);
        if (!user) {
            return sendResponse(res, false, 'User not found', null, null, null, 404);
        }
        sendResponse(res, true, 'User partially updated successfully', user);
    } catch (error) {
        sendResponse(res, false, error.message, null, error, null, 400);
    }
};

// Delete a user by ID
exports.deleteUser = async (req, res) => {
    try {
        const user = await userService.deleteUser(req.params.id);
        if (!user) {
            return sendResponse(res, false, 'User not found', null, null, null, 404);
        }
        sendResponse(res, true, 'User deleted successfully');
    } catch (error) {
        sendResponse(res, false, error.message, null, error, null, 500);
    }
};

// Search for users with query parameters
exports.searchUsers = async (req, res) => {
    try {
        const users = await userService.searchUsers(req.query);
        sendResponse(res, true, 'Users search completed', users);
    } catch (error) {
        sendResponse(res, false, error.message, null, error, null, 500);
    }
};

// Get the profile of the currently logged-in user
exports.getUserProfile = async (req, res) => {
    try {
        const user = await userService.getUserProfile(req.user.id);
        if (!user) {
            return sendResponse(res, false, 'User not found', null, null, null, 404);
        }
        sendResponse(res, true, 'User profile retrieved successfully', user);
    } catch (error) {
        sendResponse(res, false, error.message, null, error, null, 500);
    }
};

// Get the profile of the currently logged-in user with roles
exports.getUserProfileWithRoles = async (req, res) => {
    try {
        const user = await userService.getUserProfileWithRoles(req.user.id);
        if (!user) {
            return sendResponse(res, false, 'User not found', null, null, null, 404);
        }
        sendResponse(res, true, 'User profile with roles retrieved successfully', user);
    } catch (error) {
        sendResponse(res, false, error.message, null, error, null, 500);
    }
};