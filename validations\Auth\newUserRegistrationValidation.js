const { body } = require('express-validator');

const newUserRegistrationValidation = [
  // Username validation
  body('username')
    .notEmpty()
    .withMessage('Username is required')
    .isLength({ min: 3, max: 30 })
    .withMessage('Username must be between 3 and 30 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores')
    .custom(async (value) => {
      // Check if username already exists
      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient();
      
      try {
        const existingUser = await prisma.users.findUnique({
          where: { username: value }
        });
        
        if (existingUser) {
          throw new Error('Username already exists');
        }
        
        return true;
      } finally {
        await prisma.$disconnect();
      }
    }),

  // Password validation
  body('password')
    .notEmpty()
    .withMessage('Password is required')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),

  // User type validation
  body('user_type')
    .notEmpty()
    .withMessage('User type is required')
    .isIn(['business', 'personal'])
    .withMessage('User type must be either "business" or "personal"'),

  // Role validation
  body('role')
    .notEmpty()
    .withMessage('Role is required')
    .isIn(['Buyer', 'Seller'])
    .withMessage('Role must be either "Buyer" or "Seller"')
    .custom(async (value) => {
      // Check if role exists in database
      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient();

      try {
        const existingRole = await prisma.roles.findUnique({
          where: { name: value }
        });

        if (!existingRole) {
          throw new Error(`Role "${value}" does not exist`);
        }

        return true;
      } finally {
        await prisma.$disconnect();
      }
    }),

  // Age confirmation validation
  body('age_confirm')
    .notEmpty()
    .withMessage('Age confirmation is required')
    .isBoolean()
    .withMessage('Age confirmation must be a boolean')
    .custom((value) => {
      if (value !== true) {
        throw new Error('You must be 18 years or older to register');
      }
      return true;
    }),

  // Optional email validation (if provided)
  body('email')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail()
    .custom(async (value) => {
      if (value) {
        // Check if email already exists
        const { PrismaClient } = require('@prisma/client');
        const prisma = new PrismaClient();
        
        try {
          const existingUser = await prisma.users.findUnique({
            where: { email: value }
          });
          
          if (existingUser) {
            throw new Error('Email already exists');
          }
          
          return true;
        } finally {
          await prisma.$disconnect();
        }
      }
      return true;
    }),

  // Optional phone validation (if provided)
  body('phone_number')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number')
    .custom(async (value) => {
      if (value) {
        // Check if phone already exists
        const { PrismaClient } = require('@prisma/client');
        const prisma = new PrismaClient();
        
        try {
          const existingUser = await prisma.users.findUnique({
            where: { phone_number: value }
          });
          
          if (existingUser) {
            throw new Error('Phone number already exists');
          }
          
          return true;
        } finally {
          await prisma.$disconnect();
        }
      }
      return true;
    }),


];

module.exports = { newUserRegistrationValidation };
