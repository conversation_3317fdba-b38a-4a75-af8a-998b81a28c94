const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

class SubscriptionModel {
  /**
   * Create a new subscription plan
   * @param {Object} data - Subscription data
   * @returns {Promise<Object>} Created subscription
   */
  static async createSubscription(data) {
    return await prisma.subscriptions.create({
      data: {
        name: data.name,
        description: data.description,
        price: data.price,
        duration_days: data.duration_days,
        max_requests: data.max_requests,
        max_offers: data.max_offers,
        max_orders: data.max_orders,
        is_active: data.is_active,
        is_featured: data.is_featured,
        user_type: data.user_type,
        features: data.features
      }
    });
  }

  /**
   * Get all subscription plans
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated subscriptions
   */
  static async getAllSubscriptions(filters = {}, page = 1, limit = 10) {
    const skip = (page - 1) * limit;
    const where = {};

    if (filters.user_type) {
      where.user_type = { in: [filters.user_type, 'BOTH'] };
    }
    if (filters.is_active !== undefined) {
      where.is_active = filters.is_active;
    }
    if (filters.is_featured !== undefined) {
      where.is_featured = filters.is_featured;
    }

    const [subscriptions, total] = await Promise.all([
      prisma.subscriptions.findMany({
        where,
        skip,
        take: limit,
        orderBy: { created_at: 'desc' },
        include: {
          _count: {
            select: { user_subscriptions: true }
          }
        }
      }),
      prisma.subscriptions.count({ where })
    ]);

    return {
      data: subscriptions,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Get subscription by ID
   * @param {string} id - Subscription ID
   * @returns {Promise<Object>} Subscription
   */
  static async getSubscriptionById(id) {
    return await prisma.subscriptions.findUnique({
      where: { id },
      include: {
        _count: {
          select: { user_subscriptions: true }
        }
      }
    });
  }

  /**
   * Update subscription
   * @param {string} id - Subscription ID
   * @param {Object} data - Update data
   * @returns {Promise<Object>} Updated subscription
   */
  static async updateSubscription(id, data) {
    return await prisma.subscriptions.update({
      where: { id },
      data
    });
  }

  /**
   * Delete subscription
   * @param {string} id - Subscription ID
   * @returns {Promise<Object>} Deleted subscription
   */
  static async deleteSubscription(id) {
    return await prisma.subscriptions.delete({
      where: { id }
    });
  }

  /**
   * Subscribe user to a plan
   * @param {string} userId - User ID
   * @param {string} subscriptionId - Subscription ID
   * @param {Object} paymentData - Payment information
   * @returns {Promise<Object>} User subscription
   */
  static async subscribeUser(userId, subscriptionId, paymentData = {}) {
    const subscription = await prisma.subscriptions.findUnique({
      where: { id: subscriptionId }
    });

    if (!subscription) {
      throw new Error('Subscription plan not found');
    }

    const startDate = new Date();
    const endDate = new Date(startDate.getTime() + (subscription.duration_days * 24 * 60 * 60 * 1000));

    // Cancel any existing active subscription for this user
    await prisma.user_subscriptions.updateMany({
      where: {
        user_id: userId,
        status: 'ACTIVE'
      },
      data: {
        status: 'CANCELLED'
      }
    });

    return await prisma.user_subscriptions.create({
      data: {
        user_id: userId,
        subscription_id: subscriptionId,
        start_date: startDate,
        end_date: endDate,
        payment_method: paymentData.payment_method,
        transaction_id: paymentData.transaction_id,
        auto_renew: paymentData.auto_renew || false
      },
      include: {
        subscription: true
      }
    });
  }

  /**
   * Get user's active subscription
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Active subscription
   */
  static async getUserActiveSubscription(userId) {
    return await prisma.user_subscriptions.findFirst({
      where: {
        user_id: userId,
        status: 'ACTIVE',
        end_date: {
          gte: new Date()
        }
      },
      include: {
        subscription: true,
        usage_records: true
      }
    });
  }

  /**
   * Get user's subscription history
   * @param {string} userId - User ID
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Subscription history
   */
  static async getUserSubscriptionHistory(userId, page = 1, limit = 10) {
    const skip = (page - 1) * limit;

    const [subscriptions, total] = await Promise.all([
      prisma.user_subscriptions.findMany({
        where: { user_id: userId },
        skip,
        take: limit,
        orderBy: { created_at: 'desc' },
        include: {
          subscription: true
        }
      }),
      prisma.user_subscriptions.count({
        where: { user_id: userId }
      })
    ]);

    return {
      data: subscriptions,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Cancel user subscription
   * @param {string} userSubscriptionId - User subscription ID
   * @returns {Promise<Object>} Updated subscription
   */
  static async cancelSubscription(userSubscriptionId) {
    return await prisma.user_subscriptions.update({
      where: { id: userSubscriptionId },
      data: {
        status: 'CANCELLED',
        auto_renew: false
      }
    });
  }

  /**
   * Track usage for a user
   * @param {string} userId - User ID
   * @param {string} usageType - Usage type (REQUEST, OFFER, ORDER)
   * @returns {Promise<Object>} Usage record
   */
  static async trackUsage(userId, usageType) {
    const activeSubscription = await this.getUserActiveSubscription(userId);
    
    if (!activeSubscription) {
      throw new Error('No active subscription found');
    }

    const now = new Date();
    const month = now.getMonth() + 1;
    const year = now.getFullYear();

    return await prisma.subscription_usage.upsert({
      where: {
        user_subscription_id_usage_type_month_year: {
          user_subscription_id: activeSubscription.id,
          usage_type: usageType,
          month,
          year
        }
      },
      update: {
        count: {
          increment: 1
        }
      },
      create: {
        user_subscription_id: activeSubscription.id,
        usage_type: usageType,
        count: 1,
        month,
        year
      }
    });
  }

  /**
   * Check if user can perform action based on subscription limits
   * @param {string} userId - User ID
   * @param {string} usageType - Usage type (REQUEST, OFFER, ORDER)
   * @returns {Promise<Object>} Usage check result
   */
  static async checkUsageLimit(userId, usageType) {
    const activeSubscription = await this.getUserActiveSubscription(userId);
    
    if (!activeSubscription) {
      return {
        canUse: false,
        reason: 'No active subscription found',
        limit: 0,
        used: 0,
        remaining: 0
      };
    }

    const subscription = activeSubscription.subscription;
    let limit;

    switch (usageType) {
      case 'REQUEST':
        limit = subscription.max_requests;
        break;
      case 'OFFER':
        limit = subscription.max_offers;
        break;
      case 'ORDER':
        limit = subscription.max_orders;
        break;
      default:
        throw new Error('Invalid usage type');
    }

    // If limit is null, it means unlimited
    if (limit === null) {
      return {
        canUse: true,
        reason: 'Unlimited usage',
        limit: null,
        used: 0,
        remaining: null
      };
    }

    const now = new Date();
    const month = now.getMonth() + 1;
    const year = now.getFullYear();

    const usage = await prisma.subscription_usage.findUnique({
      where: {
        user_subscription_id_usage_type_month_year: {
          user_subscription_id: activeSubscription.id,
          usage_type: usageType,
          month,
          year
        }
      }
    });

    const used = usage ? usage.count : 0;
    const remaining = limit - used;

    return {
      canUse: remaining > 0,
      reason: remaining > 0 ? 'Within limits' : 'Usage limit exceeded',
      limit,
      used,
      remaining: Math.max(0, remaining)
    };
  }

  /**
   * Get subscription statistics for admin
   * @returns {Promise<Object>} Statistics
   */
  static async getSubscriptionStats() {
    const [
      totalSubscriptions,
      activeSubscriptions,
      totalRevenue,
      usersByPlan
    ] = await Promise.all([
      prisma.subscriptions.count(),
      prisma.user_subscriptions.count({
        where: { status: 'ACTIVE' }
      }),
      prisma.user_subscriptions.aggregate({
        where: { status: 'ACTIVE' },
        _sum: {
          subscription: {
            price: true
          }
        }
      }),
      prisma.user_subscriptions.groupBy({
        by: ['subscription_id'],
        where: { status: 'ACTIVE' },
        _count: true
      })
    ]);

    return {
      totalSubscriptions,
      activeSubscriptions,
      totalRevenue: totalRevenue._sum?.price || 0,
      usersByPlan
    };
  }

  /**
   * Get trial subscription plan
   * @returns {Promise<Object>} Trial subscription plan
   */
  static async getTrialSubscription() {
    return await prisma.subscriptions.findFirst({
      where: {
        name: 'Trial Plan',
        is_active: true
      }
    });
  }

  /**
   * Update trial subscription duration
   * @param {number} durationDays - New duration in days
   * @returns {Promise<Object>} Updated trial subscription
   */
  static async updateTrialDuration(durationDays) {
    const trialSubscription = await this.getTrialSubscription();

    if (!trialSubscription) {
      throw new Error('Trial subscription plan not found');
    }

    return await prisma.subscriptions.update({
      where: { id: trialSubscription.id },
      data: { duration_days: durationDays }
    });
  }

  /**
   * Assign trial subscription to multiple users
   * @param {string[]} userIds - Array of user IDs
   * @returns {Promise<Object[]>} Array of created user subscriptions
   */
  static async assignTrialToUsers(userIds) {
    const trialSubscription = await this.getTrialSubscription();

    if (!trialSubscription) {
      throw new Error('Trial subscription plan not found');
    }

    const results = [];

    for (const userId of userIds) {
      try {
        // Check if user already has an active subscription
        const existingSubscription = await this.getUserActiveSubscription(userId);

        if (existingSubscription) {
          results.push({
            userId,
            success: false,
            message: 'User already has an active subscription'
          });
          continue;
        }

        // Create trial subscription for user
        const userSubscription = await this.subscribeUser(userId, trialSubscription.id, {
          payment_method: 'trial',
          transaction_id: `trial_${Date.now()}_${userId.slice(-8)}`
        });

        results.push({
          userId,
          success: true,
          subscription: userSubscription,
          message: 'Trial subscription assigned successfully'
        });
      } catch (error) {
        results.push({
          userId,
          success: false,
          message: error.message
        });
      }
    }

    return results;
  }

  /**
   * Assign trial subscription to all existing users without active subscriptions
   * @returns {Promise<Object>} Assignment results
   */
  static async assignTrialToAllUsers() {
    const trialSubscription = await this.getTrialSubscription();

    if (!trialSubscription) {
      throw new Error('Trial subscription plan not found');
    }

    // Get all users who don't have active subscriptions
    const usersWithoutSubscription = await prisma.users.findMany({
      where: {
        is_deleted: false,
        status: 'active',
        user_subscriptions: {
          none: {
            status: 'ACTIVE'
          }
        }
      },
      select: {
        id: true,
        email: true,
        first_name: true,
        last_name: true
      }
    });

    if (usersWithoutSubscription.length === 0) {
      return {
        totalUsers: 0,
        successCount: 0,
        failureCount: 0,
        results: [],
        message: 'No users found without active subscriptions'
      };
    }

    const userIds = usersWithoutSubscription.map(user => user.id);
    const results = await this.assignTrialToUsers(userIds);

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    return {
      totalUsers: usersWithoutSubscription.length,
      successCount,
      failureCount,
      results,
      message: `Trial subscription assigned to ${successCount} users, ${failureCount} failed`
    };
  }
}

module.exports = SubscriptionModel;
