# Business Information API Documentation

## Overview

The Business Information API allows sellers to manage their business information. Sellers can have multiple businesses, and each business can be verified by administrators.

## Database Schema

### business_informations Table

| Field | Type | Description |
|-------|------|-------------|
| id | UUID | Primary key |
| seller_id | UUID | Foreign key to users table |
| company_name | String | Name of the company (required) |
| short_description | String | Brief description (optional) |
| long_description | String | Detailed description (optional) |
| logo_url | String | URL to business logo (optional) |
| banner_url | String | URL to business banner (optional) |
| address | String | Business address (optional) |
| city | String | Business city (optional) |
| state | String | Business state (optional) |
| country | String | Business country (optional) |
| postal_code | String | Postal/ZIP code (optional) |
| phone_number | String | Business phone number (optional) |
| email | String | Business email (optional) |
| website_url | String | Business website (optional) |
| business_type | String | Type of business (optional) |
| business_category | String | Business category (optional) |
| established_year | Integer | Year business was established (optional) |
| employee_count | String | Number of employees (optional) |
| annual_revenue | String | Annual revenue range (optional) |
| business_license | String | Business license number (optional) |
| tax_id | String | Tax identification number (optional) |
| social_media_links | JSON | Social media links (optional) |
| operating_hours | JSON | Business operating hours (optional) |
| services_offered | JSON | List of services offered (optional) |
| certifications | JSON | Business certifications (optional) |
| is_verified | Boolean | Whether business is verified |
| verification_status | String | Verification status (pending/verified/rejected) |
| verification_date | DateTime | Date of verification (optional) |
| is_active | Boolean | Whether business is active |
| is_deleted | Boolean | Soft delete flag |
| created_at | DateTime | Creation timestamp |
| updated_at | DateTime | Last update timestamp |

## API Endpoints

### Seller Endpoints

#### 1. Create Business Information
- **POST** `/api/seller/business-information`
- **Auth Required**: Yes (Seller role)
- **Description**: Create new business information

**Request Body:**
```json
{
  "company_name": "Tech Solutions Inc",
  "short_description": "We provide innovative tech solutions",
  "long_description": "A comprehensive technology company...",
  "logo_url": "https://example.com/logo.png",
  "banner_url": "https://example.com/banner.jpg",
  "address": "123 Tech Street",
  "city": "San Francisco",
  "state": "California",
  "country": "USA",
  "postal_code": "94105",
  "phone_number": "+**********",
  "email": "<EMAIL>",
  "website_url": "https://techsolutions.com",
  "business_type": "Technology",
  "business_category": "Software Development",
  "established_year": 2020,
  "employee_count": "11-50",
  "annual_revenue": "$1M-$10M",
  "business_license": "BL123456",
  "tax_id": "TAX789012",
  "social_media_links": {
    "facebook": "https://facebook.com/techsolutions",
    "twitter": "https://twitter.com/techsolutions",
    "linkedin": "https://linkedin.com/company/techsolutions"
  },
  "operating_hours": {
    "monday": "9:00 AM - 6:00 PM",
    "tuesday": "9:00 AM - 6:00 PM",
    "wednesday": "9:00 AM - 6:00 PM",
    "thursday": "9:00 AM - 6:00 PM",
    "friday": "9:00 AM - 6:00 PM",
    "saturday": "Closed",
    "sunday": "Closed"
  },
  "services_offered": [
    "Web Development",
    "Mobile App Development",
    "Cloud Solutions",
    "IT Consulting"
  ],
  "certifications": [
    "ISO 9001:2015",
    "AWS Certified Partner",
    "Microsoft Gold Partner"
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Business information created successfully",
  "data": {
    "id": "uuid",
    "seller_id": "uuid",
    "business_name": "Tech Solutions Inc",
    // ... other fields
    "verification_status": "pending",
    "is_verified": false,
    "created_at": "2024-01-01T00:00:00.000Z",
    "updated_at": "2024-01-01T00:00:00.000Z"
  }
}
```

#### 2. Get My Business Information
- **GET** `/api/seller/business-information`
- **Auth Required**: Yes (Seller role)
- **Description**: Get all business information for the authenticated seller

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `includeInactive` (optional): Include inactive businesses (default: false)

**Response:**
```json
{
  "success": true,
  "message": "Business information retrieved successfully",
  "data": {
    "data": [
      {
        "id": "uuid",
        "business_name": "Tech Solutions Inc",
        // ... other fields
      }
    ],
    "meta": {
      "total": 5,
      "page": 1,
      "limit": 10,
      "totalPages": 1
    }
  }
}
```

#### 3. Get Business Information by ID
- **GET** `/api/seller/business-information/:id`
- **Auth Required**: Yes (Seller role)
- **Description**: Get specific business information by ID

**Response:**
```json
{
  "success": true,
  "message": "Business information retrieved successfully",
  "data": {
    "id": "uuid",
    "business_name": "Tech Solutions Inc",
    // ... other fields
  }
}
```

#### 4. Update Business Information
- **PUT** `/api/seller/business-information/:id`
- **Auth Required**: Yes (Seller role)
- **Description**: Update business information

**Request Body:** (Same as create, but all fields are optional)

#### 5. Delete Business Information
- **DELETE** `/api/seller/business-information/:id`
- **Auth Required**: Yes (Seller role)
- **Description**: Soft delete business information

### Admin Endpoints

#### 1. Get All Business Information
- **GET** `/api/admin/business-information`
- **Auth Required**: Yes (Admin role)
- **Description**: Get all business information with filtering

**Query Parameters:**
- `page` (optional): Page number
- `limit` (optional): Items per page
- `search` (optional): Search term
- `business_type` (optional): Filter by business type
- `verification_status` (optional): Filter by verification status
- `is_active` (optional): Filter by active status

#### 2. Get Business Information by ID (Admin)
- **GET** `/api/admin/business-information/:id`
- **Auth Required**: Yes (Admin role)
- **Description**: Get business information by ID (admin view)

#### 3. Update Verification Status
- **PUT** `/api/admin/business-information/:id/verification-status`
- **Auth Required**: Yes (Admin role)
- **Description**: Update verification status

**Request Body:**
```json
{
  "status": "verified" // or "pending" or "rejected"
}
```

## Business Types

- Retail
- Wholesale
- Service
- Manufacturing
- Technology
- Healthcare
- Education
- Other

## Employee Count Options

- 1-10
- 11-50
- 51-200
- 201-500
- 500+

## Annual Revenue Options

- Under $100K
- $100K-$1M
- $1M-$10M
- $10M-$100M
- $100M+

## Verification Status

- **pending**: Newly created, awaiting admin review
- **verified**: Approved by admin
- **rejected**: Rejected by admin

## Integration with Seller Profile

When fetching seller profile (`GET /api/seller/profile`), business information is automatically included:

```json
{
  "success": true,
  "message": "Seller profile retrieved successfully",
  "data": {
    "id": "uuid",
    "first_name": "John",
    "last_name": "Doe",
    // ... other user fields
    "business_informations": [
      {
        "id": "uuid",
        "business_name": "Tech Solutions Inc",
        // ... business fields
      }
    ],
    "stats": {
      "assigned_requests": 10,
      "offers_created": 25,
      "completed_transactions": 15,
      "business_informations": 2
    }
  }
}
```

## Error Handling

All endpoints return standardized error responses:

```json
{
  "success": false,
  "message": "Error message",
  "error": {
    "field_name": ["Validation error message"]
  }
}
```

Common HTTP status codes:
- 200: Success
- 201: Created
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 422: Validation Error
- 500: Internal Server Error
