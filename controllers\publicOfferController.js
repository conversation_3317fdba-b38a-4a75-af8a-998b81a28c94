const { validationResult } = require('express-validator');
const sendResponse = require('../utils/sendResponse');
const { formatValidationErrors } = require('../utils/validationFormatter');
const PublicOfferService = require('../services/publicOfferService');

class PublicOfferController {
  /**
   * Get public offers with filtering and pagination
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getPublicOffers(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const laravelStyleErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          laravelStyleErrors.message,
          null,
          laravelStyleErrors.errors,
          null,
          422
        );
      }

      // Extract query parameters
      const {
        category_id,
        subcategory_id,
        offer_type,
        min_price,
        max_price,
        search,
        sort_by,
        sort_order
      } = req.query;
      
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      // Build filters
      const filters = {};
      if (category_id) filters.category_id = category_id;
      if (subcategory_id) filters.subcategory_id = subcategory_id;
      if (offer_type) filters.offer_type = offer_type;
      if (min_price) filters.min_price = parseFloat(min_price);
      if (max_price) filters.max_price = parseFloat(max_price);
      if (search) filters.search = search;

      // Build sorting
      const sorting = {
        sort_by: sort_by || 'created_at',
        sort_order: sort_order || 'desc'
      };

      // Get offers
      const result = await PublicOfferService.getPublicOffers(filters, page, limit, sorting);

      return sendResponse(
        res,
        true,
        'Offers retrieved successfully',
        result.data,
        null,
        result.meta,
        200
      );
    } catch (error) {
      console.error('Error in getPublicOffers:', error);
      return sendResponse(
        res,
        false,
        error.message || 'Failed to retrieve offers',
        null,
        error,
        null,
        500
      );
    }
  }

  /**
   * Get offer details by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getOfferDetails(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const laravelStyleErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          laravelStyleErrors.message,
          null,
          laravelStyleErrors.errors,
          null,
          422
        );
      }

      const { id } = req.params;

      // Get offer details
      const offer = await PublicOfferService.getOfferDetails(id);

      if (!offer) {
        return sendResponse(
          res,
          false,
          'Offer not found',
          null,
          null,
          null,
          404
        );
      }

      return sendResponse(
        res,
        true,
        'Offer details retrieved successfully',
        offer,
        null,
        null,
        200
      );
    } catch (error) {
      console.error('Error in getOfferDetails:', error);
      return sendResponse(
        res,
        false,
        error.message || 'Failed to retrieve offer details',
        null,
        error,
        null,
        500
      );
    }
  }

  /**
   * Get offers by seller
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getOffersBySeller(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const laravelStyleErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          laravelStyleErrors.message,
          null,
          laravelStyleErrors.errors,
          null,
          422
        );
      }

      const { sellerId } = req.params;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      // Get offers by seller
      const result = await PublicOfferService.getOffersBySeller(sellerId, page, limit);

      return sendResponse(
        res,
        true,
        'Seller offers retrieved successfully',
        result.data,
        null,
        result.meta,
        200
      );
    } catch (error) {
      console.error('Error in getOffersBySeller:', error);
      return sendResponse(
        res,
        false,
        error.message || 'Failed to retrieve seller offers',
        null,
        error,
        null,
        500
      );
    }
  }

  /**
   * Get offers by category
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getOffersByCategory(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const laravelStyleErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          laravelStyleErrors.message,
          null,
          laravelStyleErrors.errors,
          null,
          422
        );
      }

      const { categoryId } = req.params;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      // Get offers by category
      const result = await PublicOfferService.getOffersByCategory(categoryId, page, limit);

      return sendResponse(
        res,
        true,
        'Category offers retrieved successfully',
        result.data,
        null,
        result.meta,
        200
      );
    } catch (error) {
      console.error('Error in getOffersByCategory:', error);
      return sendResponse(
        res,
        false,
        error.message || 'Failed to retrieve category offers',
        null,
        error,
        null,
        500
      );
    }
  }

  /**
   * Get offers by subcategory
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getOffersBySubcategory(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const laravelStyleErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          laravelStyleErrors.message,
          null,
          laravelStyleErrors.errors,
          null,
          422
        );
      }

      const { subcategoryId } = req.params;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      // Get offers by subcategory
      const result = await PublicOfferService.getOffersBySubcategory(subcategoryId, page, limit);

      return sendResponse(
        res,
        true,
        'Subcategory offers retrieved successfully',
        result.data,
        null,
        result.meta,
        200
      );
    } catch (error) {
      console.error('Error in getOffersBySubcategory:', error);
      return sendResponse(
        res,
        false,
        error.message || 'Failed to retrieve subcategory offers',
        null,
        error,
        null,
        500
      );
    }
  }
}

module.exports = PublicOfferController;
