const sendResponse = (res, success, message, data = null, error = null, meta = null, status_code) => {
    const response = { success, message };
    if (data) response.data = data;
    if (error) response.error = error;
    if (meta) response.meta = meta;

    // Use the provided status code or default to 200 for success and 400 for errors
    let statusCode = status_code || (success ? 200 : 400);

    res.status(statusCode).json(response);
  };

  module.exports = sendResponse;