const { body } = require('express-validator');

const completeProfileValidation = [
  // Common fields for both personal and business users
  body('first_name')
    .notEmpty()
    .withMessage('First name is required')
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),

  body('last_name')
    .notEmpty()
    .withMessage('Last name is required')
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),

  body('phone_number')
    .notEmpty()
    .withMessage('Phone number is required')
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),

  body('country')
    .notEmpty()
    .withMessage('Country is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Country must be between 2 and 100 characters'),

  body('street')
    .notEmpty()
    .withMessage('Street address is required')
    .isLength({ min: 5, max: 200 })
    .withMessage('Street address must be between 5 and 200 characters'),

  body('city')
    .notEmpty()
    .withMessage('City is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('City must be between 2 and 100 characters'),

  body('state')
    .notEmpty()
    .withMessage('State/Province is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('State/Province must be between 2 and 100 characters'),

  body('zip')
    .notEmpty()
    .withMessage('ZIP/Postal code is required')
    .isLength({ min: 3, max: 20 })
    .withMessage('ZIP/Postal code must be between 3 and 20 characters'),

  // Personal user specific fields
  body('occupation')
    .if(body('user_type').equals('personal'))
    .notEmpty()
    .withMessage('Occupation is required for personal users')
    .isLength({ min: 2, max: 100 })
    .withMessage('Occupation must be between 2 and 100 characters'),

  body('date_of_birth')
    .if(body('user_type').equals('personal'))
    .notEmpty()
    .withMessage('Date of birth is required for personal users')
    .isISO8601()
    .withMessage('Please provide a valid date of birth'),

  body('gender')
    .if(body('user_type').equals('personal'))
    .notEmpty()
    .withMessage('Gender is required for personal users')
    .isIn(['male', 'female', 'other'])
    .withMessage('Gender must be male, female, or other'),

  // Business user specific fields
  body('business_name')
    .if(body('user_type').equals('business'))
    .notEmpty()
    .withMessage('Business name is required for business users')
    .isLength({ min: 2, max: 100 })
    .withMessage('Business name must be between 2 and 100 characters'),

  body('business_type')
    .if(body('user_type').equals('business'))
    .notEmpty()
    .withMessage('Business type is required for business users')
    .isIn(['IT', 'Clothing', 'Restaurant', 'Retail', 'Service', 'Other'])
    .withMessage('Please select a valid business type'),

  body('business_address')
    .if(body('user_type').equals('business'))
    .notEmpty()
    .withMessage('Business address is required for business users')
    .isLength({ min: 10, max: 500 })
    .withMessage('Business address must be between 10 and 500 characters'),

  // Optional fields
  body('business_website')
    .optional()
    .isURL()
    .withMessage('Please provide a valid website URL'),

  body('business_registration_number')
    .optional()
    .isLength({ min: 5, max: 50 })
    .withMessage('Business registration number must be between 5 and 50 characters'),

  body('tax_id')
    .optional()
    .isLength({ min: 5, max: 50 })
    .withMessage('Tax/VAT ID must be between 5 and 50 characters'),

  body('interests')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Interests must not exceed 1000 characters'),

  body('bio')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Bio must not exceed 1000 characters'),
];

module.exports = { completeProfileValidation };
