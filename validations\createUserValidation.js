const { body } = require("express-validator");
const UserModel = require("../models/userModel");
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

const createUserValidation = [
  // Basic user information
  body("first_name")
    .notEmpty().withMessage("The first name field is required.")
    .isString().withMessage("The first name must be a string.")
    .isLength({ min: 2, max: 50 }).withMessage("The first name must be between 2 and 50 characters."),

  body("last_name")
    .notEmpty().withMessage("The last name field is required.")
    .isString().withMessage("The last name must be a string.")
    .isLength({ min: 2, max: 50 }).withMessage("The last name must be between 2 and 50 characters."),

  body("email")
    .notEmpty().withMessage("The email field is required.")
    .isEmail().withMessage("The email must be a valid email address.")
    .normalizeEmail()
    .custom(async (value) => {
      const existingUser = await UserModel.findUserByEmail(value);
      if (existingUser) {
        throw new Error("The email has already been taken.");
      }
      return true;
    }),

  body("phone_number")
    .notEmpty().withMessage("The phone number field is required.")
    .isMobilePhone().withMessage("The phone number format is invalid.")
    .custom(async (value) => {
      const existingUser = await UserModel.findUserByPhone(value);
      if (existingUser) {
        throw new Error("The phone number has already been taken.");
      }
      return true;
    }),

  body("password")
    .notEmpty().withMessage("The password field is required.")
    .isLength({ min: 8 }).withMessage("The password must be at least 8 characters.")
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])[A-Za-z\d!@#$%^&*]/)
    .withMessage("The password must contain at least 1 lowercase letter, 1 uppercase letter, 1 number (0-9), and 1 special character (!@#$%^&*)."),

  body("password_confirmation")
    .optional()
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error("The password confirmation does not match.");
      }
      return true;
    }),

  // Optional fields
  body("father_name")
    .optional()
    .isString().withMessage("The father's name must be a string.")
    .isLength({ max: 50 }).withMessage("The father's name may not be greater than 50 characters."),

  body("mother_name")
    .optional()
    .isString().withMessage("The mother's name must be a string.")
    .isLength({ max: 50 }).withMessage("The mother's name may not be greater than 50 characters."),

  body("date_of_birth")
    .optional()
    .isString().withMessage("The date of birth must be a string.")
    .custom(async (value) => {
      // Check if it's a valid date
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        throw new Error("The date of birth is not a valid date.");
      }

      // Check age
      const age = Math.floor((Date.now() - date.getTime()) / 1000 / 60 / 60 / 24 / 365.25);
      if (age < 18 || age > 120) {
        throw new Error("The age must be between 18 and 120 years.");
      }
      return true;
    }),

  body("gender")
    .optional()
    .isString().withMessage("The gender must be a string.")
    .isIn(["male", "female", "other"]).withMessage("The selected gender is invalid."),

  body("address")
    .optional()
    .isString().withMessage("The address must be a string.")
    .isLength({ max: 255 }).withMessage("The address may not be greater than 255 characters."),

  body("national_id_number")
    .optional()
    .isString().withMessage("The national ID number must be a string.")
    .custom(async (value) => {
      if (!value) return true;
      const existingUser = await prisma.users.findFirst({
        where: { national_id_number: value }
      });
      if (existingUser) {
        throw new Error("The national ID number has already been taken.");
      }
      return true;
    }),

  body("passport_number")
    .optional()
    .isString().withMessage("The passport number must be a string.")
    .custom(async (value) => {
      if (!value) return true;
      const existingUser = await prisma.users.findFirst({
        where: { passport_number: value }
      });
      if (existingUser) {
        throw new Error("The passport number has already been taken.");
      }
      return true;
    }),

  body("profile_picture_url")
    .optional(),

  // Role validation
  body("role")
    .notEmpty().withMessage("The role field is required.")
    .isString().withMessage("The role must be a string.")
    .isIn(["Buyer", "Seller", "Supplier", "Admin"]).withMessage("The selected role is invalid."),

  // Admin-only fields
  body("is_approved")
    .optional()
    .isBoolean().withMessage("The is approved field must be true or false."),

  body("status")
    .optional()
    .isString().withMessage("The status must be a string.")
    .isIn(["active", "inactive", "suspended"]).withMessage("The selected status is invalid.")
];

module.exports = { createUserValidation };
