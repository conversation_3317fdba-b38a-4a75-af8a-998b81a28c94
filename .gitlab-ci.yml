stages:
  - build
  - deploy

variables:
  NODE_ENV: development
  DEPLOY_PATH: "/var/www/Marktzoom-Backend"

build:
  stage: build
  script:
    - echo "Installing dependencies..."
    - npm install
    - echo "Running tests..."
    - npm test || echo "No tests defined, skipping."

deploy:
  stage: deploy
  script:
    - echo "Deploying to $DEPLOY_PATH..."
    - sudo mkdir -p $DEPLOY_PATH
    - echo "Syncing files with rsync..."
    - sudo rsync -avz --delete --exclude='.git' --exclude='node_modules' --exclude='.env' . $DEPLOY_PATH
    - echo "Fixing ownership..."
    - sudo chown -R www-data:www-data $DEPLOY_PATH
    - echo "Installing production dependencies in $DEPLOY_PATH..."
    - cd $DEPLOY_PATH && npm install --production
    - echo "Generating Prisma client..."
    - cd $DEPLOY_PATH && npx prisma generate
    - echo "Running Prisma migrations..."
    - cd $DEPLOY_PATH && npx prisma migrate deploy
    - echo "Seeding database..."
    - cd $DEPLOY_PATH && node prisma/seed.js || echo "Skipping seed if already seeded"
    - echo "Restarting application..."
    # Try to remove any existing PM2 process; ignore errors if not present.
    - cd $DEPLOY_PATH && pm2 delete server || true
    # Attempt to start the app with PM2; if that fails, fall back to nohup to run it in background.
    - cd $DEPLOY_PATH && pm2 start server.js --name server --update-env || (nohup npm start > /dev/null 2>&1 &)
    - echo "Deployment stage completed. Exiting deploy job."
