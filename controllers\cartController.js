const CartService = require('../services/cartService');
const sendResponse = require('../utils/sendResponse');
const { validationResult } = require('express-validator');
const { formatValidationErrors } = require('../utils/validationFormatter');

class CartController {
  /**
   * Add an offer to the cart
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async addToCart(req, res) {
    // try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const laravelStyleErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          laravelStyleErrors.message,
          null,
          laravelStyleErrors.errors,
          null,
          422
        );
      }

      const buyerId = req.user.id;
      const { offer_id, quantity } = req.body;

      const cartItem = await CartService.addToCart(buyerId, offer_id, quantity || 1);

      return sendResponse(
        res,
        true,
        'Item added to cart successfully',
        cartItem,
        null,
        null,
        201
      );
    // } catch (error) {
    //   return sendResponse(
    //     res,
    //     false,
    //     error.message,
    //     null,
    //     { general: [error.message] },
    //     null,
    //     error.statusCode || 400
    //   );
    // }
  }

  /**
   * Get all items in a buyer's cart
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getCartItems(req, res) {
    try {
      const buyerId = req.user.id;
      const cart = await CartService.getCartItems(buyerId);

      return sendResponse(
        res,
        true,
        'Cart items retrieved successfully',
        cart,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Update a cart item
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async updateCartItem(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const laravelStyleErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          laravelStyleErrors.message,
          null,
          laravelStyleErrors.errors,
          null,
          422
        );
      }

      const buyerId = req.user.id;
      const cartItemId = req.params.itemId;
      const { quantity } = req.body;

      const updatedItem = await CartService.updateCartItem(buyerId, cartItemId, quantity);

      return sendResponse(
        res,
        true,
        'Cart item updated successfully',
        updatedItem,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 400
      );
    }
  }

  /**
   * Remove an item from the cart
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async removeCartItem(req, res) {
    try {
      const buyerId = req.user.id;
      const cartItemId = req.params.itemId;

      await CartService.removeCartItem(buyerId, cartItemId);

      return sendResponse(
        res,
        true,
        'Item removed from cart successfully',
        null,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 400
      );
    }
  }

  /**
   * Clear all items from a buyer's cart
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async clearCart(req, res) {
    try {
      const buyerId = req.user.id;
      await CartService.clearCart(buyerId);

      return sendResponse(
        res,
        true,
        'Cart cleared successfully',
        null,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }
}

module.exports = CartController;
