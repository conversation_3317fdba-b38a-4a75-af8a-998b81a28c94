const UserModel = require('../models/userModel');
const SubscriptionModel = require('../models/subscriptionModel');
const jwt = require('jsonwebtoken');
const nodemailer = require('nodemailer');
require('dotenv').config();

const otpService = require('./otpService');

class AuthService {
  /**
   * Get user subscription information for auth responses
   * @param {string} userId - User ID
   * @returns {Promise<Object|null>} Subscription information
   */
  async getUserSubscriptionInfo(userId) {
    try {
      const activeSubscription = await SubscriptionModel.getUserActiveSubscription(userId);

      if (!activeSubscription) {
        return null;
      }

      return {
        id: activeSubscription.id,
        subscription_id: activeSubscription.subscription_id,
        status: activeSubscription.status,
        start_date: activeSubscription.start_date,
        end_date: activeSubscription.end_date,
        auto_renew: activeSubscription.auto_renew,
        plan: {
          id: activeSubscription.subscription.id,
          name: activeSubscription.subscription.name,
          description: activeSubscription.subscription.description,
          price: activeSubscription.subscription.price,
          duration_days: activeSubscription.subscription.duration_days,
          max_requests: activeSubscription.subscription.max_requests,
          max_offers: activeSubscription.subscription.max_offers,
          max_orders: activeSubscription.subscription.max_orders,
          user_type: activeSubscription.subscription.user_type,
          features: activeSubscription.subscription.features
        }
      };
    } catch (error) {
      console.error('Error getting user subscription info:', error);
      return null;
    }
  }

  async register(userData) {
    const { email, phone_number, password, role } = userData;

    // Check if user exists
    const existingUser = await UserModel.findUserByEmail(email);
    if (existingUser) {
      throw {
        type: "ValidationError",
        errors: { email: "Email is already in use." },
      };
    }

    // Set default role to 'Buyer' for public registration
    userData.role = 'Buyer';

    const user = await UserModel.createUser(userData);

    // Transform roles to simple array (same as login)
    if (user.roles) {
      user.roles = user.roles.map(userRole => userRole.role.name);
    }

    // Remove sensitive data
    delete user.password_hash;

    // Generate email verification token
    const token = await UserModel.generateEmailVerificationToken(user.id);

    // Send verification email
    // await this.sendVerificationEmail(user.email, token);

    // Generate tokens with the same expiration as login
    const accessToken = jwt.sign(
      { id: user.id, email: user.email, roles: user.roles },
      process.env.JWT_SECRET,
      { expiresIn: "7d" }
    );

    const refreshToken = jwt.sign({ id: user.id }, process.env.REFRESH_SECRET, { expiresIn: "7d" });

    await UserModel.storeRefreshToken(user.id, refreshToken);

    // Get subscription information
    const subscription = await this.getUserSubscriptionInfo(user.id);

    // Return the same structure as login
    return { accessToken, refreshToken, user, subscription };
  }

  async login(email, password) {
    const user = await UserModel.findUserByEmail(email);
    if (!user) throw new Error("User not found");

    // if (!user.emailVerified) throw new Error("Email not verified. Please check your email.");

    const isPasswordValid = await UserModel.comparePassword(password, user.password_hash);
    if (!isPasswordValid) throw new Error("Invalid credentials");

    // Check if user has Buyer or Seller role
    const hasValidRole = user.roles.some(role => role === 'Buyer' || role === 'Seller');
    if (!hasValidRole) {
      throw new Error("Access denied. Only Buyers and Sellers can log in through this portal.");
    }

    delete user.password_hash;

    const accessToken = jwt.sign(
      { id: user.id, email: user.email, roles: user.roles },
      process.env.JWT_SECRET,
      { expiresIn: "7d" }
    );

    const refreshToken = jwt.sign({ id: user.id }, process.env.REFRESH_SECRET, { expiresIn: "7d" });

    await UserModel.storeRefreshToken(user.id, refreshToken);

    // Get subscription information
    const subscription = await this.getUserSubscriptionInfo(user.id);

    return { accessToken, refreshToken, user, subscription };
  }

  async logout(userId) {
    return { message: "Logged out successfully" };
  }

  async refreshToken(token) {
    try {
      const decoded = jwt.verify(token, process.env.REFRESH_SECRET);
      const user = await UserModel.findUserById(decoded.id);

      if (!user) throw new Error("Invalid refresh token");

      const newAccessToken = jwt.sign(
        { id: user.id, email: user.email, roles: user.roles },
        process.env.JWT_SECRET,
        { expiresIn: "7d" }
      );

      return { accessToken: newAccessToken };
    } catch (error) {
      throw new Error("Invalid or expired refresh token");
    }
  }

  async sendVerificationEmail(email, token) {
    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    const verificationLink = `${process.env.FRONTEND_URL}/verify-email?token=${token}`;

    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: email,
      subject: "Verify Your Email",
      html: `<p>Click <a href="${verificationLink}">here</a> to verify your email.</p>`,
    };

    await transporter.sendMail(mailOptions);
  }

  async verifyEmail(token) {
    const user = await prisma.user.findFirst({
      where: {
        emailVerificationToken: token,
        emailVerificationExpires: { gt: new Date() },
      },
    });

    if (!user) throw new Error("Invalid or expired email verification token.");

    await UserModel.updateUser(user.id, {
      emailVerified: true,
      emailVerificationToken: null,
      emailVerificationExpires: null,
    });

    return { message: "Email verified successfully. You can now log in." };
  }

  async forgotPassword(email) {
    const user = await UserModel.findUserByEmail(email);
    if (!user) throw new Error("User not found");

    const token = await UserModel.generatePasswordResetToken(user.id);

    const resetLink = `${process.env.FRONTEND_URL}/reset-password?token=${token}`;
    console.log(resetLink);
    // await this.sendPasswordResetEmail(email, resetLink);

    return { message: "Password reset email sent" };
  }

  async sendPasswordResetEmail(email, resetLink) {
    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: email,
      subject: "Reset Your Password",
      html: `<p>Click <a href="${resetLink}">here</a> to reset your password.</p>`,
    };

    await transporter.sendMail(mailOptions);
  }

  async resetPassword(token, newPassword) {
    const user = await UserModel.findUserByResetToken(token);
    if (!user) throw new Error("Invalid or expired password reset token.");

    await UserModel.resetPassword(user.id, newPassword);

    return { message: "Password reset successfully. You can now log in." };
  }


  // Mobile registration: send OTP to email, store registration data
  async mobileRegister(userData) {
    const { email } = userData;
    // Check if user already exists
    const existingUser = await UserModel.findUserByEmail(email);
    if (existingUser) {
      throw {
        type: "ValidationError",
        errors: { email: "Email is already in use." },
      };
    }
    // Generate OTP
    const otp = otpService.generateOtp();
    // Store pending registration
    await otpService.storePendingRegistration(email, otp, userData);
    // Send OTP email
    await otpService.sendOtpEmail(email, otp);
    return { message: `OTP sent to ${email}` };
  }

  // Verify OTP and complete registration
  async verifyOtp(email, otp) {
    const pending = await otpService.getPendingRegistration(email);
    if (!pending || pending.otp !== otp || new Date() > pending.expires_at) {
      throw {
        type: "ValidationError",
        errors: { otp: "Invalid or expired OTP." },
      };
    }
    // Register user using stored data
    const result = await this.register(pending.data);
    // Clean up
    await otpService.deletePendingRegistration(email);
    return result;
  }

  // New user registration with username, password, user type, and role
  async newUserRegister(userData) {
    const {
      username,
      password,
      user_type,
      role,
      age_confirm,
      email,
      phone_number,
      business_name,
      full_name
    } = userData;

    // Check if username already exists
    const existingUser = await UserModel.findUserByUsername(username);
    if (existingUser) {
      throw {
        type: "ValidationError",
        errors: { username: "Username is already in use." },
      };
    }

    // Check if email already exists (if provided)
    if (email) {
      const existingEmailUser = await UserModel.findUserByEmail(email);
      if (existingEmailUser) {
        throw {
          type: "ValidationError",
          errors: { email: "Email is already in use." },
        };
      }
    }

    // Split full name into first and last name if provided
    let first_name = null;
    let last_name = null;
    if (full_name) {
      const nameParts = full_name.trim().split(' ');
      first_name = nameParts[0];
      last_name = nameParts.slice(1).join(' ') || null;
    }

    // Use the selected role from the request
    const roleName = role;

    // Prepare user data
    const newUserData = {
      username,
      password,
      email: email || null,
      phone_number: phone_number || null, // Temporary phone if not provided
      first_name,
      last_name,
      business_name: user_type === 'business' ? business_name : null,
      user_type,
      age_confirm,
      is_completed_profile: false,
      role: roleName
    };

    const user = await UserModel.createUser(newUserData);

    // Transform roles to simple array (same as login)
    if (user.roles) {
      user.roles = user.roles.map(userRole => userRole.role.name);
    }

    // Remove sensitive data
    delete user.password_hash;

    // Generate tokens with the same expiration as login
    const accessToken = jwt.sign(
      {
        id: user.id,
        email: user.email,
        username: user.username,
        user_type: user.user_type,
        roles: user.roles,
        is_completed_profile: user.is_completed_profile
      },
      process.env.JWT_SECRET,
      { expiresIn: "7d" }
    );

    const refreshToken = jwt.sign({ id: user.id }, process.env.REFRESH_SECRET, { expiresIn: "7d" });

    await UserModel.storeRefreshToken(user.id, refreshToken);

    // Get subscription information
    const subscription = await this.getUserSubscriptionInfo(user.id);

    // Return the same structure as login
    return { accessToken, refreshToken, user, subscription };
  }

  // Login with username or email
  async newUserLogin(login, password) {
    // Try to find user by username or email
    let user = await UserModel.findUserByUsername(login);
    if (!user) {
      user = await UserModel.findUserByEmail(login);
    }

    if (!user) throw new Error("User not found");

    // Check if user is approved
    // if (!user.is_approved) {
    //   throw new Error("Your account is pending approval. Please wait for admin approval.");
    // }

    // Check if user is active
    if (user.status !== 'active') {
      throw new Error("Your account is not active. Please contact support.");
    }

    const isPasswordValid = await UserModel.comparePassword(password, user.password_hash);
    if (!isPasswordValid) throw new Error("Invalid credentials");

    // Check if user has Buyer or Seller role
    const hasValidRole = user.roles.some(role => role === 'Buyer' || role === 'Seller');
    if (!hasValidRole) {
      throw new Error("Access denied. Only Buyers and Sellers can log in through this portal.");
    }

    delete user.password_hash;

    // Update last login
    await UserModel.updateUser(user.id, { last_login_at: new Date() });

    const accessToken = jwt.sign(
      {
        id: user.id,
        email: user.email,
        username: user.username,
        user_type: user.user_type,
        roles: user.roles,
        is_completed_profile: user.is_completed_profile
      },
      process.env.JWT_SECRET,
      { expiresIn: "7d" }
    );

    const refreshToken = jwt.sign({ id: user.id }, process.env.REFRESH_SECRET, { expiresIn: "7d" });

    await UserModel.storeRefreshToken(user.id, refreshToken);

    // Get subscription information
    const subscription = await this.getUserSubscriptionInfo(user.id);

    return { accessToken, refreshToken, user, subscription };
  }

  // Complete user profile
  async completeProfile(userId, profileData, files) {
    try {
      // Get current user to check user_type
      const currentUser = await UserModel.findUserById(userId);
      if (!currentUser) {
        throw new Error("User not found");
      }

      // Handle file uploads if provided
      let profilePictureUrl = null;
      let documentUrl = null;

      if (files) {
        if (files.profile_picture) {
          // Handle profile picture upload
          const { saveBufferToFile } = require('../utils/fileUpload');
          profilePictureUrl = await saveBufferToFile(
            files.profile_picture[0].buffer,
            files.profile_picture[0].originalname,
            'uploads/profiles'
          );
        }

        // Handle document upload based on user type
        if (currentUser.user_type === 'business' && files.business_document) {
          const { saveBufferToFile } = require('../utils/fileUpload');
          documentUrl = await saveBufferToFile(
            files.business_document[0].buffer,
            files.business_document[0].originalname,
            'uploads/documents/business'
          );
        } else if (currentUser.user_type === 'personal' && files.nid_document) {
          const { saveBufferToFile } = require('../utils/fileUpload');
          documentUrl = await saveBufferToFile(
            files.nid_document[0].buffer,
            files.nid_document[0].originalname,
            'uploads/documents/nid'
          );
        }
      }

      // Prepare update data
      const updateData = {
        ...profileData,
        is_completed_profile: true
      };

      // Add file URLs if uploaded
      if (profilePictureUrl) {
        updateData.profile_picture_url = profilePictureUrl;
      }

      if (documentUrl) {
        if (currentUser.user_type === 'business') {
          updateData.business_document_url = documentUrl;
        } else {
          updateData.nid_document_url = documentUrl;
        }
      }

      // Convert date_of_birth to Date object if it's a string
      if (updateData.date_of_birth && typeof updateData.date_of_birth === 'string') {
        updateData.date_of_birth = new Date(updateData.date_of_birth);
      }

      // Update user profile
      const updatedUser = await UserModel.updateUser(userId, updateData);

      // Remove sensitive data
      delete updatedUser.password_hash;

      return updatedUser;

    } catch (error) {
      console.error('Complete profile error:', error);
      throw new Error(error.message || 'Failed to complete profile');
    }
  }

  // Get profile completion status
  async getProfileStatus(userId) {
    try {
      const user = await UserModel.findUserById(userId);
      if (!user) {
        throw new Error("User not found");
      }

      // Define required fields for each user type
      const commonRequiredFields = [
        'first_name', 'last_name', 'phone_number', 'country',
        'street', 'city', 'state', 'zip'
      ];

      const personalRequiredFields = [
        ...commonRequiredFields,
        'occupation', 'date_of_birth', 'gender', 'nid_document_url'
      ];

      const businessRequiredFields = [
        ...commonRequiredFields,
        'business_name', 'business_type', 'business_address', 'business_document_url'
      ];

      const requiredFields = user.user_type === 'business'
        ? businessRequiredFields
        : personalRequiredFields;

      // Check completion status
      const missingFields = requiredFields.filter(field => !user[field]);
      const isComplete = missingFields.length === 0;

      return {
        is_completed_profile: user.is_completed_profile && isComplete,
        user_type: user.user_type,
        required_fields: requiredFields,
        missing_fields: missingFields,
        completion_percentage: Math.round(((requiredFields.length - missingFields.length) / requiredFields.length) * 100)
      };

    } catch (error) {
      console.error('Get profile status error:', error);
      throw new Error(error.message || 'Failed to get profile status');
    }
  }

  // Upload profile picture
  async uploadProfilePicture(userId, file) {
    try {
      // Get current user
      const currentUser = await UserModel.findUserById(userId);
      if (!currentUser) {
        throw new Error("User not found");
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
      if (!allowedTypes.includes(file.mimetype)) {
        throw new Error("Invalid file type. Only JPEG, JPG, PNG, and GIF files are allowed.");
      }

      // Validate file size (5MB limit)
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      if (file.size > maxSize) {
        throw new Error("File size too large. Maximum size is 5MB.");
      }

      // Save file using the upload utility
      const { saveBufferToFile } = require('../utils/fileUpload');
      const profilePictureUrl = saveBufferToFile(
        file.buffer,
        file.originalname,
        'uploads/profiles'
      );

      // Update user profile picture URL
      const updatedUser = await UserModel.updateUser(userId, {
        profile_picture_url: profilePictureUrl
      });

      // Remove sensitive data
      delete updatedUser.password_hash;

      return {
        user: updatedUser,
        profile_picture_url: profilePictureUrl,
        message: "Profile picture uploaded successfully"
      };

    } catch (error) {
      console.error('Upload profile picture error:', error);
      throw new Error(error.message || 'Failed to upload profile picture');
    }
  }
}
module.exports = new AuthService();
