const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

class OfferModel {
  /**
   * Get all offers with pagination and filtering
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated offers with request and seller information
   */
  static async getAllOffers(filters = {}, page = 1, limit = 10) {
    const whereClause = {
      is_deleted: false,
    };

    // Apply filters with case-insensitive matching where appropriate
    if (filters.status) {
      whereClause.status = {
        equals: filters.status,
        mode: 'insensitive', // Case-insensitive matching
      };
    }

    if (filters.request_id) {
      whereClause.request_id = filters.request_id; // ID fields typically don't need case-insensitive
    }

    if (filters.seller_id) {
      whereClause.seller_id = filters.seller_id; // ID fields typically don't need case-insensitive
    }

    // Get total count for pagination
    const totalCount = await prisma.offers.count({
      where: where<PERSON>lause,
    });

    // Get paginated offers with relations
    const offers = await prisma.offers.findMany({
      where: whereClause,
      skip: (page - 1) * limit,
      take: limit,
      orderBy: { created_at: 'desc' },
      include: {
        request: {
          include: {
            buyer: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
                email: true,
                profile_picture_url: true,
              },
            },
            category: true,
            sub_category: true,
          },
        },
        seller: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            profile_picture_url: true,
          },
        },
        offer_status_changes: {
          orderBy: { created_at: 'desc' },
          take: 1,
        },
      },
    });

    return {
      data: offers,
      meta: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
    };
  }
  /**
   * Get offer by ID
   * @param {string} offerId - Offer ID
   * @returns {Promise<Object>} Offer with request and seller information
   */
  static async getOfferById(offerId) {
    return await prisma.offers.findUnique({
      where: { id: offerId, is_deleted: false },
      include: {
        request: {
          include: {
            buyer: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
                email: true,
                profile_picture_url: true,
              },
            },
            category: true,
            sub_category: true,
          },
        },
        seller: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            profile_picture_url: true,
          },
        },
        offer_status_changes: {
          orderBy: { created_at: 'desc' },
          take: 1,
        },
        offer_attachments: true,
      },
    });
  }

  /**
   * Update offer status
   * @param {string} offerId - Offer ID
   * @param {string} status - New status
   * @param {string} userId - User ID making the update
   * @param {string} reason - Reason for status change (optional)
   * @returns {Promise<Object>} Updated offer
   */
  static async updateOfferStatus(offerId, status, userId, reason = null) {
    // Use a transaction to ensure both the offer and status change are updated
    return await prisma.$transaction(async (tx) => {
      // First, get the current offer to check its status
      const offer = await tx.offers.findUnique({
        where: { id: offerId, is_deleted: false },
      });

      if (!offer) {
        throw new Error('Offer not found');
      }

      const previousStatus = offer.status;

      // Only update if the status is actually changing
      if (status === previousStatus) {
        return offer; // No change needed
      }

      // Update the offer status
      const updatedOffer = await tx.offers.update({
        where: { id: offerId },
        data: {
          status,
          updated_at: new Date()
        },
        include: {
          request: {
            include: {
              buyer: {
                select: {
                  id: true,
                  first_name: true,
                  last_name: true,
                  email: true,
                  profile_picture_url: true,
                },
              },
              category: true,
              sub_category: true,
            },
          },
          seller: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
              profile_picture_url: true,
            },
          },
          offer_status_changes: {
            orderBy: { created_at: 'desc' },
            take: 1,
          },
          offer_attachments: true,
        },
      });

      // Create a status change record
      await tx.offer_status_changes.create({
        data: {
          offer_id: offerId,
          updated_by: userId,
          status,
          previous_status: previousStatus,
          reason,
        },
      });

      return updatedOffer;
    });
  }
}

module.exports = OfferModel;
