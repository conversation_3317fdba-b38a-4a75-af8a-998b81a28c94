const { body } = require('express-validator');

const updateOfferValidation = [
  body('price')
    .optional()
    .isFloat({ min: 0.01 })
    .withMessage('The price must be greater than 0.'),
  
  body('delivery_time')
    .optional()
    .isInt({ min: 1 })
    .withMessage('The delivery time must be at least 1 day.'),
  
  body('message')
    .optional()
    .isString()
    .withMessage('The message must be a string.')
    .isLength({ max: 1000 })
    .withMessage('The message may not be greater than 1000 characters.'),
  
  body('description')
    .optional()
    .isString()
    .withMessage('The description must be a string.')
    .isLength({ max: 5000 })
    .withMessage('The description may not be greater than 5000 characters.'),
];

module.exports = updateOfferValidation;
