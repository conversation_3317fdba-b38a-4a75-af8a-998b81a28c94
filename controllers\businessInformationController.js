const { validationResult } = require('express-validator');
const SellerModel = require('../models/sellerModel');
const BusinessInformationModel = require('../models/businessInformationModel');
const sendResponse = require('../utils/sendResponse');
const { formatValidationErrors } = require('../utils/validationFormatter');
const { saveBufferToFile } = require('../utils/fileUpload');


class BusinessInformationController {
  /**
   * Create business information
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async createBusinessInformation(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const formattedErrors = formatValidationErrors(errors.array());
        return sendResponse(res, false, "Validation failed", null, formattedErrors.errors, null, 422);
      }

      const sellerId = req.user.id;
      const businessData = req.body;

      // Handle file uploads
      if (req.files) {
        if (req.files.logo && req.files.logo[0]) {
          try {
            const logoPath = saveBufferToFile(
              req.files.logo[0].buffer,
              req.files.logo[0].originalname,
              'uploads/business/logos'
            );
            businessData.logo_url = logoPath;
          } catch (error) {
            console.error('Logo upload error:', error);
            return sendResponse(res, false, "Failed to upload logo file", null, null, null, 500);
          }
        }

        if (req.files.banner && req.files.banner[0]) {
          try {
            const bannerPath = saveBufferToFile(
              req.files.banner[0].buffer,
              req.files.banner[0].originalname,
              'uploads/business/banners'
            );
            businessData.banner_url = bannerPath;
          } catch (error) {
            console.error('Banner upload error:', error);
            return sendResponse(res, false, "Failed to upload banner file", null, null, null, 500);
          }
        }
      }

      // Parse JSON fields if they come as strings from form data
      if (typeof businessData.social_media_links === 'string') {
        try {
          businessData.social_media_links = JSON.parse(businessData.social_media_links);
        } catch (e) {
          businessData.social_media_links = null;
        }
      }

      if (typeof businessData.operating_hours === 'string') {
        try {
          businessData.operating_hours = JSON.parse(businessData.operating_hours);
        } catch (e) {
          businessData.operating_hours = null;
        }
      }

      if (typeof businessData.services_offered === 'string') {
        try {
          businessData.services_offered = JSON.parse(businessData.services_offered);
        } catch (e) {
          businessData.services_offered = null;
        }
      }

      if (typeof businessData.certifications === 'string') {
        try {
          businessData.certifications = JSON.parse(businessData.certifications);
        } catch (e) {
          businessData.certifications = null;
        }
      }

      // Convert string boolean values to actual booleans
      if (typeof businessData.is_active === 'string') {
        businessData.is_active = businessData.is_active === 'true';
      }

      // Convert string numbers to integers
      if (typeof businessData.established_year === 'string') {
        const year = parseInt(businessData.established_year);
        businessData.established_year = isNaN(year) ? null : year;
      }

      const businessInformation = await SellerModel.createBusinessInformation(sellerId, businessData);

      return sendResponse(res, true, "Business information created successfully", businessInformation, null, null, 201);
    } catch (error) {
      console.error('Create business information error:', error.message);
      return sendResponse(res, false, error.message, null, null, null, 500);
    }
  }

  /**
   * Get all business information for the authenticated seller
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getMyBusinessInformations(req, res) {
    try {
      const sellerId = req.user.id;
      const { page = 1, limit = 10, includeInactive = false } = req.query;

      const options = {
        page: parseInt(page),
        limit: parseInt(limit),
        includeInactive: includeInactive === 'true'
      };

      const result = await SellerModel.getSellerBusinessInformations(sellerId, options);

      return sendResponse(res, true, "Business information retrieved successfully", result);
    } catch (error) {
      console.error('Get business information error:', error.message);
      return sendResponse(res, false, error.message, null, null, null, 500);
    }
  }

  /**
   * Get business information by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getBusinessInformationById(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const formattedErrors = formatValidationErrors(errors.array());
        return sendResponse(res, false, "Validation failed", null, formattedErrors.errors, null, 422);
      }

      const sellerId = req.user.id;
      const { id } = req.params;

      const businessInformation = await SellerModel.getBusinessInformationById(id, sellerId);

      if (!businessInformation) {
        return sendResponse(res, false, "Business information not found", null, null, null, 404);
      }

      return sendResponse(res, true, "Business information retrieved successfully", businessInformation);
    } catch (error) {
      console.error('Get business information error:', error.message);
      return sendResponse(res, false, error.message, null, null, null, 500);
    }
  }

  /**
   * Update business information
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async updateBusinessInformation(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const formattedErrors = formatValidationErrors(errors.array());
        return sendResponse(res, false, "Validation failed", null, formattedErrors.errors, null, 422);
      }

      const sellerId = req.user.id;
      const { id } = req.params;
      const updateData = req.body;

      // Handle file uploads
      if (req.files) {
        if (req.files.logo && req.files.logo[0]) {
          try {
            const logoPath = saveBufferToFile(
              req.files.logo[0].buffer,
              req.files.logo[0].originalname,
              'uploads/business/logos'
            );
            updateData.logo_url = logoPath;
          } catch (error) {
            console.error('Logo upload error:', error);
            return sendResponse(res, false, "Failed to upload logo file", null, null, null, 500);
          }
        }

        if (req.files.banner && req.files.banner[0]) {
          try {
            const bannerPath = saveBufferToFile(
              req.files.banner[0].buffer,
              req.files.banner[0].originalname,
              'uploads/business/banners'
            );
            updateData.banner_url = bannerPath;
          } catch (error) {
            console.error('Banner upload error:', error);
            return sendResponse(res, false, "Failed to upload banner file", null, null, null, 500);
          }
        }
      }

      // Parse JSON fields if they come as strings from form data
      if (typeof updateData.social_media_links === 'string') {
        try {
          updateData.social_media_links = JSON.parse(updateData.social_media_links);
        } catch (e) {
          updateData.social_media_links = null;
        }
      }

      if (typeof updateData.operating_hours === 'string') {
        try {
          updateData.operating_hours = JSON.parse(updateData.operating_hours);
        } catch (e) {
          updateData.operating_hours = null;
        }
      }

      if (typeof updateData.services_offered === 'string') {
        try {
          updateData.services_offered = JSON.parse(updateData.services_offered);
        } catch (e) {
          updateData.services_offered = null;
        }
      }

      if (typeof updateData.certifications === 'string') {
        try {
          updateData.certifications = JSON.parse(updateData.certifications);
        } catch (e) {
          updateData.certifications = null;
        }
      }

      // Convert string boolean values to actual booleans
      if (typeof updateData.is_active === 'string') {
        updateData.is_active = updateData.is_active === 'true';
      }

      // Convert string numbers to integers
      if (typeof updateData.established_year === 'string') {
        const year = parseInt(updateData.established_year);
        updateData.established_year = isNaN(year) ? null : year;
      }

      const updatedBusinessInformation = await SellerModel.updateBusinessInformation(id, sellerId, updateData);

      return sendResponse(res, true, "Business information updated successfully", updatedBusinessInformation);
    } catch (error) {
      console.error('Update business information error:', error.message);
      if (error.message.includes('not found') || error.message.includes('access denied')) {
        return sendResponse(res, false, error.message, null, null, null, 404);
      }
      return sendResponse(res, false, error.message, null, null, null, 500);
    }
  }

  /**
   * Delete business information
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async deleteBusinessInformation(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const formattedErrors = formatValidationErrors(errors.array());
        return sendResponse(res, false, "Validation failed", null, formattedErrors.errors, null, 422);
      }

      const sellerId = req.user.id;
      const { id } = req.params;

      await SellerModel.deleteBusinessInformation(id, sellerId);

      return sendResponse(res, true, "Business information deleted successfully", null);
    } catch (error) {
      console.error('Delete business information error:', error.message);
      if (error.message.includes('not found') || error.message.includes('access denied')) {
        return sendResponse(res, false, error.message, null, null, null, 404);
      }
      return sendResponse(res, false, error.message, null, null, null, 500);
    }
  }

  // Admin methods

  /**
   * Get all business information (Admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getAllBusinessInformations(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const formattedErrors = formatValidationErrors(errors.array());
        return sendResponse(res, false, "Validation failed", null, formattedErrors.errors, null, 422);
      }

      const {
        page = 1,
        limit = 10,
        search = '',
        business_type = '',
        verification_status = '',
        is_active = null
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: parseInt(limit),
        search,
        business_type,
        verification_status,
        is_active: is_active !== null ? is_active === 'true' : null
      };

      const result = await BusinessInformationModel.getAllBusinessInformations(options);

      return sendResponse(res, true, "Business information retrieved successfully", result);
    } catch (error) {
      console.error('Get all business information error:', error.message);
      return sendResponse(res, false, error.message, null, null, null, 500);
    }
  }

  /**
   * Update verification status (Admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async updateVerificationStatus(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const formattedErrors = formatValidationErrors(errors.array());
        return sendResponse(res, false, "Validation failed", null, formattedErrors.errors, null, 422);
      }

      const { id } = req.params;
      const { status } = req.body;

      const updatedBusinessInformation = await BusinessInformationModel.updateVerificationStatus(id, status);

      return sendResponse(res, true, "Verification status updated successfully", updatedBusinessInformation);
    } catch (error) {
      console.error('Update verification status error:', error.message);
      return sendResponse(res, false, error.message, null, null, null, 500);
    }
  }

  /**
   * Get business information by ID (Admin view)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getBusinessInformationByIdAdmin(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const formattedErrors = formatValidationErrors(errors.array());
        return sendResponse(res, false, "Validation failed", null, formattedErrors.errors, null, 422);
      }

      const { id } = req.params;

      const businessInformation = await BusinessInformationModel.getBusinessInformationById(id);

      if (!businessInformation) {
        return sendResponse(res, false, "Business information not found", null, null, null, 404);
      }

      return sendResponse(res, true, "Business information retrieved successfully", businessInformation);
    } catch (error) {
      console.error('Get business information error:', error.message);
      return sendResponse(res, false, error.message, null, null, null, 500);
    }
  }
}

module.exports = new BusinessInformationController();
