generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}


model activity_logs {
  id        String   @id @default(uuid())
  action    String
  user_id   String   @db.Uuid
  details   String
  timestamp DateTime @default(now())

  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
}



model business_informations {
  id                    String   @id @default(uuid())
  seller_id             String
  company_name          String
  short_description     String?
  long_description      String?
  logo_url              String?
  banner_url            String?
  address               String?
  city                  String?
  state                 String?
  country               String?
  postal_code           String?
  phone_number          String?
  email                 String?
  website_url           String?
  business_type         String?  // e.g., "Retail", "Wholesale", "Service", "Manufacturing"
  business_category     String?  // e.g., "Electronics", "Clothing", "Food"
  established_year      Int?
  employee_count        String?  // e.g., "1-10", "11-50", "51-200", "200+"
  annual_revenue        String?  // e.g., "Under $1M", "$1M-$10M", "$10M+"
  business_license      String?
  tax_id                String?
  social_media_links    Json?    // Store social media links as JSON
  operating_hours       Json?    // Store operating hours as JSON
  services_offered      Json?    // Store list of services as JSON
  certifications        Json?    // Store certifications as JSON
  is_verified           Boolean  @default(false)
  verification_status   String   @default("pending") // "pending", "verified", "rejected"
  verification_date     DateTime?
  is_active             Boolean  @default(true)
  is_deleted            Boolean  @default(false)
  created_at            DateTime @default(now())
  updated_at            DateTime @updatedAt

  // Relations
  seller                users    @relation(fields: [seller_id], references: [id], onDelete: Cascade)

  @@map("business_informations")
}


model carts {
  id         String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  buyer_id   String       @unique
  created_at DateTime     @default(now()) @db.Timestamp(6)
  updated_at DateTime     @default(now()) @updatedAt @db.Timestamp(6)
  
  // Relations
  buyer      users        @relation("BuyerCart", fields: [buyer_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  cart_items cart_items[]
}

model cart_items {
  id         String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  cart_id    String   @db.Uuid
  offer_id   String
  quantity   Int      @default(1)
  price      Float
  created_at DateTime @default(now()) @db.Timestamp(6)
  updated_at DateTime @default(now()) @updatedAt @db.Timestamp(6)
  
  // Relations
  cart       carts    @relation(fields: [cart_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  offer      offers   @relation("CartOffer", fields: [offer_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([cart_id, offer_id])
}


model categories {
  id            String    @id @default(uuid())
  title         String    @unique
  description   String?
  color         String?   // Hex color code for category
  image         String?   // URL to main category image
  thumbnail     String?   // URL to thumbnail image
  created_at    DateTime  @default(now())
  updated_at    DateTime  @updatedAt
  is_deleted    Boolean   @default(false)
  is_featured   Boolean   @default(false)
  is_premium   Boolean   @default(false)
  is_active   Boolean   @default(true)
  sort_order    Int?     

  // Relations
  sub_categories  sub_categories[] @relation("Category")
  requests        requests[]       @relation("RequestCategory")
  offers          offers[]         @relation("OfferCategory")
  products       products[]        @relation("ProductCategory")
  services       services[]        @relation("ServiceCategory")
  translations   category_translations[]

  // Metadata
  seo_title       String?
  seo_description String?
  seo_keywords    String?
}

model category_translations {
  id          String   @id @default(uuid())
  category_id String
  language    String   // e.g., 'en', 'bn', 'fr'
  title       String
  description String?
  seo_title   String?
  seo_description String?
  seo_keywords String?

  // Relations
  category    categories @relation(fields: [category_id], references: [id])

  @@unique([category_id, language]) // Prevent duplicate translations
}

model offers {
  id                   String   @id @default(uuid())
  offer_code           String?  @unique
  request_id           String?  // Made optional
  seller_id            String
  offer_title          String?
  category_id          String?
  subcategory_id       String?
  short_description    String?
  price                Float
  discount             Float?
  quantity             Int      @default(1)
  delivery_time        Int
  message              String?
  description          String?
  offer_type           offer_type_enum @default(service)
  status               String   @default("Pending")
  created_at           DateTime @default(now())
  updated_at           DateTime @updatedAt
  is_deleted           Boolean  @default(false)

  // Relations
  request              requests? @relation(fields: [request_id], references: [id], name: "RequestOffer")
  seller               users @relation(fields: [seller_id], references: [id], name: "Seller")
  category             categories? @relation(fields: [category_id], references: [id], name: "OfferCategory")
  subcategory          sub_categories? @relation(fields: [subcategory_id], references: [id], name: "OfferSubCategory")

  // Child Relations
  offer_attachments    offer_attachments[]
  offer_status_changes offer_status_changes[]
  offer_negotiations   offer_negotiations[]
  offer_form_field_values offer_form_field_values[]
  orders         orders[] @relation("OfferOrder")
  cart_items     cart_items[] @relation("CartOffer")

  @@index([category_id])
  @@index([subcategory_id])
  @@index([offer_type])
  @@index([status])
}

model offer_attachments {
  id              String   @id @default(uuid())
  offer_id        String
  uploaded_by     String
  file_path       String
  file_type       String
  file_size       Int?
  description     String?
  is_public       Boolean  @default(false)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  is_deleted      Boolean  @default(false)

  // Relations
  offer           offers   @relation(fields: [offer_id], references: [id])
}

model offer_status_changes {
  id              String   @id @default(uuid())
  offer_id        String
  updated_by      String
  status          String   @default("Pending")
  previous_status String?
  reason          String?
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  // Relations
  offer           offers   @relation(fields: [offer_id], references: [id])
  updated_by_user users    @relation(fields: [updated_by], references: [id], name: "OfferStatusUpdator")
}

model offer_negotiations {
  id                     String   @id @default(uuid())
  offer_id               String
  sender_id              String
  recipient_id           String
  message                String?
  proposed_price         Float?
  proposed_delivery_time Int?
  status                 String   @default("Pending")
  created_at             DateTime @default(now())
  updated_at             DateTime @updatedAt

  // Relations
  offer                  offers   @relation(fields: [offer_id], references: [id])
  sender       users    @relation(name: "Sender", fields: [sender_id], references: [id])
  recipient    users    @relation(name: "Recipient", fields: [recipient_id], references: [id])
}




model offer_form_field_values {
  id                     String   @id @default(uuid())
  offer_id               String
  form_field_id          String
  field_value            String?
  created_at             DateTime @default(now())
  updated_at             DateTime @updatedAt

  // Relations
  offer                  offers   @relation(fields: [offer_id], references: [id], onDelete: Cascade)
  form_field             subcategory_form_fields @relation(fields: [form_field_id], references: [id], onDelete: Cascade)

  @@unique([offer_id, form_field_id])
  @@index([offer_id])
  @@index([form_field_id])
}

enum offer_type_enum {
  digital_product
  physical_product
  service
}


model orders {
  id              String   @id @default(uuid())
  offer_id        String
  buyer_id        String
  seller_id       String
  total_amount    Float
  payment_status  String   @default("pending")
  order_status    String   @default("pending")
  payment_method  String?
  tracking_code   String?
  delivery_date   DateTime?
  cancellation_reason String?
  refund_reason   String?
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  is_deleted      Boolean  @default(false)


  buyer         users    @relation(fields: [buyer_id], references: [id], name: "BuyerOrder")
  seller        users    @relation(fields: [seller_id], references: [id], name: "SellerOrder")
  offer        offers    @relation(fields: [offer_id], references: [id], name: "OfferOrder")



  order_items    order_items[]
  order_status_changes order_status_changes[]
  order_payments order_payments[]
  order_shipments order_shipments[]
  order_refunds order_refunds[]
}


model order_items {
  id              String   @id @default(uuid())
  order_id        String
  product_id      String?
  service_id      String?
  title           String
  quantity        Int      @default(1)
  price_per_unit  Float
  total_price     Float
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  order           orders   @relation(fields: [order_id], references: [id])
}


model order_status_changes {
  id              String   @id @default(uuid())
  order_id        String
  updated_by      String
  status          String   @default("pending")
  previous_status String?
  reason          String?
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  order           orders   @relation(fields: [order_id], references: [id])
  updated_by_user users    @relation(fields: [updated_by], references: [id], name: "OrderStatusUpdator")
}


model order_payments {
  id              String   @id @default(uuid())
  order_id        String
  buyer_id        String
  amount          Float
  payment_method  String? 
  transaction_id  String?
  payment_status  String   @default("pending")
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  order           orders   @relation(fields: [order_id], references: [id])
  buyer           users    @relation(fields: [buyer_id], references: [id], name: "BuyerOrderPayment")
}


model order_shipments {
  id              String   @id @default(uuid())
  order_id        String
  shipped_by      String?
  tracking_code   String?
  carrier         String?
  estimated_delivery DateTime?
  status          String   @default("pending")
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  order           orders   @relation(fields: [order_id], references: [id])
}


model order_refunds {
  id              String   @id @default(uuid())
  order_id        String
  buyer_id        String
  refund_amount   Float
  refund_status   String   @default("pending")
  reason          String?
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  order           orders   @relation(fields: [order_id], references: [id])
  buyer           users    @relation(fields: [buyer_id], references: [id], name: "BuyerOrderRefund")
}



model products {
  id              String         @id @default(uuid())
  name            String
  slug            String         @unique // SEO-friendly URL (e.g., "iphone-15-pro")
  description     String?
  price           Decimal        @default(0.00)
  discounted_price Decimal?      // For sales/discounts
  sku             String?        @unique // Stock Keeping Unit (e.g., "IPH15P-BLK-128")
  type            product_types  @default(PHYSICAL) // PHYSICAL/DIGITAL/SERVICE
  status          product_statuses  @default(DRAFT)    // DRAFT, PUBLISHED, ARCHIVED
  is_featured     Boolean        @default(false)

  // Relations

  seller          users         @relation(fields: [seller_id], references: [id], name: "SellerProduct")
  seller_id       String
  category        categories  @relation(fields: [category_id], references: [id], name: "ProductCategory")
  category_id     String
  sub_category    sub_categories?   @relation(fields: [sub_category_id], references: [id], name: "ProductSubCategory")
  sub_category_id String?

  // Metadata
  tags            String[]       // ["smartphone", "apple"]
  seo_title       String?
  seo_description String?
  seo_keywords    String?

  // Timestamps
  created_at      DateTime       @default(now())
  updated_at      DateTime       @updatedAt

  // Child Relations
  variants        product_variants[]
  images          product_images[]
  reviews         product_reviews[]
  inventory       inventories?      // Stock tracking (if not using variants)

  @@index([seller_id])
  @@index([category_id])
  @@index([sub_category_id])
  @@index([slug])
}

enum product_types {
  PHYSICAL
  DIGITAL
  SERVICE
}

enum product_statuses {
  DRAFT
  PUBLISHED
  ARCHIVED
}

model product_variants {
  id           String     @id @default(uuid())
  name         String     // e.g., "Color: Black"
  options      String[]   // ["Black", "White", "Blue"]
  product      products    @relation(fields: [product_id], references: [id])
  product_id   String
  price_offset Decimal?   // +$10 for this variant
  sku_suffix   String?    // "-BLK" appended to parent SKU

  @@index([product_id])
}

model product_images {
  id          String   @id @default(uuid())
  url         String
  alt_text    String?
  is_primary Boolean  @default(false)
  product    products  @relation(fields: [product_id], references: [id])
  product_id String

  @@index([product_id])
}


model inventories {
  id           String   @id @default(uuid())
  product      products  @relation(fields: [product_id], references: [id])
  product_id   String   @unique
  stock        Int      @default(0)
  low_stock_threshold Int @default(5)
  updated_at   DateTime @updatedAt
}

model product_reviews {
  id          String   @id @default(uuid())
  product     products  @relation(fields: [product_id], references: [id])
  product_id  String
  user_id     String   // Reference to User table
  rating      Int      @default(0)
  comment     String?
  created_at  DateTime @default(now())

  @@index([product_id])
  @@index([user_id])
}


model requests {
  id              String   @id @default(uuid())
  request_code    String?  @unique
  buyer_id        String
  category_id     String
  sub_category_id String
  title           String   @unique
  short_description     String?
  description     String?
  quantity        Int      @default(1)
  budget_min      Float?   @default(0)
  budget_max      Float?   @default(0)
  deadline        DateTime?
  urgency         String   @default("Normal")
  status          String   @default("Pending")
  request_type    String   @default("General")
  location        String?
  additional_info String?
  file            String?
  service_period  Int?
  session_count   Int?
  custom_fields   Json?    // Store custom form field values as JSON
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  is_deleted      Boolean  @default(false)

  // Relations
  buyer          users      @relation(fields: [buyer_id], references: [id], name: "Buyer")
  category       categories  @relation(fields: [category_id], references: [id], name: "RequestCategory")
  sub_category   sub_categories @relation(fields: [sub_category_id], references: [id], name: "RequestSubCategory")


  // Child relations
  request_attachments   request_attachments[]
  merged_requests         request_merged_items[] @relation("MergedItem")
  request_statuses         request_statuses[] @relation("RequestStatusUpdates")
  offers         offers[] @relation("RequestOffer")
  assigned_sellers      request_assigned_sellers[] @relation("RequestAssignedSeller")

}




model request_attachments {
  id              String  @id @default(uuid())
  request_id      String
  file_path       String
  file_type       String
  file_size       Int?
  description     String?
  is_public       Boolean @default(false)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  is_deleted      Boolean @default(false)

  // Relations
  request         requests  @relation(fields: [request_id], references: [id])
}

model request_statuses {
  id              String   @id @default(uuid())
  request_id      String
  updated_by      String   // User who changed the status
  status          String   @default("Pending")
  reason          String?  // Reason for status change (e.g., "Merged with another request")
  previous_status String?  // Stores the last status before update
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  // Relations
  request         requests  @relation(fields: [request_id], references: [id], name: "RequestStatusUpdates")
  updated_by_user users     @relation(fields: [updated_by], references: [id], name: "RequestUpdator")
}


model request_merged_items {
  id              String   @id @default(uuid())
  request_id      String
  merged_item_id  String
  merged_by       String
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  // Relations
  merged_item     requests  @relation(name: "MergedItem", fields: [merged_item_id], references: [id])
  merged_by_user   users     @relation(fields: [merged_by], references: [id], name: "MergedByAdmin")
}

model request_assigned_sellers {
  id          String   @id @default(uuid())
  request_id  String
  seller_id   String
  assigned_at DateTime @default(now())
  assigned_by String?  // User ID who assigned this seller
  status      String   @default("Pending") // e.g., "Pending", "Accepted", "Rejected"
  notes       String?

  // Relations
  request     requests @relation(fields: [request_id], references: [id], name: "RequestAssignedSeller")
  seller      users    @relation(fields: [seller_id], references: [id], name: "AssignedSeller")
  assigner    users?   @relation(fields: [assigned_by], references: [id], name: "SellerAssigner")

  @@unique([request_id, seller_id]) // Ensure a seller is only assigned once per request
}




model services {
  id               String          @id @default(uuid())
  title            String          // e.g., "Website Development"
  slug             String          @unique // "website-development"
  description      String?
  duration         Int?            // Minutes (e.g., 60 for 1-hour service)
  price_type       service_price_types @default(FIXED) // FIXED, HOURLY, VARIABLE
  base_price       Decimal         @default(0.00)
  min_price        Decimal?        // For VARIABLE pricing
  max_price        Decimal?        // For VARIABLE pricing
  is_available     Boolean         @default(true)
  status           service_statuses @default(DRAFT) // DRAFT, ACTIVE, PAUSED
  booking_type     booking_types   @default(INSTANT) // INSTANT, APPROVAL_REQUIRED

  // Relations

  provider         users           @relation(fields: [provider_id], references: [id], name: "SellerService")
  provider_id      String
  category         categories      @relation(fields: [category_id], references: [id], name: "ServiceCategory")
  category_id      String
  sub_category     sub_categories? @relation(fields: [sub_category_id], references: [id], name: "ServiceSubCategory")
  sub_category_id  String?

  // Metadata
  tags             String[]        // ["web-design", "seo"]
  faqs             service_faqs[]
  requirements     service_requirements[]
  seo_title        String?
  seo_description  String?

  // Timestamps
  created_at       DateTime        @default(now())
  updated_at       DateTime        @updatedAt

  // Child Relations
  service_addons   service_addons[]
  service_media    service_media[]
  availability     service_availabilities[]
  reviews          service_reviews[]

  @@index([provider_id])
  @@index([category_id])
  @@index([sub_category_id])
}

enum service_price_types {
  FIXED      // Single price (e.g., $100)
  HOURLY     // Price per hour (e.g., $50/hour)
  VARIABLE   // Price range (e.g., $50-$200)
}

enum service_statuses {
  DRAFT
  ACTIVE
  PAUSED
  DELETED
}

enum booking_types {
  INSTANT           // Book without approval
  APPROVAL_REQUIRED // Provider must confirm
}

model service_addons {
  id           String   @id @default(uuid())
  name         String   // e.g., "Extra Fast Delivery"
  description  String?
  price        Decimal  @default(0.00)
  duration     Int?     // Additional minutes (e.g., +30 mins)
  service      services @relation(fields: [service_id], references: [id])
  service_id   String

  @@index([service_id])
}

model service_media {
  id          String   @id @default(uuid())
  url         String
  type        media_types @default(IMAGE) // IMAGE, VIDEO
  alt_text    String?
  is_primary  Boolean  @default(false)
  service     services @relation(fields: [service_id], references: [id])
  service_id  String

  @@index([service_id])
}

enum media_types {
  IMAGE
  VIDEO
}

model service_availabilities {
  id           String       @id @default(uuid())
  service      services     @relation(fields: [service_id], references: [id])
  service_id   String
  day_of_week  day_of_weeks // MONDAY, TUESDAY, etc.
  start_time   DateTime     // 09:00
  end_time     DateTime     // 17:00
  is_recurring Boolean      @default(true)

  @@index([service_id])
}

enum day_of_weeks {
  MONDAY
  TUESDAY
  WEDNESDAY
  THURSDAY
  FRIDAY
  SATURDAY
  SUNDAY
}

model service_requirements {
  id           String            @id @default(uuid())
  service      services          @relation(fields: [service_id], references: [id])
  service_id   String
  question     String            // e.g., "Do you have a domain name?"
  type         requirement_types @default(TEXT) // TEXT, FILE, CHOICE
  is_required  Boolean           @default(false)
  options      String[]    @default([])

  @@index([service_id])
}

enum requirement_types {
  TEXT
  FILE
  CHOICE
}

model service_reviews {
  id          String     @id @default(uuid())
  service     services   @relation(fields: [service_id], references: [id])
  service_id  String
  user_id     String     // Reference to User table
  rating      Int
  comment     String?
  created_at  DateTime   @default(now())

  @@index([service_id])
  @@index([user_id])
}

model service_faqs {
  id          String   @id @default(uuid())
  service     services @relation(fields: [service_id], references: [id])
  service_id  String
  question    String
  answer      String

  @@index([service_id])
}


model sub_categories {
  id            String     @id @default(uuid())
  category_id   String     @db.VarChar(50)
  title         String     
  description   String?
  color         String?    // Hex color code for subcategory
  image         String?    // URL to main subcategory image
  thumbnail     String?    // URL to thumbnail image
  created_at    DateTime   @default(now())
  updated_at    DateTime   @updatedAt
  is_deleted    Boolean    @default(false)
  is_featured   Boolean    @default(false)
  sort_order    Int?       // For custom sorting within parent category

  // Relations
  category      categories @relation(fields: [category_id], references: [id], name: "Category")
  requests      requests[] @relation("RequestSubCategory")
  offers        offers[] @relation("OfferSubCategory")
  products      products[] @relation("ProductSubCategory")
  services      services[] @relation("ServiceSubCategory")
  translations  sub_category_translations[]
  form_fields   subcategory_form_fields[]

  // Metadata
  seo_title       String?
  seo_description String?
  seo_keywords    String?

  // Indexes for better performance
  @@index([category_id])
  @@index([sort_order])
}

model sub_category_translations {
  id              String   @id @default(uuid())
  sub_category_id String
  language        String   // e.g., 'en', 'bn', 'fr'
  title           String
  description     String?
  seo_title       String?
  seo_description String?
  seo_keywords    String?

  // Relations
  sub_category    sub_categories @relation(fields: [sub_category_id], references: [id])

  @@unique([sub_category_id, language]) // Prevent duplicate translations
}

model subcategory_form_fields {
  id              String         @id @default(uuid())
  subcategory_id  String
  label_name      String
  input_type      form_field_type @default(TEXT)
  label_subtitle  String?
  placeholder     String?
  is_required     Boolean        @default(false)
  options         String[]       @default([]) // For dropdown, radio, checkbox types
  default_value   String?
  validation_regex String?
  min_value       Int?
  max_value       Int?
  min_length      Int?
  max_length      Int?
  sort_order      Int            @default(0)
  created_at      DateTime       @default(now())
  updated_at      DateTime       @updatedAt

  // Relations
  subcategory     sub_categories @relation(fields: [subcategory_id], references: [id], onDelete: Cascade)
  offer_form_field_values offer_form_field_values[]

  @@index([subcategory_id])
  @@index([sort_order])
}

enum form_field_type {
  TEXT
  TEXTAREA
  NUMBER
  EMAIL
  PASSWORD
  DATE
  TIME
  DATETIME
  CHECKBOX
  RADIO
  SELECT
  MULTISELECT
  FILE
  PHONE
  URL
  COLOR
  RANGE
}


model subscriptions {
  id                    String                @id @default(uuid())
  name                  String                @unique
  description           String?
  price                 Decimal               @default(0.00)
  duration_days         Int                   @default(30) // 30 days = 1 month
  max_requests          Int?                  // null = unlimited
  max_offers            Int?                  // null = unlimited
  max_orders            Int?                  // null = unlimited
  is_active             Boolean               @default(true)
  is_featured           Boolean               @default(false)
  user_type             subscription_user_types @default(BOTH) // BUYER, SELLER, BOTH
  features              Json?                 // Additional features as JSON
  created_at            DateTime              @default(now())
  updated_at            DateTime              @updatedAt
  user_subscriptions    user_subscriptions[]

  @@index([user_type])
  @@index([is_active])
}

model user_subscriptions {
  id                    String                @id @default(uuid())
  user_id               String
  subscription_id       String
  status                subscription_statuses @default(ACTIVE)
  start_date            DateTime              @default(now())
  end_date              DateTime
  auto_renew            Boolean               @default(false)
  payment_method        String?
  transaction_id        String?
  created_at            DateTime              @default(now())
  updated_at            DateTime              @updatedAt
  user                  users                 @relation(fields: [user_id], references: [id], onDelete: Cascade)
  subscription          subscriptions         @relation(fields: [subscription_id], references: [id], onDelete: Cascade)
  usage_records         subscription_usage[]

  @@index([user_id])
  @@index([subscription_id])
  @@index([status])
  @@index([end_date])
}

model subscription_usage {
  id                    String              @id @default(uuid())
  user_subscription_id  String
  usage_type            usage_types         @default(REQUEST)
  count                 Int                 @default(0)
  month                 Int                 // 1-12
  year                  Int
  created_at            DateTime            @default(now())
  updated_at            DateTime            @updatedAt
  user_subscription     user_subscriptions  @relation(fields: [user_subscription_id], references: [id], onDelete: Cascade)

  @@unique([user_subscription_id, usage_type, month, year])
  @@index([user_subscription_id])
  @@index([month, year])
}

enum subscription_user_types {
  BUYER
  SELLER
  BOTH
}

enum subscription_statuses {
  ACTIVE
  EXPIRED
  CANCELLED
  SUSPENDED
}

enum usage_types {
  REQUEST
  OFFER
  ORDER
}


model users {
  id                         String   @id @default(uuid())
  username                   String?  @unique
  first_name                 String?
  last_name                  String?
  father_name                String?  @default("")
  mother_name                String?  @default("")
  date_of_birth              DateTime?
  gender                     String?  @default("")

  // Address fields (detailed)
  address                    String?  @default("")
  street                     String?
  city                       String?
  state                      String?
  zip                        String?
  country                    String?

  phone_number               String   @unique
  email                      String   @unique
  business_name               String?
  is_email_verified          Boolean  @default(false)
  email_verification_token   String?
  email_verification_expires DateTime?
  national_id_number         String?  @unique
  passport_number            String?  @unique
  profile_picture_url        String?
  password_hash              String
  refresh_token              String?
  is_approved                Boolean  @default(false)
  status                     String   @default("active")
  is_deleted                 Boolean  @default(false)

  // New profile fields
  age_confirm                Boolean? @default(false)
  user_type                  String?  // 'business' or 'personal'
  occupation                 String?
  interests                  String?
  bio                        String?

  // Business-specific fields
  business_type              String?
  business_website           String?
  business_registration_number String?
  business_address           String?
  tax_id                     String?
  business_document_url      String?

  // Document uploads
  nid_document_url           String?

  // Profile completion tracking
  is_completed_profile       Boolean? @default(false)

  created_at                 DateTime @default(now())
  updated_at                 DateTime @updatedAt
  last_login_at              DateTime?
  password_reset_token       String?
  password_reset_expires_at  DateTime?

  // Relationships
  roles          user_roles[]
  permissions    user_permissions[]

  sent_offers         offer_negotiations[] @relation("Sender")
  received_offers     offer_negotiations[] @relation("Recipient")

  seller_offers       offers[] @relation("Seller")
  requests           requests[] @relation("Buyer")
  merged_requests    request_merged_items[] @relation("MergedByAdmin")
  request_status_updator request_statuses[] @relation("RequestUpdator")
  order_refunds      order_refunds[] @relation("BuyerOrderRefund")
  order_payments     order_payments[] @relation("BuyerOrderPayment")
  order_status_changes order_status_changes[] @relation("OrderStatusUpdator")
  offer_status_changes offer_status_changes[] @relation("OfferStatusUpdator")
  buyer_orders       orders[] @relation("BuyerOrder")
  seller_orders      orders[] @relation("SellerOrder")
  seller_products      products[] @relation("SellerProduct")
  seller_services      services[] @relation("SellerService")

  assigned_requests request_assigned_sellers[] @relation("AssignedSeller")
  seller_assignments request_assigned_sellers[] @relation("SellerAssigner")

  buyer_cart        carts[] @relation("BuyerCart")
  business_informations business_informations[]
  user_subscriptions user_subscriptions[]

}



model roles {
  id         String   @id @default(uuid())
  name       String   @unique
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // Relationships
  users        user_roles[]
  permissions  role_permissions[]
}

model permissions {
  id         String   @id @default(uuid())
  name       String   @unique
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // Relationships
  users user_permissions[]
  roles role_permissions[]
}

model user_roles {
  id       String  @id @default(uuid())
  user_id  String
  role_id  String

  user     users   @relation(fields: [user_id], references: [id], onDelete: Cascade)
  role     roles   @relation(fields: [role_id], references: [id], onDelete: Cascade)

  @@unique([user_id, role_id])
}

model role_permissions {
  id          String  @id @default(uuid())
  role_id     String
  permission_id String

  role        roles        @relation(fields: [role_id], references: [id], onDelete: Cascade)
  permission  permissions  @relation(fields: [permission_id], references: [id], onDelete: Cascade)

  @@unique([role_id, permission_id])
}

model user_permissions {
  id            String  @id @default(uuid())
  user_id       String
  permission_id String

  user         users         @relation(fields: [user_id], references: [id], onDelete: Cascade)
  permission   permissions   @relation(fields: [permission_id], references: [id], onDelete: Cascade)

  @@unique([user_id, permission_id])
}

