const { body, param, query } = require('express-validator');
const CategoryModel = require('../models/categoryModel');
const createCategoryValidation = [
  body('title')
    .notEmpty().withMessage('Title is required')
    .isString().withMessage('Title must be a string')
    .trim()
    .isLength({ max: 100 }).withMessage('Title cannot exceed 100 characters')
    .custom(async (value, { req }) => {
      const existingCategory = await CategoryModel.findCategoryByTitle(value);
      if (existingCategory && existingCategory.id) {
        throw new Error('Category with this title already exists');
      }
      return true;
    }),


  body('description')
    .optional()
    .isString().withMessage('Description must be a string')
    .trim()
    .isLength({ max: 500 }).withMessage('Description cannot exceed 500 characters'),

  body('color')
    .optional()
    .isHexColor().withMessage('Invalid hex color code'),

  body('image')
    .optional()
    .custom((value, { req }) => {
      if (!req.file) {
        throw new Error('Image is required');
      }
      return true;
    }),

  body('thumbnail')
    .optional()
    .custom((value, { req }) => {
      if (!req.file) {
        throw new Error('Thumbnail is required');
      }
      return true;
    }),

  body('is_featured')
    .optional()
    .isBoolean().withMessage('is_featured must be a boolean'),

  body('is_premium')
    .optional()
    .isBoolean().withMessage('is_premium must be a boolean'),

  body('sort_order')
    .optional()
    .isInt({ min: 1 }).withMessage('sort_order must be a positive integer'),

  body('translations')
    .optional()
    .isArray().withMessage('Translations must be an array'),

  body('translations.*.language')
    .if(body('translations').exists())
    .notEmpty().withMessage('Language code is required')
    .isLength({ min: 2, max: 5 }).withMessage('Language code must be 2-5 characters'),

  body('translations.*.title')
    .if(body('translations').exists())
    .notEmpty().withMessage('Translation title is required')
    .isString().withMessage('Translation title must be a string')
];

const categoryValidations = {
  // Create category validation
  createCategory: createCategoryValidation,

  // Update category validation
  updateCategory: [
    param('id')
      .isUUID().withMessage('Invalid category ID'),

    body('title')
      .optional()
      .isString().withMessage('Title must be a string')
      .trim()
      .isLength({ max: 100 }).withMessage('Title cannot exceed 100 characters')
      .custom(async (value, { req }) => {
        const existingCategory = await CategoryModel.findCategoryByTitle(value);
        if (existingCategory && existingCategory.id !== req.params.id) {
          throw new Error('Category with this title already exists');
        }
        return true;
      }),

    body('description')
      .optional()
      .isString().withMessage('Description must be a string')
      .trim()
      .isLength({ max: 500 }).withMessage('Description cannot exceed 500 characters'),

    body('color')
      .optional()
      .isHexColor().withMessage('Invalid hex color code'),

    body('image')
      .optional()
      .custom((value, { req }) => {
        if (value !== undefined && !req.file) {
          throw new Error('Image file is required when updating image');
        }
        return true;
      }),

    body('thumbnail')
      .optional()
      .custom((value, { req }) => {
        if (value !== undefined && !req.file) {
          throw new Error('Thumbnail file is required when updating thumbnail');
        }
        return true;
      }),

    body('is_featured')
      .optional()
      .isBoolean().withMessage('is_featured must be a boolean'),
      
    body('is_active')
      .optional()
      .isBoolean().withMessage('is_active must be a boolean'),

    body('is_premium')
      .optional()
      .isBoolean().withMessage('is_premium must be a boolean'),

    body('sort_order')
      .optional()
      .isInt({ min: 1 }).withMessage('sort_order must be a positive integer'),

    body('translations')
      .optional()
      .isArray().withMessage('Translations must be an array'),

    body('translations.*.language')
      .if(body('translations').exists())
      .notEmpty().withMessage('Language code is required')
      .isLength({ min: 2, max: 5 }).withMessage('Language code must be 2-5 characters'),

    body('translations.*.title')
      .if(body('translations').exists())
      .notEmpty().withMessage('Translation title is required')
      .isString().withMessage('Translation title must be a string')
  ],


  // ID parameter validation
  idParam: [
    param('id')
      .isUUID().withMessage('Invalid category ID')
  ],

  // Search validation
  searchCategories: [
    query('name')
      .notEmpty().withMessage('Search query is required')
      .isString().withMessage('Search query must be a string')
      .trim()
      .isLength({ min: 2 }).withMessage('Search query must be at least 2 characters')
  ],

  // Get with subcategories validation
  getWithSubcategories: [
    param('id')
      .isUUID().withMessage('Invalid category ID')
  ],

  filterCategories: [
    query('featured')
      .optional()
      .isBoolean()
      .toBoolean()
      .withMessage('Featured must be a boolean'),

    query('premium')
      .optional()
      .isBoolean()
      .toBoolean()
      .withMessage('Premium must be a boolean'),

    query('search')
      .optional()
      .isString()
      .withMessage('Search must be a string'),

    query('sort_by')
      .optional()
      .isString()
      .withMessage('Sort by must be a string'),

    query('sort_order')
      .optional()
      .isIn(['asc', 'desc'])
      .withMessage('Sort order must be either asc or desc'),

    query('include_deleted')
      .optional()
      .isBoolean()
      .toBoolean()
      .withMessage('Include deleted must be a boolean'),

    // Pagination parameters
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),

    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be a positive integer between 1 and 100')
  ]
};

module.exports = categoryValidations;
