const { body, param, query } = require('express-validator');

const createBusinessInformationValidation = [
  body('company_name')
    .notEmpty()
    .withMessage('Company name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Company name must be between 2 and 100 characters'),

  body('short_description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Short description must not exceed 500 characters'),

  body('long_description')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('Long description must not exceed 2000 characters'),

  body('logo_url')
    .optional()
    .custom((value, { req }) => {
      // Skip validation if file is uploaded
      if (req.files && req.files.logo) {
        return true;
      }
      // Validate URL if provided as string
      if (value && typeof value === 'string') {
        const urlPattern = /^https?:\/\/.+/;
        if (!urlPattern.test(value)) {
          throw new Error('Logo URL must be a valid URL');
        }
      }
      return true;
    }),

  body('banner_url')
    .optional()
    .custom((value, { req }) => {
      // Skip validation if file is uploaded
      if (req.files && req.files.banner) {
        return true;
      }
      // Validate URL if provided as string
      if (value && typeof value === 'string') {
        const urlPattern = /^https?:\/\/.+/;
        if (!urlPattern.test(value)) {
          throw new Error('Banner URL must be a valid URL');
        }
      }
      return true;
    }),

  body('address')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Address must not exceed 255 characters'),

  body('city')
    .optional()
    .isLength({ max: 100 })
    .withMessage('City must not exceed 100 characters'),

  body('state')
    .optional()
    .isLength({ max: 100 })
    .withMessage('State must not exceed 100 characters'),

  body('country')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Country must not exceed 100 characters'),

  body('postal_code')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Postal code must not exceed 20 characters'),

  body('phone_number')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),

  body('email')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid email address'),

  body('website_url')
    .optional()
    .isURL()
    .withMessage('Website URL must be a valid URL'),

  body('business_type')
    .optional()
    .isIn(['Retail', 'Wholesale', 'Service', 'Manufacturing', 'Technology', 'Healthcare', 'Education', 'Other'])
    .withMessage('Invalid business type'),

  body('business_category')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Business category must not exceed 100 characters'),

  body('established_year')
    .optional()
    .isInt({ min: 1800, max: new Date().getFullYear() })
    .withMessage('Established year must be a valid year'),

  body('employee_count')
    .optional()
    .isIn(['1-10', '11-50', '51-200', '201-500', '500+'])
    .withMessage('Invalid employee count range'),

  body('annual_revenue')
    .optional()
    .isIn(['Under $100K', '$100K-$1M', '$1M-$10M', '$10M-$100M', '$100M+'])
    .withMessage('Invalid annual revenue range'),

  body('business_license')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Business license must not exceed 100 characters'),

  body('tax_id')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Tax ID must not exceed 50 characters'),

  body('social_media_links')
    .optional()
    .custom((value) => {
      if (typeof value === 'string') {
        try {
          JSON.parse(value);
          return true;
        } catch (e) {
          throw new Error('Social media links must be a valid JSON object');
        }
      }
      if (typeof value === 'object') {
        return true;
      }
      throw new Error('Social media links must be an object or valid JSON string');
    }),

  body('operating_hours')
    .optional()
    .custom((value) => {
      if (typeof value === 'string') {
        try {
          JSON.parse(value);
          return true;
        } catch (e) {
          throw new Error('Operating hours must be a valid JSON object');
        }
      }
      if (typeof value === 'object') {
        return true;
      }
      throw new Error('Operating hours must be an object or valid JSON string');
    }),

  body('services_offered')
    .optional()
    .custom((value) => {
      if (typeof value === 'string') {
        try {
          const parsed = JSON.parse(value);
          if (!Array.isArray(parsed)) {
            throw new Error('Services offered must be an array');
          }
          return true;
        } catch (e) {
          throw new Error('Services offered must be a valid JSON array');
        }
      }
      if (Array.isArray(value)) {
        return true;
      }
      throw new Error('Services offered must be an array or valid JSON array string');
    }),

  body('certifications')
    .optional()
    .custom((value) => {
      if (typeof value === 'string') {
        try {
          const parsed = JSON.parse(value);
          if (!Array.isArray(parsed)) {
            throw new Error('Certifications must be an array');
          }
          return true;
        } catch (e) {
          throw new Error('Certifications must be a valid JSON array');
        }
      }
      if (Array.isArray(value)) {
        return true;
      }
      throw new Error('Certifications must be an array or valid JSON array string');
    }),

  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean')
];

const updateBusinessInformationValidation = [
  param('id')
    .isUUID()
    .withMessage('Invalid business information ID'),

  body('company_name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Company name must be between 2 and 100 characters'),

  body('short_description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Short description must not exceed 500 characters'),

  body('long_description')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('Long description must not exceed 2000 characters'),

  body('logo_url')
    .optional()
    .custom((value, { req }) => {
      // Skip validation if file is uploaded
      if (req.files && req.files.logo) {
        return true;
      }
      // Validate URL if provided as string
      if (value && typeof value === 'string') {
        const urlPattern = /^https?:\/\/.+/;
        if (!urlPattern.test(value)) {
          throw new Error('Logo URL must be a valid URL');
        }
      }
      return true;
    }),

  body('banner_url')
    .optional()
    .custom((value, { req }) => {
      // Skip validation if file is uploaded
      if (req.files && req.files.banner) {
        return true;
      }
      // Validate URL if provided as string
      if (value && typeof value === 'string') {
        const urlPattern = /^https?:\/\/.+/;
        if (!urlPattern.test(value)) {
          throw new Error('Banner URL must be a valid URL');
        }
      }
      return true;
    }),

  body('address')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Address must not exceed 255 characters'),

  body('city')
    .optional()
    .isLength({ max: 100 })
    .withMessage('City must not exceed 100 characters'),

  body('state')
    .optional()
    .isLength({ max: 100 })
    .withMessage('State must not exceed 100 characters'),

  body('country')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Country must not exceed 100 characters'),

  body('postal_code')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Postal code must not exceed 20 characters'),

  body('phone_number')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),

  body('email')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid email address'),

  body('website_url')
    .optional()
    .isURL()
    .withMessage('Website URL must be a valid URL'),

  body('business_type')
    .optional()
    .isIn(['Retail', 'Wholesale', 'Service', 'Manufacturing', 'Technology', 'Healthcare', 'Education', 'Other'])
    .withMessage('Invalid business type'),

  body('business_category')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Business category must not exceed 100 characters'),

  body('established_year')
    .optional()
    .isInt({ min: 1800, max: new Date().getFullYear() })
    .withMessage('Established year must be a valid year'),

  body('employee_count')
    .optional()
    .isIn(['1-10', '11-50', '51-200', '201-500', '500+'])
    .withMessage('Invalid employee count range'),

  body('annual_revenue')
    .optional()
    .isIn(['Under $100K', '$100K-$1M', '$1M-$10M', '$10M-$100M', '$100M+'])
    .withMessage('Invalid annual revenue range'),

  body('business_license')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Business license must not exceed 100 characters'),

  body('tax_id')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Tax ID must not exceed 50 characters'),

  body('social_media_links')
    .optional()
    .isObject()
    .withMessage('Social media links must be an object'),

  body('operating_hours')
    .optional()
    .isObject()
    .withMessage('Operating hours must be an object'),

  body('services_offered')
    .optional()
    .isArray()
    .withMessage('Services offered must be an array'),

  body('certifications')
    .optional()
    .isArray()
    .withMessage('Certifications must be an array'),

  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean')
];

const getBusinessInformationValidation = [
  param('id')
    .isUUID()
    .withMessage('Invalid business information ID')
];

const getBusinessInformationsValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),

  query('search')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Search term must not exceed 100 characters'),

  query('business_type')
    .optional()
    .isIn(['Retail', 'Wholesale', 'Service', 'Manufacturing', 'Technology', 'Healthcare', 'Education', 'Other'])
    .withMessage('Invalid business type'),

  query('verification_status')
    .optional()
    .isIn(['pending', 'verified', 'rejected'])
    .withMessage('Invalid verification status'),

  query('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean')
];

const updateVerificationStatusValidation = [
  param('id')
    .isUUID()
    .withMessage('Invalid business information ID'),

  body('status')
    .notEmpty()
    .withMessage('Status is required')
    .isIn(['pending', 'verified', 'rejected'])
    .withMessage('Invalid verification status')
];

module.exports = {
  createBusinessInformationValidation,
  updateBusinessInformationValidation,
  getBusinessInformationValidation,
  getBusinessInformationsValidation,
  updateVerificationStatusValidation
};
