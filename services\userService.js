const User = require('../models/userModel');

// Service to get all users
exports.getAllUsers = async () => {
    return await User.find();
};

// Service to get a user by ID
exports.getUserById = async (id) => {
    return await User.findById(id);
};

// Service to create a new user
exports.createUser = async (userData) => {
    const user = new User(userData);
    return await user.save();
};

// Service to update a user by ID
exports.updateUser = async (id, userData) => {
    return await User.findByIdAndUpdate(id, userData, { new: true, runValidators: true });
};

// Service to partially update a user by ID
exports.partialUpdateUser = async (id, userData) => {
    return await User.findByIdAndUpdate(id, userData, { new: true, runValidators: true });
};

// Service to delete a user by ID
exports.deleteUser = async (id) => {
    return await User.findByIdAndDelete(id);
};

// Service to search for users by query parameters
exports.searchUsers = async (query) => {
    return await User.find(query);
};

// Service to get the profile of the logged-in user with roles
exports.getUserProfile = async (userId) => {
    return await User.findById(userId);
};

// Service to get the profile of the logged-in user with roles
exports.getUserProfileWithRoles = async (userId) => {
    return await User.findUserByIdWithRoles(userId);
};
