const { body } = require("express-validator");
const UserModel = require("../models/userModel");

const registerValidation = [
  body("first_name").notEmpty().withMessage("First name is required"),
  body("last_name").notEmpty().withMessage("Last name is required"),
  body("email")
    .isEmail()
    .withMessage("Invalid email address")
    .custom(async (value) => {
      const existingUser = await UserModel.findUserByEmail(value);
      if (existingUser) {
        throw new Error("Email is already in use.");
      }
    }),
  body("phone_number")
    .isMobilePhone()
    .withMessage("The phone number is invalid.")
    .notEmpty()
    .withMessage("The phone number field is required.")
    .custom(async (value, { req }) => {
      const existingUser = await UserModel.findUserByPhone(value);
      if (existingUser) {
        throw new Error("The phone number has already been taken.");
      }
      return true;
  }),
  body("password")
    .isLength({ min: 8 })
    .withMessage("Password must be at least 8 characters long")
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])[A-Za-z\d!@#$%^&*]/)
    .withMessage("Password must contain at least 1 lowercase letter, 1 uppercase letter, 1 number (0-9), and 1 special character (!@#$%^&*)"),
  body("business_name")
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage("Business name must be between 2 and 100 characters"),
];

module.exports = { registerValidation };
