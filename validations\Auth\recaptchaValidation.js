/**
 * reCAPTCHA validation middleware
 */
const { body } = require('express-validator');
const RecaptchaVerifier = require('../../utils/recaptchaVerifier');

const recaptchaValidation = [
  body('recaptcha_token')
    .notEmpty()
    .withMessage('reCAPTCHA token is required')
    .isString()
    .withMessage('reCAPTCHA token must be a string')
    .isLength({ min: 10 })
    .withMessage('Invalid reCAPTCHA token format')
];

/**
 * Middleware to verify reCAPTCHA token
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const verifyRecaptcha = async (req, res, next) => {
  try {
    console.log('=== reCAPTCHA MIDDLEWARE DEBUG ===');
    console.log('Request body in middleware:', req.body);
    console.log('NODE_ENV:', process.env.NODE_ENV);
    console.log('RECAPTCHA_DISABLED:', process.env.RECAPTCHA_DISABLED);

    const { recaptcha_token } = req.body;
    console.log('Extracted recaptcha_token:', recaptcha_token ? '[PRESENT]' : '[MISSING]');

    // Skip reCAPTCHA verification in test environment
    if (process.env.NODE_ENV === 'test') {
      console.log('Skipping reCAPTCHA - test environment');
      return next();
    }

    // Skip reCAPTCHA verification if disabled in environment
    if (process.env.RECAPTCHA_DISABLED === 'true') {
      console.log('Skipping reCAPTCHA - disabled in environment');
      return next();
    }

    if (!recaptcha_token) {
      console.log('reCAPTCHA token missing - returning error');
      return res.status(400).json({
        success: false,
        message: 'reCAPTCHA token is required',
        errors: {
          recaptcha_token: ['reCAPTCHA token is required']
        }
      });
    }

    // Get user's IP address
    const userIP = RecaptchaVerifier.getUserIP(req);

    // Verify reCAPTCHA token
    const verificationResult = await RecaptchaVerifier.verifyToken(recaptcha_token, userIP);

    if (!verificationResult.success) {
      return res.status(400).json({
        success: false,
        message: 'reCAPTCHA verification failed',
        errors: {
          recaptcha_token: [verificationResult.error || 'reCAPTCHA verification failed']
        }
      });
    }

    // Store verification result in request for potential logging
    req.recaptchaResult = verificationResult;

    next();
  } catch (error) {
    console.error('reCAPTCHA verification middleware error:', error);
    return res.status(500).json({
      success: false,
      message: 'reCAPTCHA verification service error',
      errors: {
        recaptcha_token: ['reCAPTCHA verification service temporarily unavailable']
      }
    });
  }
};

/**
 * Middleware to verify reCAPTCHA token with score (for reCAPTCHA v3)
 * @param {number} minScore - Minimum score required (0.0 to 1.0)
 * @param {string} expectedAction - Expected action name (optional)
 * @returns {Function} Express middleware function
 */
const verifyRecaptchaWithScore = (minScore = 0.5, expectedAction = 'login') => {
  return async (req, res, next) => {
    try {
      const { recaptcha_token } = req.body;

      // Skip reCAPTCHA verification in test environment
      if (process.env.NODE_ENV === 'test') {
        return next();
      }

      // Skip reCAPTCHA verification if disabled in environment
      if (process.env.RECAPTCHA_DISABLED === 'true') {
        return next();
      }

      if (!recaptcha_token) {
        return res.status(400).json({
          success: false,
          message: 'reCAPTCHA token is required',
          errors: {
            recaptcha_token: ['reCAPTCHA token is required']
          }
        });
      }

      // Get user's IP address
      const userIP = RecaptchaVerifier.getUserIP(req);

      // Verify reCAPTCHA token with score
      const verificationResult = await RecaptchaVerifier.verifyTokenWithScore(
        recaptcha_token,
        minScore,
        expectedAction,
        userIP
      );

      if (!verificationResult.success) {
        return res.status(400).json({
          success: false,
          message: 'reCAPTCHA verification failed',
          errors: {
            recaptcha_token: [verificationResult.error || 'reCAPTCHA verification failed']
          }
        });
      }

      // Store verification result in request for potential logging
      req.recaptchaResult = verificationResult;

      next();
    } catch (error) {
      console.error('reCAPTCHA verification middleware error:', error);
      return res.status(500).json({
        success: false,
        message: 'reCAPTCHA verification service error',
        errors: {
          recaptcha_token: ['reCAPTCHA verification service temporarily unavailable']
        }
      });
    }
  };
};

module.exports = {
  recaptchaValidation,
  verifyRecaptcha,
  verifyRecaptchaWithScore
};
