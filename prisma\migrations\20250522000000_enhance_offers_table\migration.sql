-- Create enum if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'offer_type_enum') THEN
        CREATE TYPE "offer_type_enum" AS ENUM ('digital_product', 'physical_product', 'service');
    END IF;
END $$;

-- Add columns to offers table if they don't exist
DO $$
BEGIN
    -- Add offer_title column
    IF NOT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_name = 'offers' AND column_name = 'offer_title'
    ) THEN
        ALTER TABLE "offers" ADD COLUMN "offer_title" TEXT;
    END IF;

    -- Add category_id column
    IF NOT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_name = 'offers' AND column_name = 'category_id'
    ) THEN
        ALTER TABLE "offers" ADD COLUMN "category_id" TEXT;
    END IF;

    -- Add subcategory_id column
    IF NOT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_name = 'offers' AND column_name = 'subcategory_id'
    ) THEN
        ALTER TABLE "offers" ADD COLUMN "subcategory_id" TEXT;
    END IF;

    -- Add short_description column
    IF NOT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_name = 'offers' AND column_name = 'short_description'
    ) THEN
        ALTER TABLE "offers" ADD COLUMN "short_description" TEXT;
    END IF;

    -- Add discount column
    IF NOT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_name = 'offers' AND column_name = 'discount'
    ) THEN
        ALTER TABLE "offers" ADD COLUMN "discount" DOUBLE PRECISION;
    END IF;

    -- Add quantity column
    IF NOT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_name = 'offers' AND column_name = 'quantity'
    ) THEN
        ALTER TABLE "offers" ADD COLUMN "quantity" INTEGER NOT NULL DEFAULT 1;
    END IF;

    -- Add offer_type column
    IF NOT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_name = 'offers' AND column_name = 'offer_type'
    ) THEN
        ALTER TABLE "offers" ADD COLUMN "offer_type" "offer_type_enum" NOT NULL DEFAULT 'service';
    END IF;
END $$;

-- Make request_id nullable if it's not already
DO $$
BEGIN
    IF EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_name = 'offers'
        AND column_name = 'request_id'
        AND is_nullable = 'NO'
    ) THEN
        ALTER TABLE "offers" ALTER COLUMN "request_id" DROP NOT NULL;
    END IF;
END $$;

-- Create table if it doesn't exist
CREATE TABLE IF NOT EXISTS "offer_form_field_values" (
    "id" TEXT NOT NULL,
    "offer_id" TEXT NOT NULL,
    "form_field_id" TEXT NOT NULL,
    "field_value" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "offer_form_field_values_pkey" PRIMARY KEY ("id")
);

-- Create indexes if they don't exist
CREATE INDEX IF NOT EXISTS "offers_category_id_idx" ON "offers"("category_id");
CREATE INDEX IF NOT EXISTS "offers_subcategory_id_idx" ON "offers"("subcategory_id");
CREATE INDEX IF NOT EXISTS "offers_offer_type_idx" ON "offers"("offer_type");
CREATE INDEX IF NOT EXISTS "offers_status_idx" ON "offers"("status");
CREATE UNIQUE INDEX IF NOT EXISTS "offer_form_field_values_offer_id_form_field_id_key" ON "offer_form_field_values"("offer_id", "form_field_id");
CREATE INDEX IF NOT EXISTS "offer_form_field_values_offer_id_idx" ON "offer_form_field_values"("offer_id");
CREATE INDEX IF NOT EXISTS "offer_form_field_values_form_field_id_idx" ON "offer_form_field_values"("form_field_id");

-- Add foreign key constraints if they don't exist
DO $$
BEGIN
    -- Add offers_category_id_fkey constraint
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'offers_category_id_fkey'
        AND table_name = 'offers'
    ) THEN
        ALTER TABLE "offers" ADD CONSTRAINT "offers_category_id_fkey"
        FOREIGN KEY ("category_id") REFERENCES "categories"("id") ON DELETE SET NULL ON UPDATE CASCADE;
    END IF;

    -- Add offers_subcategory_id_fkey constraint
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'offers_subcategory_id_fkey'
        AND table_name = 'offers'
    ) THEN
        ALTER TABLE "offers" ADD CONSTRAINT "offers_subcategory_id_fkey"
        FOREIGN KEY ("subcategory_id") REFERENCES "sub_categories"("id") ON DELETE SET NULL ON UPDATE CASCADE;
    END IF;

    -- Add offer_form_field_values_offer_id_fkey constraint
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'offer_form_field_values_offer_id_fkey'
        AND table_name = 'offer_form_field_values'
    ) THEN
        ALTER TABLE "offer_form_field_values" ADD CONSTRAINT "offer_form_field_values_offer_id_fkey"
        FOREIGN KEY ("offer_id") REFERENCES "offers"("id") ON DELETE CASCADE ON UPDATE CASCADE;
    END IF;

    -- Add offer_form_field_values_form_field_id_fkey constraint
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'offer_form_field_values_form_field_id_fkey'
        AND table_name = 'offer_form_field_values'
    ) THEN
        ALTER TABLE "offer_form_field_values" ADD CONSTRAINT "offer_form_field_values_form_field_id_fkey"
        FOREIGN KEY ("form_field_id") REFERENCES "subcategory_form_fields"("id") ON DELETE CASCADE ON UPDATE CASCADE;
    END IF;
END $$;
