const SellerService = require('../services/sellerService');
const sendResponse = require('../utils/sendResponse');
const { validationResult } = require('express-validator');
const { formatValidationErrors } = require('../utils/validationFormatter');
const { saveBufferToFile } = require('../utils/fileUpload');

class SellerController {
  /**
   * Get assigned requests for a seller
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getAssignedRequests(req, res) {
    try {
      const sellerId = req.user.id;
      const { status } = req.query;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      const filters = {};
      if (status) {
        filters.status = status;
      }

      const result = await SellerService.getAssignedRequests(sellerId, filters, page, limit);

      return sendResponse(
        res,
        true,
        'Assigned requests retrieved successfully',
        result.data,
        null,
        result.meta,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Accept an assigned request
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async acceptAssignedRequest(req, res) {
    try {
      const sellerId = req.user.id;
      const { assignmentId } = req.params;

      const result = await SellerService.acceptAssignedRequest(assignmentId, sellerId);

      return sendResponse(
        res,
        true,
        'Request assignment accepted successfully',
        result,
        null,
        null,
        200
      );
    } catch (error) {
      let statusCode = error.statusCode || 500;

      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        statusCode
      );
    }
  }

  /**
   * Reject an assigned request
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async rejectAssignedRequest(req, res) {
    try {
      const sellerId = req.user.id;
      const { assignmentId } = req.params;
      const { reason } = req.body;

      const result = await SellerService.rejectAssignedRequest(assignmentId, sellerId, reason);

      return sendResponse(
        res,
        true,
        'Request assignment rejected successfully',
        result,
        null,
        null,
        200
      );
    } catch (error) {
      let statusCode = error.statusCode || 500;

      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        statusCode
      );
    }
  }

  /**
   * Create an offer for a request
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async createOffer(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const laravelStyleErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          laravelStyleErrors.message,
          null,
          laravelStyleErrors.errors,
          null,
          422
        );
      }

      const sellerId = req.user.id;
      const offerData = req.body;

      const result = await SellerService.createOffer(offerData, sellerId);

      return sendResponse(
        res,
        true,
        'Offer created successfully',
        result,
        null,
        null,
        201
      );
    } catch (error) {
      let statusCode = error.statusCode || 500;

      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        statusCode
      );
    }
  }

  /**
   * Get request details for a seller
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getRequestDetailsForSeller(req, res) {
    try {
      const sellerId = req.user.id;
      const { requestId } = req.params;

      const result = await SellerService.getRequestDetailsForSeller(requestId, sellerId);

      return sendResponse(
        res,
        true,
        'Request details retrieved successfully',
        result,
        null,
        null,
        200
      );
    } catch (error) {
      let statusCode = error.statusCode || 500;

      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        statusCode
      );
    }
  }

  /**
   * Get offers created by a seller
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getSellerOffers(req, res) {
    try {
      const sellerId = req.user.id;
      const { status, request_id } = req.query;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      const filters = {};
      if (status) {
        filters.status = status;
      }
      if (request_id) {
        filters.request_id = request_id;
      }

      const result = await SellerService.getSellerOffers(sellerId, filters, page, limit);

      return sendResponse(
        res,
        true,
        'Seller offers retrieved successfully',
        result.data,
        null,
        result.meta,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Update an offer
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async updateOffer(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const laravelStyleErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          laravelStyleErrors.message,
          null,
          laravelStyleErrors.errors,
          null,
          422
        );
      }

      const sellerId = req.user.id;
      const { offerId } = req.params;
      const offerData = req.body;

      const result = await SellerService.updateOffer(offerId, sellerId, offerData);

      return sendResponse(
        res,
        true,
        'Offer updated successfully',
        result,
        null,
        null,
        200
      );
    } catch (error) {
      let statusCode = error.statusCode || 500;

      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        statusCode
      );
    }
  }

  /**
   * Update seller profile
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async updateProfile(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const laravelStyleErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          laravelStyleErrors.message,
          null,
          laravelStyleErrors.errors,
          null,
          422
        );
      }

      const sellerId = req.user.id;
      let profileData = req.body;

      // Handle avatar upload if present
      if (req.file) {
        profileData.profile_picture_url = saveBufferToFile(
          req.file.buffer,
          req.file.originalname,
          'uploads/avatars'
        );
      }

      const result = await SellerService.updateProfile(sellerId, profileData);

      return sendResponse(
        res,
        true,
        'Profile updated successfully',
        result,
        null,
        null,
        200
      );
    } catch (error) {
      let statusCode = error.statusCode || 500;

      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        statusCode
      );
    }
  }
}

module.exports = SellerController;
