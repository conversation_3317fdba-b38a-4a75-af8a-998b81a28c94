# reCAPTCHA Setup Guide

This guide explains how to set up Google reCAPTCHA for the login endpoint.

## Environment Variables

Add the following environment variables to your `.env` file:

```env
# reCAPTCHA Configuration
RECAPTCHA_SECRET_KEY=your_recaptcha_secret_key_here
RECAPTCHA_DISABLED=false
```

### Environment Variable Descriptions

- `RECAPTCHA_SECRET_KEY`: Your Google reCAPTCHA secret key (required)
- `RECAPTCHA_DISABLED`: Set to `true` to disable reCAPTCHA verification (optional, defaults to `false`)

## Getting reCAPTCHA Keys

1. Go to [Google reCAPTCHA Admin Console](https://www.google.com/recaptcha/admin)
2. Click "Create" to add a new site
3. Choose reCAPTCHA type:
   - **reCAPTCHA v2**: Traditional checkbox "I'm not a robot"
   - **reCAPTCHA v3**: Invisible, score-based verification (recommended)
4. Add your domain(s)
5. Copy the **Site Key** (for frontend) and **Secret Key** (for backend)

## Frontend Integration

### For reCAPTCHA v2:
```html
<script src="https://www.google.com/recaptcha/api.js" async defer></script>
<div class="g-recaptcha" data-sitekey="YOUR_SITE_KEY"></div>
```

### For reCAPTCHA v3:
```html
<script src="https://www.google.com/recaptcha/api.js?render=YOUR_SITE_KEY"></script>
<script>
grecaptcha.ready(function() {
    grecaptcha.execute('YOUR_SITE_KEY', {action: 'login'}).then(function(token) {
        // Add token to your form
        document.getElementById('recaptcha_token').value = token;
    });
});
</script>
```

## API Usage

### Login Request Format

```json
{
  "email": "<EMAIL>",
  "password": "userpassword123",
  "recaptcha_token": "03AGdBq25SiWT2OlqTT4DPuoK7AdvIHEyeP..."
}
```

### Response Format

#### Success Response:
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "accessToken": "jwt_token_here",
    "refreshToken": "refresh_token_here",
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "roles": ["Buyer"]
    }
  }
}
```

#### reCAPTCHA Validation Error:
```json
{
  "success": false,
  "message": "reCAPTCHA verification failed",
  "errors": {
    "recaptcha_token": ["reCAPTCHA verification failed"]
  }
}
```

## Testing

### Disable reCAPTCHA for Testing
Set `RECAPTCHA_DISABLED=true` in your `.env` file to disable reCAPTCHA verification during testing.

### Test Environment
reCAPTCHA verification is automatically disabled when `NODE_ENV=test`.

## Security Considerations

1. **Never expose your secret key** in client-side code
2. **Always verify tokens server-side** - never trust client-side verification
3. **Set appropriate score thresholds** for reCAPTCHA v3 (0.5 is recommended)
4. **Monitor reCAPTCHA analytics** in the Google reCAPTCHA console
5. **Implement rate limiting** as an additional security layer

## Troubleshooting

### Common Issues:

1. **"reCAPTCHA secret key not configured"**
   - Ensure `RECAPTCHA_SECRET_KEY` is set in your `.env` file

2. **"reCAPTCHA verification timeout"**
   - Check your internet connection
   - Verify Google reCAPTCHA service is accessible

3. **"reCAPTCHA verification failed"**
   - Verify the site key matches your domain
   - Check if the token is expired (tokens expire after 2 minutes)
   - Ensure the secret key is correct

4. **"Invalid reCAPTCHA token format"**
   - Verify the token is being sent from the frontend
   - Check that the token is a valid string

### Debug Mode:
Enable debug logging by checking the server console for reCAPTCHA verification results.
