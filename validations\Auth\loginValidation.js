/**
 * Login validation with reCAPTCHA
 */
const { body } = require('express-validator');

const loginValidation = [
  body('email')
    .notEmpty()
    .withMessage('Email is required')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail()
    .custom((value, { req }) => {
      console.log('Email validation - value:', value, 'type:', typeof value);
      return true;
    }),

  body('password')
    .notEmpty()
    .withMessage('Password is required')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
    .custom((value, { req }) => {
      console.log('Password validation - present:', !!value, 'type:', typeof value);
      return true;
    }),

  // body('recaptcha_token')
  //   .notEmpty()
  //   .withMessage('reCAPTCHA verification is required')
  //   .isString()
  //   .withMessage('reCAPTCHA token must be a string')
  //   .isLength({ min: 10 })
  //   .withMessage('Invalid reCAPTCHA token format')
  //   .custom((value, { req }) => {
  //     console.log('reCAPTCHA validation - present:', !!value, 'type:', typeof value, 'length:', value ? value.length : 0);
  //     return true;
  //   })
];

module.exports = {
  loginValidation
};
