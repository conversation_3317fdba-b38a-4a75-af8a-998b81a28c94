const { body, validationResult } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

const createOfferValidation = [
  // Optional request_id (now optional)
  body('request_id')
    .optional()
    .isUUID().withMessage('The request ID must be a valid UUID.'),

  // Required fields
  body('price')
    .notEmpty().withMessage('The price field is required.')
    .isFloat({ min: 0.01 }).withMessage('The price must be greater than 0.'),

  body('delivery_time')
    .notEmpty().withMessage('The delivery time field is required.')
    .isInt({ min: 1 }).withMessage('The delivery time must be at least 1 day.'),

  // New optional fields
  body('offer_title')
    .optional()
    .isString().withMessage('The offer title must be a string.')
    .isLength({ max: 255 }).withMessage('The offer title may not be greater than 255 characters.'),

  body('category_id')
    .optional()
    .isUUID().withMessage('The category ID must be a valid UUID.'),

  body('subcategory_id')
    .optional()
    .isUUID().withMessage('The subcategory ID must be a valid UUID.'),

  body('short_description')
    .optional()
    .isString().withMessage('The short description must be a string.')
    .isLength({ max: 500 }).withMessage('The short description may not be greater than 500 characters.'),

  body('discount')
    .optional()
    .isFloat({ min: 0 }).withMessage('The discount must be 0 or greater.'),

  body('quantity')
    .optional()
    .isInt({ min: 1 }).withMessage('The quantity must be at least 1.'),

  body('offer_type')
    .optional()
    .isIn(['digital_product', 'physical_product', 'service']).withMessage('Invalid offer type.'),

  // Existing optional fields
  body('message')
    .optional()
    .isString().withMessage('The message must be a string.')
    .isLength({ max: 1000 }).withMessage('The message may not be greater than 1000 characters.'),

  body('description')
    .optional()
    .isString().withMessage('The description must be a string.')
    .isLength({ max: 5000 }).withMessage('The description may not be greater than 5000 characters.'),

  // Dynamic form fields validation
  body('form_fields')
    .optional()
    .isArray().withMessage('Form fields must be an array.'),

  body('form_fields.*.form_field_id')
    .optional()
    .isUUID().withMessage('Form field ID must be a valid UUID.'),

  body('form_fields.*.field_value')
    .optional()
    .isString().withMessage('Field value must be a string.'),
];

module.exports = createOfferValidation;
