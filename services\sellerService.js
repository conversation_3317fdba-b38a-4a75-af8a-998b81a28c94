const SellerModel = require('../models/sellerModel');
const ApiError = require('../utils/apiError');

class SellerService {
  /**
   * Get assigned requests for a seller
   * @param {string} sellerId - Seller ID
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated assigned requests
   */
  static async getAssignedRequests(sellerId, filters = {}, page = 1, limit = 10) {
    try {
      return await SellerModel.getAssignedRequests(sellerId, filters, page, limit);
    } catch (error) {
      throw new ApiError(500, `Failed to get assigned requests: ${error.message}`);
    }
  }

  /**
   * Accept an assigned request
   * @param {string} assignmentId - Assignment ID
   * @param {string} sellerId - Seller ID
   * @returns {Promise<Object>} Updated assignment
   */
  static async acceptAssignedRequest(assignmentId, sellerId) {
    try {
      return await SellerModel.acceptAssignedRequest(assignmentId, sellerId);
    } catch (error) {
      if (error.message.includes('not found') || error.message.includes('does not belong')) {
        throw new ApiError(404, error.message);
      } else if (error.message.includes('Cannot accept')) {
        throw new ApiError(400, error.message);
      }
      throw new ApiError(500, `Failed to accept assigned request: ${error.message}`);
    }
  }

  /**
   * Reject an assigned request
   * @param {string} assignmentId - Assignment ID
   * @param {string} sellerId - Seller ID
   * @param {string} reason - Rejection reason
   * @returns {Promise<Object>} Updated assignment
   */
  static async rejectAssignedRequest(assignmentId, sellerId, reason) {
    try {
      return await SellerModel.rejectAssignedRequest(assignmentId, sellerId, reason);
    } catch (error) {
      if (error.message.includes('not found') || error.message.includes('does not belong')) {
        throw new ApiError(404, error.message);
      } else if (error.message.includes('Cannot reject')) {
        throw new ApiError(400, error.message);
      }
      throw new ApiError(500, `Failed to reject assigned request: ${error.message}`);
    }
  }

  /**
   * Create an offer for a request
   * @param {Object} offerData - Offer data
   * @param {string} sellerId - Seller ID
   * @returns {Promise<Object>} Created offer
   */
  static async createOffer(offerData, sellerId) {
    try {
      // Validate offer data
      if (!offerData.price || offerData.price <= 0) {
        throw new ApiError(400, 'Price must be greater than 0');
      }

      if (!offerData.delivery_time || offerData.delivery_time <= 0) {
        throw new ApiError(400, 'Delivery time must be greater than 0');
      }

      return await SellerModel.createOffer(offerData, sellerId);
    } catch (error) {
      if (error.message.includes('not found')) {
        throw new ApiError(404, error.message);
      } else if (error.message.includes('must be assigned') || error.message.includes('already have an offer')) {
        throw new ApiError(400, error.message);
      }
      throw new ApiError(500, `Failed to create offer: ${error.message}`);
    }
  }

  /**
   * Get request details for a seller
   * @param {string} requestId - Request ID
   * @param {string} sellerId - Seller ID
   * @returns {Promise<Object>} Request details
   */
  static async getRequestDetailsForSeller(requestId, sellerId) {
    try {
      return await SellerModel.getRequestDetailsForSeller(requestId, sellerId);
    } catch (error) {
      if (error.message.includes('not assigned')) {
        throw new ApiError(403, error.message);
      } else if (error.message.includes('not found')) {
        throw new ApiError(404, error.message);
      }
      throw new ApiError(500, `Failed to get request details: ${error.message}`);
    }
  }

  /**
   * Get offers created by a seller
   * @param {string} sellerId - Seller ID
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated offers
   */
  static async getSellerOffers(sellerId, filters = {}, page = 1, limit = 10) {
    try {
      return await SellerModel.getSellerOffers(sellerId, filters, page, limit);
    } catch (error) {
      throw new ApiError(500, `Failed to get seller offers: ${error.message}`);
    }
  }

  /**
   * Update an offer
   * @param {string} offerId - Offer ID
   * @param {string} sellerId - Seller ID
   * @param {Object} offerData - Offer data to update
   * @returns {Promise<Object>} Updated offer
   */
  static async updateOffer(offerId, sellerId, offerData) {
    try {
      // Validate offer data
      if (offerData.price !== undefined && (offerData.price <= 0)) {
        throw new ApiError(400, 'Price must be greater than 0');
      }

      if (offerData.delivery_time !== undefined && (offerData.delivery_time <= 0)) {
        throw new ApiError(400, 'Delivery time must be greater than 0');
      }

      return await SellerModel.updateOffer(offerId, sellerId, offerData);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      if (error.message.includes('not found')) {
        throw new ApiError(404, error.message);
      } else if (error.message.includes('cannot update') || error.message.includes('not allowed')) {
        throw new ApiError(400, error.message);
      }
      throw new ApiError(500, `Failed to update offer: ${error.message}`);
    }
  }

  /**
   * Update seller profile
   * @param {string} sellerId - Seller ID
   * @param {Object} profileData - Profile data to update
   * @returns {Promise<Object>} Updated seller profile
   */
  static async updateProfile(sellerId, profileData) {
    try {
      // Check if email is being updated and if it's already in use
      if (profileData.email) {
        const existingUser = await SellerModel.findUserByEmail(profileData.email);
        if (existingUser && existingUser.id !== sellerId) {
          throw new ApiError(422, 'The email has already been taken');
        }
      }

      // Check if phone number is being updated and if it's already in use
      if (profileData.phone_number) {
        const existingUser = await SellerModel.findUserByPhone(profileData.phone_number);
        if (existingUser && existingUser.id !== sellerId) {
          throw new ApiError(422, 'The phone number has already been taken');
        }
      }

      return await SellerModel.updateSellerProfile(sellerId, profileData);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, `Failed to update profile: ${error.message}`);
    }
  }

  /**
   * Get seller profile
   * @param {string} sellerId - Seller ID
   * @returns {Promise<Object>} Seller profile with business information
   */
  static async getSellerProfile(sellerId) {
    try {
      const profile = await SellerModel.getSellerProfile(sellerId);

      if (!profile) {
        throw new ApiError(404, 'Seller profile not found');
      }

      return profile;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, `Failed to get seller profile: ${error.message}`);
    }
  }

  // Business Information Methods

  /**
   * Create business information
   * @param {string} sellerId - Seller ID
   * @param {Object} businessData - Business information data
   * @returns {Promise<Object>} Created business information
   */
  static async createBusinessInformation(sellerId, businessData) {
    try {
      return await SellerModel.createBusinessInformation(sellerId, businessData);
    } catch (error) {
      throw new ApiError(500, `Failed to create business information: ${error.message}`);
    }
  }

  /**
   * Get seller business informations
   * @param {string} sellerId - Seller ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} List of business information
   */
  static async getSellerBusinessInformations(sellerId, options = {}) {
    try {
      return await SellerModel.getSellerBusinessInformations(sellerId, options);
    } catch (error) {
      throw new ApiError(500, `Failed to get business information: ${error.message}`);
    }
  }

  /**
   * Get business information by ID
   * @param {string} businessId - Business information ID
   * @param {string} sellerId - Seller ID
   * @returns {Promise<Object>} Business information
   */
  static async getBusinessInformationById(businessId, sellerId) {
    try {
      const businessInfo = await SellerModel.getBusinessInformationById(businessId, sellerId);

      if (!businessInfo) {
        throw new ApiError(404, 'Business information not found');
      }

      return businessInfo;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, `Failed to get business information: ${error.message}`);
    }
  }

  /**
   * Update business information
   * @param {string} businessId - Business information ID
   * @param {string} sellerId - Seller ID
   * @param {Object} updateData - Update data
   * @returns {Promise<Object>} Updated business information
   */
  static async updateBusinessInformation(businessId, sellerId, updateData) {
    try {
      return await SellerModel.updateBusinessInformation(businessId, sellerId, updateData);
    } catch (error) {
      if (error.message.includes('not found') || error.message.includes('access denied')) {
        throw new ApiError(404, error.message);
      }
      throw new ApiError(500, `Failed to update business information: ${error.message}`);
    }
  }

  /**
   * Delete business information
   * @param {string} businessId - Business information ID
   * @param {string} sellerId - Seller ID
   * @returns {Promise<Object>} Deleted business information
   */
  static async deleteBusinessInformation(businessId, sellerId) {
    try {
      return await SellerModel.deleteBusinessInformation(businessId, sellerId);
    } catch (error) {
      if (error.message.includes('not found') || error.message.includes('access denied')) {
        throw new ApiError(404, error.message);
      }
      throw new ApiError(500, `Failed to delete business information: ${error.message}`);
    }
  }
}

module.exports = SellerService;
