const { body, param, query } = require('express-validator');
const { validationResult } = require('express-validator');
const sendResponse = require('../utils/sendResponse');

/**
 * Validation middleware to handle validation errors
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = {};
    errors.array().forEach(error => {
      if (!errorMessages[error.path]) {
        errorMessages[error.path] = [];
      }
      errorMessages[error.path].push(error.msg);
    });

    return sendResponse(
      res,
      false,
      'Validation failed',
      null,
      errorMessages,
      null,
      400
    );
  }
  next();
};

/**
 * Validation for creating subscription plan
 */
const createSubscriptionValidation = [
  body('name')
    .notEmpty()
    .withMessage('Subscription name is required')
    .isLength({ min: 3, max: 100 })
    .withMessage('Subscription name must be between 3 and 100 characters'),
  
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must not exceed 500 characters'),
  
  body('price')
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number'),
  
  body('duration_days')
    .isInt({ min: 1 })
    .withMessage('Duration must be at least 1 day'),
  
  body('max_requests')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Max requests must be a positive integer'),
  
  body('max_offers')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Max offers must be a positive integer'),
  
  body('max_orders')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Max orders must be a positive integer'),
  
  body('user_type')
    .optional()
    .isIn(['BUYER', 'SELLER', 'BOTH'])
    .withMessage('User type must be BUYER, SELLER, or BOTH'),
  
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean'),
  
  body('is_featured')
    .optional()
    .isBoolean()
    .withMessage('is_featured must be a boolean'),
  
  handleValidationErrors
];

/**
 * Validation for updating subscription plan
 */
const updateSubscriptionValidation = [
  param('id')
    .isUUID()
    .withMessage('Invalid subscription ID'),
  
  body('name')
    .optional()
    .isLength({ min: 3, max: 100 })
    .withMessage('Subscription name must be between 3 and 100 characters'),
  
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must not exceed 500 characters'),
  
  body('price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number'),
  
  body('duration_days')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Duration must be at least 1 day'),
  
  body('max_requests')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Max requests must be a positive integer'),
  
  body('max_offers')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Max offers must be a positive integer'),
  
  body('max_orders')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Max orders must be a positive integer'),
  
  body('user_type')
    .optional()
    .isIn(['BUYER', 'SELLER', 'BOTH'])
    .withMessage('User type must be BUYER, SELLER, or BOTH'),
  
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean'),
  
  body('is_featured')
    .optional()
    .isBoolean()
    .withMessage('is_featured must be a boolean'),
  
  handleValidationErrors
];

/**
 * Validation for subscription ID parameter
 */
const subscriptionIdValidation = [
  param('id')
    .isUUID()
    .withMessage('Invalid subscription ID'),
  
  handleValidationErrors
];

/**
 * Validation for user subscription
 */
const subscribeUserValidation = [
  body('subscription_id')
    .isUUID()
    .withMessage('Valid subscription ID is required'),
  
  body('payment_method')
    .optional()
    .isLength({ min: 3, max: 50 })
    .withMessage('Payment method must be between 3 and 50 characters'),
  
  body('transaction_id')
    .optional()
    .isLength({ min: 3, max: 100 })
    .withMessage('Transaction ID must be between 3 and 100 characters'),
  
  body('auto_renew')
    .optional()
    .isBoolean()
    .withMessage('auto_renew must be a boolean'),
  
  handleValidationErrors
];

/**
 * Validation for cancel subscription
 */
const cancelSubscriptionValidation = [
  param('subscription_id')
    .isUUID()
    .withMessage('Invalid subscription ID'),
  
  handleValidationErrors
];

/**
 * Validation for query parameters
 */
const queryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),

  query('user_type')
    .optional()
    .isIn(['BUYER', 'SELLER', 'BOTH'])
    .withMessage('User type must be BUYER, SELLER, or BOTH'),

  query('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean'),

  query('is_featured')
    .optional()
    .isBoolean()
    .withMessage('is_featured must be a boolean'),

  handleValidationErrors
];

/**
 * Validation for updating trial duration
 */
const updateTrialDurationValidation = [
  body('duration_days')
    .isInt({ min: 1, max: 365 })
    .withMessage('Trial duration must be between 1 and 365 days'),

  handleValidationErrors
];

/**
 * Validation for assigning trial subscription to users
 */
const assignTrialValidation = [
  body('user_ids')
    .isArray({ min: 1 })
    .withMessage('user_ids must be a non-empty array'),

  body('user_ids.*')
    .isUUID()
    .withMessage('Each user ID must be a valid UUID'),

  handleValidationErrors
];

module.exports = {
  createSubscriptionValidation,
  updateSubscriptionValidation,
  subscriptionIdValidation,
  subscribeUserValidation,
  cancelSubscriptionValidation,
  queryValidation,
  updateTrialDurationValidation,
  assignTrialValidation
};
