const SubscriptionService = require('../services/subscriptionService');
const sendResponse = require('../utils/sendResponse');

/**
 * Middleware to check subscription limits before allowing actions
 * @param {string} usageType - Type of usage to check (REQUEST, OFFER, ORDER)
 * @returns {Function} Express middleware function
 */
function checkSubscriptionLimit(usageType) {
  return async (req, res, next) => {
    try {
      const userId = req.user.id;

      // Check if user can perform the action based on subscription limits
      const usageCheck = await SubscriptionService.checkAndTrackUsage(userId, usageType);

      if (!usageCheck.success) {
        return sendResponse(
          res,
          false,
          `Subscription limit exceeded for ${usageType.toLowerCase()}s`,
          null,
          { subscription: [`You have reached your ${usageType.toLowerCase()} limit for this month. Please upgrade your subscription.`] },
          null,
          403
        );
      }

      // Add usage information to request for potential use in controller
      req.subscriptionUsage = usageCheck.usage;
      next();
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { subscription: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  };
}

/**
 * Middleware to check if user has an active subscription
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function requireActiveSubscription(req, res, next) {
  try {
    const userId = req.user.id;
    const activeSubscription = await SubscriptionService.getUserActiveSubscription(userId);

    if (!activeSubscription) {
      return sendResponse(
        res,
        false,
        'Active subscription required',
        null,
        { subscription: ['You need an active subscription to perform this action. Please subscribe to a plan.'] },
        null,
        403
      );
    }

    // Add subscription info to request
    req.activeSubscription = activeSubscription;
    next();
  } catch (error) {
    return sendResponse(
      res,
      false,
      error.message,
      null,
      { subscription: [error.message] },
      null,
      error.statusCode || 500
    );
  }
}

/**
 * Middleware to check subscription limits for requests
 */
const checkRequestLimit = checkSubscriptionLimit('REQUEST');

/**
 * Middleware to check subscription limits for offers
 */
const checkOfferLimit = checkSubscriptionLimit('OFFER');

/**
 * Middleware to check subscription limits for orders
 */
const checkOrderLimit = checkSubscriptionLimit('ORDER');

module.exports = {
  checkSubscriptionLimit,
  requireActiveSubscription,
  checkRequestLimit,
  checkOfferLimit,
  checkOrderLimit
};
