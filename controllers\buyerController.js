const BuyerService = require('../services/buyerService');
const SubcategoryService = require('../services/subCategoryService');
const SubCategoryFormFieldService = require('../services/subCategoryFormFieldService');
const sendResponse = require('../utils/sendResponse');
const { validationResult } = require('express-validator');
const { formatValidationErrors } = require('../utils/validationFormatter');
const { saveBufferToFile } = require('../utils/fileUpload');

class BuyerController {
  /**
   * Get buyer profile
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getBuyerProfile(req, res) {
    try {
      const buyerId = req.user.id;
      const profile = await BuyerService.getBuyerProfile(buyerId);

      return sendResponse(
        res,
        true,
        'Buyer profile retrieved successfully',
        profile,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Update buyer profile
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async updateBuyerProfile(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const laravelStyleErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          laravelStyleErrors.message,
          null,
          laravelStyleErrors.errors,
          null,
          422
        );
      }

      const buyerId = req.user.id;
      let profileData = req.body;

      // Handle avatar upload if present
      if (req.file) {
        // Validate image file
        const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedMimeTypes.includes(req.file.mimetype)) {
          return sendResponse(
            res,
            false,
            'Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.',
            null,
            { profile_picture_url: ['Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.'] },
            null,
            422
          );
        }

        // Check file size (max 5MB)
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (req.file.size > maxSize) {
          return sendResponse(
            res,
            false,
            'File size exceeds the limit of 5MB.',
            null,
            { profile_picture_url: ['File size exceeds the limit of 5MB.'] },
            null,
            422
          );
        }

        profileData.profile_picture_url = saveBufferToFile(
          req.file.buffer,
          req.file.originalname,
          'uploads/avatars'
        );
      }

      // Convert date_of_birth to Date object if it's a string
      if (profileData.date_of_birth && typeof profileData.date_of_birth === 'string') {
        profileData.date_of_birth = new Date(profileData.date_of_birth);
      }

      const updatedProfile = await BuyerService.updateBuyerProfile(buyerId, profileData);

      return sendResponse(
        res,
        true,
        'Buyer profile updated successfully',
        updatedProfile,
        null,
        null,
        200
      );
    } catch (error) {
      // Format API errors in Laravel style
      let statusCode = error.statusCode || 400;

      // Check if it's a validation error
      if (error.message.includes('already been taken') ||
          error.message.includes('already in use') ||
          error.message.includes('invalid')) {
        statusCode = 422; // Unprocessable Entity for validation errors
      }

      const errorResponse = {
        message: error.message || 'An error occurred',
        errors: {}
      };

      // If it's a specific field error
      if (error.field) {
        errorResponse.errors[error.field] = [error.message];
      } else {
        // General error
        errorResponse.errors.general = [error.message || 'An error occurred while updating the profile'];
      }

      return sendResponse(
        res,
        false,
        errorResponse.message,
        null,
        errorResponse.errors,
        null,
        statusCode
      );
    }
  }

  /**
   * Get buyer requests
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getBuyerRequests(req, res) {
    try {
      const buyerId = req.user.id;
      const { status, search } = req.query;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      const filters = {};
      if (status) {
        filters.status = status;
      }
      if (search) {
        filters.search = search;
      }

      const result = await BuyerService.getBuyerRequests(buyerId, filters, page, limit);

      return sendResponse(
        res,
        true,
        'Buyer requests retrieved successfully',
        result.data,
        null,
        result.meta,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Get buyer offers
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getBuyerOffers(req, res) {
    try {
      const buyerId = req.user.id;
      const { status, request_id } = req.query;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      const filters = {};
      if (status) {
        filters.status = status;
      }
      if (request_id) {
        filters.request_id = request_id;
      }

      const result = await BuyerService.getBuyerOffers(buyerId, filters, page, limit);

      return sendResponse(
        res,
        true,
        'Buyer offers retrieved successfully',
        result.data,
        null,
        result.meta,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Get accepted offers for a buyer without seller information
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getBuyerAcceptedOffers(req, res) {
    try {
      const buyerId = req.user.id;
      const { request_id } = req.query;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      const filters = {};
      if (request_id) {
        filters.request_id = request_id;
      }

      const result = await BuyerService.getBuyerAcceptedOffers(buyerId, filters, page, limit);

      return sendResponse(
        res,
        true,
        'Accepted offers retrieved successfully',
        result.data,
        null,
        result.meta,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Get a specific offer by ID for a buyer without seller information
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getBuyerOfferById(req, res) {
    try {
      const buyerId = req.user.id;
      const offerId = req.params.id;

      const offer = await BuyerService.getBuyerOfferById(buyerId, offerId);

      return sendResponse(
        res,
        true,
        'Offer details retrieved successfully',
        offer,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }
  /**
   * Get subcategory details with form fields by subcategory ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getSubcategoryWithFormFields(req, res) {
    try {
      const subcategoryId = req.params.id;

      // Check if subcategory exists
      const subcategory = await SubcategoryService.get(subcategoryId);
      if (!subcategory) {
        return sendResponse(
          res,
          false,
          'Subcategory not found',
          null,
          null,
          null,
          404
        );
      }

      // Get form fields for the subcategory
      const formFields = await SubCategoryFormFieldService.getFormFieldsBySubcategoryId(subcategoryId);

      // Combine the data
      const responseData = {
        ...subcategory,
        form_fields: formFields
      };

      return sendResponse(
        res,
        true,
        'Subcategory details with form fields retrieved successfully',
        responseData,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }
}

module.exports = BuyerController;
