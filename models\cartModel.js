const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

class CartModel {
  /**
   * Get or create a cart for a buyer
   * @param {string} buyerId - Buyer ID
   * @returns {Promise<Object>} Cart object
   */
  static async getOrCreateCart(buyerId) {
    try {
      // Check if the buyer already has a cart using raw SQL
      const existingCarts = await prisma.$queryRaw`
        SELECT id, buyer_id, created_at, updated_at
        FROM carts
        WHERE buyer_id = ${buyerId}
      `;

      let cart = existingCarts.length > 0 ? existingCarts[0] : null;

      // If no cart exists, create one
      if (!cart) {
        const newCarts = await prisma.$queryRaw`
          INSERT INTO carts (id, buyer_id, created_at, updated_at)
          VALUES (gen_random_uuid(), ${buyerId}, NOW(), NOW())
          RETURNING id, buyer_id, created_at, updated_at
        `;
        cart = newCarts[0];
      }

      return cart;
    } catch (error) {
      console.error('Error in getOrCreateCart:', error);
      throw error;
    }
  }

  /**
   * Add an offer to the cart
   * @param {string} buyerId - Buyer ID
   * @param {string} offerId - Offer ID
   * @param {number} quantity - Quantity to add
   * @returns {Promise<Object>} Cart item
   */
  static async addToCart(buyerId, offerId, quantity = 1) {
    try {
      // First, verify the offer exists and is valid for this buyer using raw SQL
      const validOffers = await prisma.$queryRaw`
        SELECT o.id, o.price
        FROM offers o
        JOIN requests r ON o.request_id = r.id
        WHERE o.id = ${offerId}
        AND o.status = 'Approved'
        AND o.is_deleted = false
        AND r.buyer_id = ${buyerId}
      `;

      if (validOffers.length === 0) {
        throw new Error('Offer not found or not available for purchase');
      }

      const offer = validOffers[0];

      // Get or create the buyer's cart
      const cart = await this.getOrCreateCart(buyerId);

      // Check if this offer is already in the cart
      const existingCartItems = await prisma.$queryRaw`
        SELECT id, quantity
        FROM cart_items
        WHERE cart_id = ${cart.id}::uuid
        AND offer_id = ${offerId}
      `;

      let cartItem;

      if (existingCartItems.length > 0) {
        // Update the quantity if it already exists
        const existingItem = existingCartItems[0];
        const newQuantity = existingItem.quantity + quantity;

        const updatedItems = await prisma.$queryRaw`
          UPDATE cart_items
          SET quantity = ${newQuantity},
              price = ${offer.price},
              updated_at = NOW()
          WHERE id = ${existingItem.id}::uuid
          RETURNING id, cart_id, offer_id, quantity, price, created_at, updated_at
        `;

        cartItem = updatedItems[0];
      } else {
        // Add new item to cart
        const newItems = await prisma.$queryRaw`
          INSERT INTO cart_items (id, cart_id, offer_id, quantity, price, created_at, updated_at)
          VALUES (gen_random_uuid(), ${cart.id}::uuid, ${offerId}, ${quantity}, ${offer.price}, NOW(), NOW())
          RETURNING id, cart_id, offer_id, quantity, price, created_at, updated_at
        `;

        cartItem = newItems[0];
      }

      // Get offer and request details
      const offerDetails = await prisma.$queryRaw`
        SELECT o.*, r.title as request_title, r.short_description as request_short_description
        FROM offers o
        JOIN requests r ON o.request_id = r.id
        WHERE o.id = ${offerId}
      `;

      return {
        ...cartItem,
        offer: offerDetails[0]
      };
    } catch (error) {
      console.error('Error in addToCart:', error);
      throw error;
    }
  }

  /**
   * Get all items in a buyer's cart
   * @param {string} buyerId - Buyer ID
   * @returns {Promise<Object>} Cart with items
   */
  static async getCartItems(buyerId) {
    try {
      // Get the buyer's cart
      const carts = await prisma.$queryRaw`
        SELECT id, buyer_id, created_at, updated_at
        FROM carts
        WHERE buyer_id = ${buyerId}
      `;

      if (carts.length === 0) {
        // Return empty cart if none exists
        return {
          id: null,
          buyer_id: buyerId,
          cart_items: [],
          total_items: 0,
          total_price: 0,
        };
      }

      const cart = carts[0];

      // Get cart items with offer and request details
      const cartItems = await prisma.$queryRaw`
        SELECT ci.*,
               o.id as offer_id, o.price as offer_price, o.delivery_time,
               r.title as request_title, r.short_description as request_short_description
        FROM cart_items ci
        JOIN offers o ON ci.offer_id = o.id
        JOIN requests r ON o.request_id = r.id
        WHERE ci.cart_id = ${cart.id}::uuid
      `;

      // Format cart items to include offer object
      const formattedCartItems = cartItems.map(item => ({
        id: item.id,
        cart_id: item.cart_id,
        offer_id: item.offer_id,
        quantity: item.quantity,
        price: item.price,
        created_at: item.created_at,
        updated_at: item.updated_at,
        offer: {
          id: item.offer_id,
          price: item.offer_price,
          delivery_time: item.delivery_time,
          request: {
            title: item.request_title,
            short_description: item.request_short_description
          }
        }
      }));

      // Calculate totals
      const totalItems = formattedCartItems.reduce((sum, item) => sum + item.quantity, 0);
      const totalPrice = formattedCartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);

      return {
        ...cart,
        cart_items: formattedCartItems,
        total_items: totalItems,
        total_price: totalPrice,
      };
    } catch (error) {
      console.error('Error in getCartItems:', error);
      throw error;
    }
  }

  /**
   * Update a cart item
   * @param {string} buyerId - Buyer ID
   * @param {string} cartItemId - Cart item ID
   * @param {number} quantity - New quantity
   * @returns {Promise<Object>} Updated cart item
   */
  static async updateCartItem(buyerId, cartItemId, quantity) {
    try {
      // Verify the cart item belongs to this buyer
      const cartItems = await prisma.$queryRaw`
        SELECT ci.id, ci.quantity
        FROM cart_items ci
        JOIN carts c ON ci.cart_id = c.id
        WHERE ci.id = ${cartItemId}::uuid
        AND c.buyer_id = ${buyerId}
      `;

      if (cartItems.length === 0) {
        throw new Error('Cart item not found');
      }

      if (quantity <= 0) {
        // Remove the item if quantity is 0 or negative
        return await this.removeCartItem(buyerId, cartItemId);
      }

      // Update the quantity
      const updatedItems = await prisma.$queryRaw`
        UPDATE cart_items
        SET quantity = ${quantity},
            updated_at = NOW()
        WHERE id = ${cartItemId}::uuid
        RETURNING id, cart_id, offer_id, quantity, price, created_at, updated_at
      `;

      const cartItem = updatedItems[0];

      // Get offer and request details
      const offerDetails = await prisma.$queryRaw`
        SELECT o.*, r.title as request_title, r.short_description as request_short_description
        FROM offers o
        JOIN requests r ON o.request_id = r.id
        JOIN cart_items ci ON ci.offer_id = o.id
        WHERE ci.id = ${cartItemId}::uuid
      `;

      return {
        ...cartItem,
        offer: {
          ...offerDetails[0],
          request: {
            title: offerDetails[0].request_title,
            short_description: offerDetails[0].request_short_description
          }
        }
      };
    } catch (error) {
      console.error('Error in updateCartItem:', error);
      throw error;
    }
  }

  /**
   * Remove an item from the cart
   * @param {string} buyerId - Buyer ID
   * @param {string} cartItemId - Cart item ID
   * @returns {Promise<Object>} Result of deletion
   */
  static async removeCartItem(buyerId, cartItemId) {
    try {
      // Verify the cart item belongs to this buyer
      const cartItems = await prisma.$queryRaw`
        SELECT ci.id
        FROM cart_items ci
        JOIN carts c ON ci.cart_id = c.id
        WHERE ci.id = ${cartItemId}::uuid
        AND c.buyer_id = ${buyerId}
      `;

      if (cartItems.length === 0) {
        throw new Error('Cart item not found');
      }

      // Delete the cart item
      await prisma.$queryRaw`
        DELETE FROM cart_items
        WHERE id = ${cartItemId}::uuid
      `;

      return { id: cartItemId, deleted: true };
    } catch (error) {
      console.error('Error in removeCartItem:', error);
      throw error;
    }
  }

  /**
   * Clear all items from a buyer's cart
   * @param {string} buyerId - Buyer ID
   * @returns {Promise<Object>} Result of deletion
   */
  static async clearCart(buyerId) {
    try {
      // Get the buyer's cart
      const carts = await prisma.$queryRaw`
        SELECT id
        FROM carts
        WHERE buyer_id = ${buyerId}
      `;

      if (carts.length === 0) {
        return { count: 0 };
      }

      const cart = carts[0];

      // Delete all items in the cart
      const result = await prisma.$queryRaw`
        DELETE FROM cart_items
        WHERE cart_id = ${cart.id}::uuid
        RETURNING id
      `;

      return { count: result.length };
    } catch (error) {
      console.error('Error in clearCart:', error);
      throw error;
    }
  }
}

module.exports = CartModel;
