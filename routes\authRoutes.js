const express = require('express');
const authController = require('../controllers/authController');
const userController = require('../controllers/userController');

const authenticateToken = require('../middlewares/authMiddleware');
const { registerValidation } = require("../validations/registerValidation");
const { loginValidation } = require("../validations/Auth/loginValidation");
const {
  resetPasswordValidation,
  forgotPasswordValidation,
  changePasswordValidation
} = require("../validations/Auth/resetPasswordValidation");
const { newUserRegistrationValidation } = require("../validations/Auth/newUserRegistrationValidation");
const { completeProfileValidation } = require("../validations/Auth/completeProfileValidation");
const { uploadProfilePictureValidation } = require("../validations/uploadProfilePictureValidation");
const { upload } = require('../middlewares/upload');
const router = express.Router();

// Public routes
router.post('/register', registerValidation, authController.register);
router.post('/mobile/register', registerValidation, authController.mobileRegister);
router.post('/verify/otp', authController.verifyOtp);
router.post('/login', loginValidation, authController.login);
router.post('/refresh-token', authController.refreshToken);
router.post('/forgot-password', forgotPasswordValidation, authController.forgotPassword);
router.post('/reset-password', resetPasswordValidation, authController.resetPassword);
router.post('/verify-email', authController.verifyEmail);

// New user registration and login routes
router.post('/new-register', newUserRegistrationValidation, authController.newUserRegister);
router.post('/new-login', authController.newUserLogin);

// Authenticated routes
router.post('/logout', authenticateToken, authController.logout);
router.post('/change-password', authenticateToken, changePasswordValidation, authController.changePassword);
router.get('/user', authenticateToken, authController.getAuthenticatedUser);

router.get('/profile', authenticateToken, userController.getUserProfile);

// Profile completion routes
router.post('/complete-profile',
  authenticateToken,
  upload.fields([
    { name: 'profile_picture', maxCount: 1 },
    { name: 'business_document', maxCount: 1 },
    { name: 'nid_document', maxCount: 1 }
  ]),
  completeProfileValidation,
  authController.completeProfile
);
router.get('/profile-status', authenticateToken, authController.getProfileStatus);

// Profile picture upload route
router.post('/upload-profile-picture',
  authenticateToken,
  upload.single('profile_picture'),
  uploadProfilePictureValidation,
  authController.uploadProfilePicture
);

module.exports = router;