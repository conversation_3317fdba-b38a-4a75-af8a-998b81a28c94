const OfferModel = require('../models/offerModel');
const ApiError = require('../utils/apiError');

class OfferService {
  /**
   * Get all offers with pagination and filtering
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated offers with request and seller information
   */
  static async getAllOffers(filters = {}, page = 1, limit = 10) {
    try {
      return await OfferModel.getAllOffers(filters, page, limit);
    } catch (error) {
      throw new ApiError(500, `Failed to get offers: ${error.message}`);
    }
  }

  /**
   * Get offer by ID
   * @param {string} offerId - Offer ID
   * @returns {Promise<Object>} Offer with request and seller information
   */
  static async getOfferById(offerId) {
    try {
      const offer = await OfferModel.getOfferById(offerId);

      if (!offer) {
        throw new ApiError(404, 'Offer not found');
      }

      return offer;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, `Failed to get offer: ${error.message}`);
    }
  }

  /**
   * Update offer status
   * @param {string} offerId - Offer ID
   * @param {string} status - New status
   * @param {string} userId - User ID making the update
   * @param {string} reason - Reason for status change (optional)
   * @returns {Promise<Object>} Updated offer
   */
  static async updateOfferStatus(offerId, status, userId, reason = null) {
    try {
      // Validate that the offer exists
      const existingOffer = await OfferModel.getOfferById(offerId);

      if (!existingOffer) {
        throw new ApiError(404, 'Offer not found');
      }

      // Update the offer status
      const updatedOffer = await OfferModel.updateOfferStatus(offerId, status, userId, reason);

      return updatedOffer;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, `Failed to update offer status: ${error.message}`);
    }
  }
}

module.exports = OfferService;
