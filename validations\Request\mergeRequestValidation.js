const { body } = require('express-validator');
const { validationResult } = require('express-validator');
const sendResponse = require('../../utils/sendResponse');

/**
 * Validation middleware to handle validation errors
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = {};
    errors.array().forEach(error => {
      if (!errorMessages[error.path]) {
        errorMessages[error.path] = [];
      }
      errorMessages[error.path].push(error.msg);
    });

    return sendResponse(
      res,
      false,
      'Validation failed',
      null,
      errorMessages,
      null,
      400
    );
  }
  next();
};

/**
 * Validation for merge requests
 */
const mergeRequestValidation = [
  body('main_request_id')
    .notEmpty()
    .withMessage('Main request ID is required')
    .isUUID()
    .withMessage('Main request ID must be a valid UUID'),

  body('request_ids_to_merge')
    .isArray({ min: 1 })
    .withMessage('Request IDs to merge must be a non-empty array'),

  body('request_ids_to_merge.*')
    .isUUID()
    .withMessage('Each request ID must be a valid UUID'),

  // Custom validation to ensure main_request_id is not in request_ids_to_merge
  body('request_ids_to_merge')
    .custom((value, { req }) => {
      if (value.includes(req.body.main_request_id)) {
        throw new Error('Main request ID cannot be included in the list of requests to merge');
      }
      return true;
    }),

  // Custom validation to ensure no duplicate IDs in request_ids_to_merge
  body('request_ids_to_merge')
    .custom((value) => {
      const uniqueIds = [...new Set(value)];
      if (uniqueIds.length !== value.length) {
        throw new Error('Duplicate request IDs are not allowed');
      }
      return true;
    }),

  handleValidationErrors
];

module.exports = {
  mergeRequestValidation
};
