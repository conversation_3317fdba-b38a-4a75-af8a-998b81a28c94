/**
 * Format validation errors in Laravel-style
 * 
 * @param {Array} errors - Array of validation errors from express-validator
 * @returns {Object} - Laravel-style formatted errors
 */
const formatValidationErrors = (errors) => {
  const formattedErrors = {};
  
  errors.forEach(error => {
    // If this is the first error for this field, create an array
    if (!formattedErrors[error.path]) {
      formattedErrors[error.path] = [];
    }
    
    // Add the error message to the array
    formattedErrors[error.path].push(error.msg);
  });
  
  return {
    message: 'The given data was invalid.',
    errors: formattedErrors
  };
};

module.exports = { formatValidationErrors };
