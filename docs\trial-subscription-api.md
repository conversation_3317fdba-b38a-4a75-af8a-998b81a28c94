# Trial Subscription API Documentation

## Overview

This document describes the trial subscription management API endpoints that allow administrators to manage trial subscriptions for users. By default, users get a 14-day trial subscription, and administrators can update the trial duration and assign trial subscriptions to existing users.

## Features

- **Default Trial Duration**: 14 days (configurable)
- **Trial Subscription Plan**: Includes basic features with limited usage
- **Admin Management**: Update trial duration and assign to users
- **Automatic Assignment**: Assign trial to all existing users without active subscriptions

## Trial Subscription Plan Details

- **Name**: Trial Plan
- **Duration**: 14 days (default, configurable)
- **Price**: $0.00 (Free)
- **Max Requests**: 10 per month
- **Max Offers**: 10 per month
- **Max Orders**: 5 per month
- **User Type**: BOTH (Buyers and Sellers)
- **Features**: Email support, low priority

## API Endpoints

### 1. Update Trial Duration

Updates the duration of the trial subscription plan for all future trial assignments.

**Endpoint**: `PUT /api/admin/subscriptions/trial/duration`

**Authentication**: Admin token required

**Request Body**:
```json
{
  "duration_days": 21
}
```

**Validation**:
- `duration_days`: Integer between 1 and 365

**Response**:
```json
{
  "success": true,
  "message": "Trial subscription duration updated successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "name": "Trial Plan",
    "description": "14-day trial subscription with basic features",
    "price": "0",
    "duration_days": 21,
    "max_requests": 10,
    "max_offers": 10,
    "max_orders": 5,
    "is_active": true,
    "is_featured": false,
    "user_type": "BOTH",
    "features": {
      "trial": true,
      "support": "email",
      "priority": "low"
    },
    "created_at": "2025-06-12T16:32:19.524Z",
    "updated_at": "2025-06-12T16:40:25.661Z"
  }
}
```

### 2. Assign Trial to Specific Users

Assigns trial subscription to specific users by their IDs.

**Endpoint**: `POST /api/admin/subscriptions/trial/assign`

**Authentication**: Admin token required

**Request Body**:
```json
{
  "user_ids": [
    "user-id-1",
    "user-id-2",
    "user-id-3"
  ]
}
```

**Validation**:
- `user_ids`: Array of valid UUIDs (minimum 1 user)

**Response**:
```json
{
  "success": true,
  "message": "Trial subscription assignment completed",
  "data": [
    {
      "userId": "user-id-1",
      "success": true,
      "subscription": { /* subscription details */ },
      "message": "Trial subscription assigned successfully"
    },
    {
      "userId": "user-id-2",
      "success": false,
      "message": "User already has an active subscription"
    }
  ]
}
```

### 3. Assign Trial to All Users

Assigns trial subscription to all existing users who don't have active subscriptions.

**Endpoint**: `POST /api/admin/subscriptions/trial/assign-all`

**Authentication**: Admin token required

**Request Body**: Empty `{}`

**Response**:
```json
{
  "success": true,
  "message": "Trial subscription assignment to all users completed",
  "data": {
    "totalUsers": 3,
    "successCount": 3,
    "failureCount": 0,
    "results": [
      {
        "userId": "user-id-1",
        "success": true,
        "subscription": { /* subscription details */ },
        "message": "Trial subscription assigned successfully"
      }
    ],
    "message": "Trial subscription assigned to 3 users, 0 failed"
  }
}
```

## Usage Examples

### Using cURL

#### Update Trial Duration
```bash
curl -X PUT http://localhost:5000/api/admin/subscriptions/trial/duration \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"duration_days": 30}'
```

#### Assign Trial to Specific Users
```bash
curl -X POST http://localhost:5000/api/admin/subscriptions/trial/assign \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"user_ids": ["user-id-1", "user-id-2"]}'
```

#### Assign Trial to All Users
```bash
curl -X POST http://localhost:5000/api/admin/subscriptions/trial/assign-all \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}'
```

## Error Responses

### Validation Errors (400)
```json
{
  "success": false,
  "message": "Validation failed",
  "error": {
    "duration_days": ["Duration must be between 1 and 365 days"]
  }
}
```

### Authentication Errors (401/403)
```json
{
  "success": false,
  "message": "Access denied. Insufficient permissions."
}
```

### Server Errors (500)
```json
{
  "success": false,
  "message": "Failed to update trial duration: Database connection error",
  "error": {
    "general": ["Internal server error"]
  }
}
```

## Business Logic

1. **Trial Assignment**: Users can only have one active subscription at a time
2. **Existing Subscriptions**: Users with active subscriptions are skipped during assignment
3. **Trial Duration**: Changes to trial duration only affect new trial assignments
4. **User Eligibility**: Only active, non-deleted users are eligible for trial assignment
5. **Payment Method**: Trial subscriptions use "trial" as payment method with auto-generated transaction ID

## Database Changes

The trial subscription plan is automatically created with ID `550e8400-e29b-41d4-a716-446655440000` when running the setup script.

## Setup Instructions

1. Run the trial subscription setup script:
   ```bash
   node scripts/add-trial-subscription.js
   ```

2. The trial plan will be created with default 14-day duration

3. Use the API endpoints to manage trial subscriptions

## Testing

Run the test script to verify all endpoints:
```bash
node scripts/test-trial-api.js
```

This will test:
- Admin authentication
- Trial duration update
- Trial assignment to all users
- Subscription listing verification
