const { PrismaClient } = require("@prisma/client");
const bcrypt = require("bcryptjs");

const prisma = new PrismaClient();

// Define default roles as a constant at the top level
const defaultRoles = ["Admin", "Seller", "Buyer", "Supplier"];

/**
 * Create default roles in the database
 * @returns {Promise<void>}
 */
async function createDefaultRoles() {
    try {
        console.log("Creating default roles...");

        // Check if the roles table exists
        try {
            // Create each role if it doesn't exist
            for (const role of defaultRoles) {
                try {
                    const existingRole = await prisma.roles.findFirst({
                        where: { name: role }
                    });

                    if (!existingRole) {
                        await prisma.roles.create({
                            data: { name: role }
                        });
                        console.log(`✅ Role '${role}' added.`);
                    } 
                } catch (roleError) {
                    // If the error is not about the table not existing, rethrow it
                    if (!roleError.message.includes('does not exist in the current database')) {
                        throw roleError;
                    }
                    console.log(`⚠️ Could not check/create role '${role}': ${roleError.message}`);
                }
            }
        } catch (tableError) {
            // If the error is about the table not existing, log it but don't throw
            if (tableError.message.includes('does not exist in the current database')) {
                console.log("⚠️ Roles table does not exist yet. Run migrations first.");
            } else {
                throw tableError;
            }
        }

        console.log("✅ Default roles setup completed.");
    } catch (error) {
        console.error("❌ Error creating default roles:", error);
        // Don't throw the error, just log it
        // This allows the seed script to continue with other operations
    }
}

async function initializeDatabase() {
    try {
        await prisma.$connect();
        console.log("✅ Database connected successfully");

        // First, ensure all default roles exist
        await createDefaultRoles();

        // Check if admin exists
        try {
            const adminExists = await prisma.users.findFirst({
                where: { email: "<EMAIL>" },
            });

            if (!adminExists) {
                const hashedPassword = await bcrypt.hash("12345678", 10);

                try {
                    // Create the Admin user
                    const adminUser = await prisma.users.create({
                        data: {
                            first_name: "Admin",
                            last_name: "Markzoom",
                            email: "<EMAIL>",
                            password_hash: hashedPassword,
                            phone_number: "0123456789",
                            gender: "Male",
                            status: "active",
                            is_approved: true,
                            is_email_verified: true,
                        },
                    });

                    try {
                        // Fetch the Admin role
                        const adminRole = await prisma.roles.findFirst({ where: { name: "Admin" } });

                        if (adminRole) {
                            // Assign Admin role to the user
                            await prisma.user_roles.create({
                                data: {
                                    user_id: adminUser.id,
                                    role_id: adminRole.id,
                                },
                            });
                        }
                    } catch (roleError) {
                        console.log("⚠️ Could not assign Admin role: ", roleError.message);
                    }

                    console.log("✅ Admin account created successfully.");
                } catch (createError) {
                    console.log("⚠️ Could not create Admin account: ", createError.message);
                }
            } 
        } catch (userTableError) {
            if (userTableError.message.includes('does not exist in the current database')) {
                console.log("⚠️ Users table does not exist yet. Run migrations first.");
            } else {
                console.error(" Error checking for Admin account: ", userTableError.message);
            }
        }
    } catch (error) {
        console.error("❌ Error initializing database:", error);
    } finally {
        await prisma.$disconnect();
    }
}

// If this script is run directly, execute the initialization
if (require.main === module) {
    initializeDatabase()
        .then(() => {
            console.log("✅ Database initialization completed successfully");
        })
        .catch((error) => {
            console.error("❌ Database initialization failed:", error);
            process.exit(1);
        });
}

module.exports = { initializeDatabase, createDefaultRoles, defaultRoles };
