const OrderService = require('../services/orderService');
const sendResponse = require('../utils/sendResponse');
const { validationResult } = require('express-validator');
const { formatValidationErrors } = require('../utils/validationFormatter');

class OrderController {
  /**
   * Confirm an order with billing and payment information
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async confirmOrder(req, res) {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const laravelStyleErrors = formatValidationErrors(errors.array());
        return sendResponse(
          res,
          false,
          laravelStyleErrors.message,
          null,
          laravelStyleErrors.errors,
          null,
          422
        );
      }

      const buyerId = req.user.id;
      const { cart_id, billing_info, card_info, amount } = req.body;

      // Add amount to billing info if it's provided at the top level
      const billingInfoWithAmount = {
        ...billing_info,
        amount: amount
      };

      const order = await OrderService.createOrderFromCart(
        buyerId,
        cart_id,
        billingInfoWithAmount,
        card_info
      );

      return sendResponse(
        res,
        true,
        'Order placed successfully',
        order,
        null,
        null,
        201
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 400
      );
    }
  }

  /**
   * Get orders for the authenticated buyer
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getBuyerOrders(req, res) {
    try {
      const buyerId = req.user.id;
      const { status, payment_status } = req.query;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      const filters = {};
      if (status) {
        filters.status = status;
      }
      if (payment_status) {
        filters.payment_status = payment_status;
      }

      const result = await OrderService.getBuyerOrders(buyerId, filters, page, limit);

      return sendResponse(
        res,
        true,
        'Orders retrieved successfully',
        result.data,
        null,
        result.meta,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }
  /**
   * Get all orders for admin
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getBuyerOrderForAdmin(req, res) {
    try {
      const { status, payment_status, buyer_id, seller_id } = req.query;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      const filters = {};
      if (status) {
        filters.status = status;
      }
      if (payment_status) {
        filters.payment_status = payment_status;
      }
      if (buyer_id) {
        filters.buyer_id = buyer_id;
      }
      if (seller_id) {
        filters.seller_id = seller_id;
      }

      const result = await OrderService.getAllOrders(filters, page, limit);

      return sendResponse(
        res,
        true,
        'Orders retrieved successfully',
        result.data,
        null,
        result.meta,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Get order details by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getOrderById(req, res) {
    try {
      const { id } = req.params;
      const order = await OrderService.getOrderById(id);

      return sendResponse(
        res,
        true,
        'Order details retrieved successfully',
        order,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }

  /**
   * Get order details for a buyer (ensures buyer can only access their own orders)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getBuyerOrderDetails(req, res) {
    try {
      const { id } = req.params;
      const buyerId = req.user.id;

      // Get the order details
      const order = await OrderService.getOrderById(id);

      // Check if the order belongs to the authenticated buyer
      if (order.buyer_id !== buyerId) {
        return sendResponse(
          res,
          false,
          'You do not have permission to view this order',
          null,
          { general: ['You do not have permission to view this order'] },
          null,
          403
        );
      }

      return sendResponse(
        res,
        true,
        'Order details retrieved successfully',
        order,
        null,
        null,
        200
      );
    } catch (error) {
      return sendResponse(
        res,
        false,
        error.message,
        null,
        { general: [error.message] },
        null,
        error.statusCode || 500
      );
    }
  }
}

module.exports = OrderController;
