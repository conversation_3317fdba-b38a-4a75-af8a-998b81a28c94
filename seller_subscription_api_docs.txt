SELLER SUBSCRIPTION API DOCUMENTATION
=====================================

Base URL: /api/seller/subscriptions
Authentication: <PERSON><PERSON> (Seller role required)

=====================================

1. GET /subscriptions/available
   Description: Get available subscription plans for sellers
   
   Endpoint: GET /api/seller/subscriptions/available
   
   Headers:
   - Authorization: Bearer <seller_token>
   
   Payload: None
   
   Response:
   {
     "success": true,
     "message": "Available subscription plans retrieved successfully",
     "data": [
       {
         "id": "uuid",
         "name": "Premium Seller Plan",
         "description": "Advanced features for sellers",
         "price": 29.99,
         "duration_days": 30,
         "max_requests": 100,
         "max_offers": 50,
         "max_orders": 25,
         "user_type": "SELLER",
         "is_active": true,
         "is_featured": false,
         "created_at": "2024-01-01T00:00:00.000Z",
         "updated_at": "2024-01-01T00:00:00.000Z"
       }
     ]
   }

=====================================

2. POST /subscriptions/subscribe
   Description: Subscribe to a subscription plan
   
   Endpoint: POST /api/seller/subscriptions/subscribe
   
   Headers:
   - Authorization: Bearer <seller_token>
   - Content-Type: application/json
   
   Payload:
   {
     "subscription_id": "uuid",           // Required: Valid UUID
     "payment_method": "credit_card",     // Optional: 3-50 characters
     "transaction_id": "txn_123456",      // Optional: 3-100 characters
     "auto_renew": true                   // Optional: boolean
   }
   
   Response:
   {
     "success": true,
     "message": "Successfully subscribed to plan",
     "data": {
       "id": "uuid",
       "user_id": "uuid",
       "subscription_id": "uuid",
       "status": "ACTIVE",
       "start_date": "2024-01-01T00:00:00.000Z",
       "end_date": "2024-01-31T00:00:00.000Z",
       "auto_renew": true,
       "payment_method": "credit_card",
       "transaction_id": "txn_123456",
       "created_at": "2024-01-01T00:00:00.000Z"
     }
   }

=====================================

3. GET /subscriptions/active
   Description: Get user's active subscription
   
   Endpoint: GET /api/seller/subscriptions/active
   
   Headers:
   - Authorization: Bearer <seller_token>
   
   Payload: None
   
   Response (with active subscription):
   {
     "success": true,
     "message": "Active subscription retrieved successfully",
     "data": {
       "id": "uuid",
       "user_id": "uuid",
       "subscription_id": "uuid",
       "status": "ACTIVE",
       "start_date": "2024-01-01T00:00:00.000Z",
       "end_date": "2024-01-31T00:00:00.000Z",
       "auto_renew": true,
       "subscription": {
         "id": "uuid",
         "name": "Premium Seller Plan",
         "price": 29.99,
         "duration_days": 30,
         "max_requests": 100,
         "max_offers": 50,
         "max_orders": 25
       }
     }
   }
   
   Response (no active subscription):
   {
     "success": true,
     "message": "No active subscription found",
     "data": null
   }

=====================================

4. GET /subscriptions/history
   Description: Get user's subscription history with pagination
   
   Endpoint: GET /api/seller/subscriptions/history?page=1&limit=10
   
   Headers:
   - Authorization: Bearer <seller_token>
   
   Query Parameters:
   - page: integer (optional, min: 1, default: 1)
   - limit: integer (optional, min: 1, max: 100, default: 10)
   
   Payload: None
   
   Response:
   {
     "success": true,
     "message": "Subscription history retrieved successfully",
     "data": [
       {
         "id": "uuid",
         "subscription_id": "uuid",
         "status": "EXPIRED",
         "start_date": "2023-12-01T00:00:00.000Z",
         "end_date": "2023-12-31T00:00:00.000Z",
         "auto_renew": false,
         "subscription": {
           "name": "Basic Seller Plan",
           "price": 19.99
         }
       }
     ],
     "meta": {
       "page": 1,
       "limit": 10,
       "total": 5,
       "totalPages": 1
     }
   }

=====================================

5. GET /subscriptions/usage
   Description: Get user's current usage status
   
   Endpoint: GET /api/seller/subscriptions/usage
   
   Headers:
   - Authorization: Bearer <seller_token>
   
   Payload: None
   
   Response (with active subscription):
   {
     "success": true,
     "message": "Usage status retrieved successfully",
     "data": {
       "hasActiveSubscription": true,
       "subscription": {
         "id": "uuid",
         "name": "Premium Seller Plan",
         "max_requests": 100,
         "max_offers": 50,
         "max_orders": 25
       },
       "subscriptionDetails": {
         "id": "uuid",
         "status": "ACTIVE",
         "start_date": "2024-01-01T00:00:00.000Z",
         "end_date": "2024-01-31T00:00:00.000Z",
         "auto_renew": true
       },
       "usage": {
         "requests": {
           "canUse": true,
           "limit": 100,
           "used": 25,
           "remaining": 75
         },
         "offers": {
           "canUse": true,
           "limit": 50,
           "used": 10,
           "remaining": 40
         },
         "orders": {
           "canUse": true,
           "limit": 25,
           "used": 5,
           "remaining": 20
         }
       }
     }
   }
   
   Response (no active subscription):
   {
     "success": true,
     "message": "Usage status retrieved successfully",
     "data": {
       "hasActiveSubscription": false,
       "subscription": null,
       "usage": null
     }
   }

=====================================

6. PUT /subscriptions/:subscription_id/cancel
   Description: Cancel user's subscription
   
   Endpoint: PUT /api/seller/subscriptions/{subscription_id}/cancel
   
   Headers:
   - Authorization: Bearer <seller_token>
   
   URL Parameters:
   - subscription_id: UUID (required)
   
   Payload: None
   
   Response:
   {
     "success": true,
     "message": "Subscription cancelled successfully",
     "data": {
       "id": "uuid",
       "status": "CANCELLED",
       "start_date": "2024-01-01T00:00:00.000Z",
       "end_date": "2024-01-31T00:00:00.000Z",
       "cancelled_at": "2024-01-15T10:30:00.000Z"
     }
   }

=====================================

ERROR RESPONSES:

Validation Error (400):
{
  "success": false,
  "message": "Validation failed",
  "error": {
    "subscription_id": ["Valid subscription ID is required"]
  }
}

Unauthorized (401):
{
  "message": "Access Denied"
}

Forbidden (403):
{
  "message": "Access denied. Insufficient permissions."
}

Server Error (500):
{
  "success": false,
  "message": "Internal server error",
  "error": {
    "general": ["Error message"]
  }
}
