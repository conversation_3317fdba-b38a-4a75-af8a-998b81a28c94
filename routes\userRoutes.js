const express = require('express');
const userController = require('../controllers/userController');
const authenticateToken = require('../middlewares/authMiddleware');
const router = express.Router();

router.get('/users', authenticateToken, userController.getUsers);
router.get('/users/search', authenticateToken, userController.searchUsers);
router.get('/users/profile', authenticateToken, userController.getUserProfile);
router.get('/users/:id', authenticateToken, userController.getUserById);
router.post('/users', authenticateToken, userController.createUser);
router.put('/users/:id', authenticateToken, userController.updateUser);
router.patch('/users/:id', authenticateToken, userController.partialUpdateUser);
router.delete('/users/:id', authenticateToken, userController.deleteUser);
router.get('/profile', authenticateToken, userController.getUserProfileWithRoles);

module.exports = router;

