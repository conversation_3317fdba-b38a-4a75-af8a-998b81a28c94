const SubCategoryFormFieldModel = require('../models/subCategoryFormFieldModel');

class SubCategoryFormFieldService {
  /**
   * Create a new form field for a subcategory
   * @param {Object} data - Form field data
   * @returns {Promise<Object>} Created form field
   */
  static async createFormField(data) {
    return await SubCategoryFormFieldModel.createFormField(data);
  }

  /**
   * Create multiple form fields for a subcategory
   * @param {Array} formFields - Array of form field data
   * @param {String} subcategoryId - Subcategory ID
   * @returns {Promise<Array>} Created form fields
   */
  static async createManyFormFields(formFields, subcategoryId) {
    return await SubCategoryFormFieldModel.createManyFormFields(formFields, subcategoryId);
  }

  /**
   * Get all form fields for a subcategory
   * @param {String} subcategoryId - Subcategory ID
   * @returns {Promise<Array>} Form fields
   */
  static async getFormFieldsBySubcategoryId(subcategoryId) {
    return await SubCategoryFormFieldModel.getFormFieldsBySubcategoryId(subcategoryId);
  }

  /**
   * Get a form field by ID
   * @param {String} id - Form field ID
   * @returns {Promise<Object>} Form field
   */
  static async getFormFieldById(id) {
    return await SubCategoryFormFieldModel.getFormFieldById(id);
  }

  /**
   * Update a form field
   * @param {String} id - Form field ID
   * @param {Object} data - Form field data to update
   * @returns {Promise<Object>} Updated form field
   */
  static async updateFormField(id, data) {
    return await SubCategoryFormFieldModel.updateFormField(id, data);
  }

  /**
   * Delete a form field
   * @param {String} id - Form field ID
   * @returns {Promise<Object>} Deleted form field
   */
  static async deleteFormField(id) {
    return await SubCategoryFormFieldModel.deleteFormField(id);
  }

  /**
   * Delete all form fields for a subcategory
   * @param {String} subcategoryId - Subcategory ID
   * @returns {Promise<Object>} Result of deletion
   */
  static async deleteFormFieldsBySubcategoryId(subcategoryId) {
    return await SubCategoryFormFieldModel.deleteFormFieldsBySubcategoryId(subcategoryId);
  }
}

module.exports = SubCategoryFormFieldService;
