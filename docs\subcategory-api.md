# Subcategory API Documentation

## Create Subcategory

Creates a new subcategory with optional dynamic form fields.

### Endpoint

```
POST /api/admin/categories/:id/subcategories
```

### Authentication

- Requires a valid JWT token
- User must have 'Admin' role

### URL Parameters

| Parameter | Type   | Description                                |
|-----------|--------|--------------------------------------------|
| id        | string | ID of the parent category (UUID format)    |

### Request Headers

| Header          | Value                       | Description                   |
|-----------------|----------------------------|-------------------------------|
| Authorization   | Bearer {token}             | JWT authentication token      |
| Content-Type    | multipart/form-data        | For file uploads              |

### Request Body (multipart/form-data)

| Field           | Type    | Required | Description                                                |
|-----------------|---------|----------|------------------------------------------------------------|
| title           | string  | Yes      | Title of the subcategory                                   |
| description     | string  | No       | Description of the subcategory                             |
| color           | string  | No       | Hex color code for the subcategory                         |
| image           | file    | No       | Main image for the subcategory                             |
| thumbnail       | file    | No       | Thumbnail image for the subcategory                        |
| is_featured     | string  | No       | Whether the subcategory is featured ('1' for true, '0' for false) |
| sort_order      | string  | No       | Order in which to display the subcategory                  |
| seo_title       | string  | No       | SEO title for the subcategory                              |
| seo_description | string  | No       | SEO description for the subcategory                        |
| seo_keywords    | string  | No       | SEO keywords for the subcategory                           |
| translations    | string  | No       | JSON string of translations (see format below)             |
| form_fields     | string  | No       | JSON string of form fields (see format below)              |

### Translations Format

```json
[
  {
    "language": "fr",
    "title": "French Title",
    "description": "French Description",
    "seo_title": "French SEO Title",
    "seo_description": "French SEO Description",
    "seo_keywords": "French SEO Keywords"
  },
  {
    "language": "es",
    "title": "Spanish Title",
    "description": "Spanish Description",
    "seo_title": "Spanish SEO Title",
    "seo_description": "Spanish SEO Description",
    "seo_keywords": "Spanish SEO Keywords"
  }
]
```

### Form Fields Format

```json
[
  {
    "label_name": "Project Description",
    "input_type": "TEXTAREA",
    "label_subtitle": "Describe your project in detail",
    "placeholder": "Enter project details here...",
    "is_required": true,
    "sort_order": 1
  },
  {
    "label_name": "Project Type",
    "input_type": "SELECT",
    "label_subtitle": "Select the type of website you need",
    "is_required": true,
    "options": ["E-commerce", "Blog", "Corporate", "Portfolio", "Other"],
    "sort_order": 2
  },
  {
    "label_name": "Budget Range",
    "input_type": "RANGE",
    "label_subtitle": "Select your budget range",
    "is_required": true,
    "min_value": 100,
    "max_value": 10000,
    "default_value": "1000",
    "sort_order": 3
  }
]
```

### Form Field Properties

| Property         | Type    | Required | Description                                                |
|------------------|---------|----------|------------------------------------------------------------|
| label_name       | string  | Yes      | Display name for the field                                 |
| input_type       | string  | Yes      | Type of input field (see supported types below)            |
| label_subtitle   | string  | No       | Subtitle or description for the field                      |
| placeholder      | string  | No       | Placeholder text for the field                             |
| is_required      | boolean | No       | Whether the field is required (default: false)             |
| options          | array   | Yes*     | Array of options for SELECT, RADIO, CHECKBOX fields        |
| default_value    | string  | No       | Default value for the field                                |
| validation_regex | string  | No       | Regex pattern for validation                               |
| min_value        | integer | No       | Minimum value for NUMBER fields                            |
| max_value        | integer | No       | Maximum value for NUMBER fields                            |
| min_length       | integer | No       | Minimum length for TEXT fields                             |
| max_length       | integer | No       | Maximum length for TEXT fields                             |
| sort_order       | integer | No       | Order in which fields should be displayed (default: 0)     |

*Required for SELECT, RADIO, CHECKBOX, and MULTISELECT input types

### Supported Input Types

- `TEXT`: Single line text input
- `TEXTAREA`: Multi-line text input
- `NUMBER`: Numeric input
- `EMAIL`: Email input
- `PASSWORD`: Password input
- `DATE`: Date picker
- `TIME`: Time picker
- `DATETIME`: Date and time picker
- `CHECKBOX`: Multiple selection checkboxes
- `RADIO`: Single selection radio buttons
- `SELECT`: Dropdown select
- `MULTISELECT`: Multiple selection dropdown
- `FILE`: File upload
- `PHONE`: Phone number input
- `URL`: URL input
- `COLOR`: Color picker
- `RANGE`: Range slider

### Success Response

**Code:** 201 Created

```json
{
  "success": true,
  "message": "Subcategory created successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "title": "Web Development",
    "description": "Web development services",
    "category_id": "550e8400-e29b-41d4-a716-446655440001",
    "color": "#4287f5",
    "image": "uploads/subcategories/web-dev.jpg",
    "thumbnail": "uploads/subcategories/web-dev-thumb.jpg",
    "is_featured": true,
    "sort_order": 1,
    "seo_title": "Web Development Services",
    "seo_description": "Professional web development services",
    "seo_keywords": "web development, website, programming",
    "created_at": "2025-05-12T12:00:00.000Z",
    "updated_at": "2025-05-12T12:00:00.000Z",
    "is_deleted": false,
    "translations": [
      {
        "id": "550e8400-e29b-41d4-a716-446655440002",
        "sub_category_id": "550e8400-e29b-41d4-a716-446655440000",
        "language": "fr",
        "title": "Développement Web",
        "description": "Services de développement web"
      }
    ],
    "category": {
      "id": "550e8400-e29b-41d4-a716-446655440001",
      "title": "IT Services"
    },
    "form_fields": [
      {
        "id": "550e8400-e29b-41d4-a716-446655440003",
        "subcategory_id": "550e8400-e29b-41d4-a716-446655440000",
        "label_name": "Project Description",
        "input_type": "TEXTAREA",
        "label_subtitle": "Describe your project in detail",
        "placeholder": "Enter project details here...",
        "is_required": true,
        "options": [],
        "default_value": null,
        "validation_regex": null,
        "min_value": null,
        "max_value": null,
        "min_length": null,
        "max_length": null,
        "sort_order": 1,
        "created_at": "2025-05-12T12:00:00.000Z",
        "updated_at": "2025-05-12T12:00:00.000Z"
      },
      {
        "id": "550e8400-e29b-41d4-a716-446655440004",
        "subcategory_id": "550e8400-e29b-41d4-a716-446655440000",
        "label_name": "Project Type",
        "input_type": "SELECT",
        "label_subtitle": "Select the type of website you need",
        "placeholder": null,
        "is_required": true,
        "options": ["E-commerce", "Blog", "Corporate", "Portfolio", "Other"],
        "default_value": null,
        "validation_regex": null,
        "min_value": null,
        "max_value": null,
        "min_length": null,
        "max_length": null,
        "sort_order": 2,
        "created_at": "2025-05-12T12:00:00.000Z",
        "updated_at": "2025-05-12T12:00:00.000Z"
      }
    ]
  },
  "errors": null,
  "meta": null
}
```

### Error Responses

**Code:** 422 Unprocessable Entity

```json
{
  "success": false,
  "message": "Validation failed",
  "data": null,
  "errors": {
    "title": "The title field is required.",
    "form_fields.0.label_name": "Label name is required",
    "form_fields.1.options": "Options are required for this input type"
  },
  "meta": null
}
```

**Code:** 404 Not Found

```json
{
  "success": false,
  "message": "Category not found",
  "data": null,
  "errors": null,
  "meta": null
}
```

**Code:** 500 Internal Server Error

```json
{
  "success": false,
  "message": "Failed to create subcategory",
  "data": null,
  "errors": "Internal server error details",
  "meta": null
}
```

### Example Request

```bash
curl -X POST \
  https://api.example.com/api/admin/categories/550e8400-e29b-41d4-a716-446655440001/subcategories \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -H 'Content-Type: multipart/form-data' \
  -F 'title=Web Development' \
  -F 'description=Web development services' \
  -F 'is_featured=1' \
  -F 'sort_order=1' \
  -F 'image=@/path/to/image.jpg' \
  -F 'thumbnail=@/path/to/thumbnail.jpg' \
  -F 'translations=[{"language":"fr","title":"Développement Web","description":"Services de développement web"}]' \
  -F 'form_fields=[{"label_name":"Project Description","input_type":"TEXTAREA","label_subtitle":"Describe your project in detail","placeholder":"Enter project details here...","is_required":true,"sort_order":1},{"label_name":"Project Type","input_type":"SELECT","label_subtitle":"Select the type of website you need","is_required":true,"options":["E-commerce","Blog","Corporate","Portfolio","Other"],"sort_order":2}]'
```

### Notes

1. The `form_fields` property allows you to define dynamic form fields that will be displayed to buyers when creating requests after selecting this subcategory.

2. Each form field can have different properties depending on its `input_type`. For example, `SELECT`, `RADIO`, and `CHECKBOX` types require the `options` array to be provided.

3. The `sort_order` property determines the order in which form fields are displayed to the user.

4. Form fields are automatically associated with the subcategory and will be returned when fetching the subcategory details.

5. Images are uploaded to the server and stored in the 'uploads/subcategories' directory.

6. Translations allow you to provide localized versions of the subcategory content for different languages.

7. When retrieving a subcategory, the form fields (children) are automatically included in the response in the `form_fields` array, sorted by their `sort_order` value.

### Retrieving Form Fields

When you retrieve a subcategory using the `GET /api/admin/subcategories/:id` endpoint, the response will include all associated form fields in the `form_fields` array:

```json
{
  "success": true,
  "message": "Subcategory retrieved successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "title": "Web Development",
    "description": "Web development services",
    "category_id": "550e8400-e29b-41d4-a716-446655440001",
    "color": "#4287f5",
    "image": "uploads/subcategories/web-dev.jpg",
    "thumbnail": "uploads/subcategories/web-dev-thumb.jpg",
    "is_featured": true,
    "sort_order": 1,
    "seo_title": "Web Development Services",
    "seo_description": "Professional web development services",
    "seo_keywords": "web development, website, programming",
    "created_at": "2025-05-12T12:00:00.000Z",
    "updated_at": "2025-05-12T12:00:00.000Z",
    "is_deleted": false,
    "translations": [...],
    "category": {...},
    "form_fields": [
      {
        "id": "550e8400-e29b-41d4-a716-446655440003",
        "subcategory_id": "550e8400-e29b-41d4-a716-446655440000",
        "label_name": "Project Description",
        "input_type": "TEXTAREA",
        "label_subtitle": "Describe your project in detail",
        "placeholder": "Enter project details here...",
        "is_required": true,
        "options": [],
        "default_value": null,
        "validation_regex": null,
        "min_value": null,
        "max_value": null,
        "min_length": null,
        "max_length": null,
        "sort_order": 1,
        "created_at": "2025-05-12T12:00:00.000Z",
        "updated_at": "2025-05-12T12:00:00.000Z"
      },
      {
        "id": "550e8400-e29b-41d4-a716-446655440004",
        "subcategory_id": "550e8400-e29b-41d4-a716-446655440000",
        "label_name": "Project Type",
        "input_type": "SELECT",
        "label_subtitle": "Select the type of website you need",
        "placeholder": null,
        "is_required": true,
        "options": ["E-commerce", "Blog", "Corporate", "Portfolio", "Other"],
        "default_value": null,
        "validation_regex": null,
        "min_value": null,
        "max_value": null,
        "min_length": null,
        "max_length": null,
        "sort_order": 2,
        "created_at": "2025-05-12T12:00:00.000Z",
        "updated_at": "2025-05-12T12:00:00.000Z"
      }
    ]
  },
  "errors": null,
  "meta": null
}
```

You can also retrieve just the form fields for a subcategory using the dedicated endpoint:

```
GET /api/admin/subcategories/:id/form-fields
```

This will return only the form fields array:

```json
{
  "success": true,
  "message": "Form fields retrieved successfully",
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440003",
      "subcategory_id": "550e8400-e29b-41d4-a716-446655440000",
      "label_name": "Project Description",
      "input_type": "TEXTAREA",
      "label_subtitle": "Describe your project in detail",
      "placeholder": "Enter project details here...",
      "is_required": true,
      "options": [],
      "sort_order": 1,
      "created_at": "2025-05-12T12:00:00.000Z",
      "updated_at": "2025-05-12T12:00:00.000Z"
    },
    {
      "id": "550e8400-e29b-41d4-a716-446655440004",
      "subcategory_id": "550e8400-e29b-41d4-a716-446655440000",
      "label_name": "Project Type",
      "input_type": "SELECT",
      "label_subtitle": "Select the type of website you need",
      "is_required": true,
      "options": ["E-commerce", "Blog", "Corporate", "Portfolio", "Other"],
      "sort_order": 2,
      "created_at": "2025-05-12T12:00:00.000Z",
      "updated_at": "2025-05-12T12:00:00.000Z"
    }
  ],
  "errors": null,
  "meta": null
}
