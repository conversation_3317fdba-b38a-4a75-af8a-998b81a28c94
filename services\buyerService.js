const BuyerModel = require('../models/buyerModel');
const ApiError = require('../utils/apiError');

class BuyerService {
  /**
   * Get buyer profile
   * @param {string} buyerId - Buyer ID
   * @returns {Promise<Object>} Buyer profile
   */
  static async getBuyerProfile(buyerId) {
    try {
      const profile = await BuyerModel.getBuyerProfile(buyerId);

      if (!profile) {
        throw new ApiError(404, 'Buyer profile not found');
      }

      return profile;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, `Failed to get buyer profile: ${error.message}`);
    }
  }

  /**
   * Update buyer profile
   * @param {string} buyerId - Buyer ID
   * @param {Object} profileData - Profile data to update
   * @returns {Promise<Object>} Updated buyer profile
   */
  static async updateBuyerProfile(buyerId, profileData) {
    try {
      // Check if email is being updated and if it's already in use
      if (profileData.email) {
        const existingUser = await BuyerModel.findUserByEmail(profileData.email);
        if (existingUser && existingUser.id !== buyerId) {
          throw new ApiError(422, 'The email has already been taken');
        }
      }

      // Check if phone number is being updated and if it's already in use
      if (profileData.phone_number) {
        const existingUser = await BuyerModel.findUserByPhone(profileData.phone_number);
        if (existingUser && existingUser.id !== buyerId) {
          throw new ApiError(422, 'The phone number has already been taken');
        }
      }

      // Update the profile
      const updatedProfile = await BuyerModel.updateBuyerProfile(buyerId, profileData);

      return updatedProfile;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, `Failed to update buyer profile: ${error.message}`);
    }
  }

  /**
   * Get buyer requests
   * @param {string} buyerId - Buyer ID
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated buyer requests
   */
  static async getBuyerRequests(buyerId, filters = {}, page = 1, limit = 10) {
    try {
      return await BuyerModel.getBuyerRequests(buyerId, filters, page, limit);
    } catch (error) {
      throw new ApiError(500, `Failed to get buyer requests: ${error.message}`);
    }
  }

  /**
   * Get buyer offers
   * @param {string} buyerId - Buyer ID
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated buyer offers
   */
  static async getBuyerOffers(buyerId, filters = {}, page = 1, limit = 10) {
    try {
      return await BuyerModel.getBuyerOffers(buyerId, filters, page, limit);
    } catch (error) {
      throw new ApiError(500, `Failed to get buyer offers: ${error.message}`);
    }
  }

  /**
   * Get accepted offers for a buyer without seller information
   * @param {string} buyerId - Buyer ID
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated accepted offers
   */
  static async getBuyerAcceptedOffers(buyerId, filters = {}, page = 1, limit = 10) {
    try {
      return await BuyerModel.getBuyerAcceptedOffers(buyerId, filters, page, limit);
    } catch (error) {
      throw new ApiError(500, `Failed to get accepted offers: ${error.message}`);
    }
  }

  /**
   * Get a specific offer by ID for a buyer without seller information
   * @param {string} buyerId - Buyer ID
   * @param {string} offerId - Offer ID
   * @returns {Promise<Object>} Offer details
   */
  static async getBuyerOfferById(buyerId, offerId) {
    try {
      const offer = await BuyerModel.getBuyerOfferById(buyerId, offerId);

      if (!offer) {
        throw new ApiError(404, 'Offer not found or does not belong to your requests');
      }

      return offer;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, `Failed to get offer details: ${error.message}`);
    }
  }
}

module.exports = BuyerService;
