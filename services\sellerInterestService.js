const SellerInterestModel = require('../models/sellerInterestModel');
const ApiError = require('../utils/apiError');

class SellerInterestService {
  /**
   * Get seller interested categories
   * @param {string} sellerId - Seller ID
   * @returns {Promise<Array>} List of interested categories
   */
  static async getInterestedCategories(sellerId) {
    try {
      return await SellerInterestModel.getInterestedCategories(sellerId);
    } catch (error) {
      throw new ApiError(500, `Failed to get interested categories: ${error.message}`);
    }
  }

  /**
   * Get seller interested subcategories
   * @param {string} sellerId - Seller ID
   * @returns {Promise<Array>} List of interested subcategories
   */
  static async getInterestedSubcategories(sellerId) {
    try {
      return await SellerInterestModel.getInterestedSubcategories(sellerId);
    } catch (error) {
      throw new ApiError(500, `Failed to get interested subcategories: ${error.message}`);
    }
  }

  /**
   * Add seller interested category
   * @param {string} sellerId - Seller ID
   * @param {string} categoryId - Category ID
   * @returns {Promise<Object>} Created interest
   */
  static async addInterestedCategory(sellerId, categoryId) {
    try {
      return await SellerInterestModel.addInterestedCategory(sellerId, categoryId);
    } catch (error) {
      if (error.message.includes('not found')) {
        throw new ApiError(404, error.message);
      }
      throw new ApiError(500, `Failed to add interested category: ${error.message}`);
    }
  }

  /**
   * Add seller interested subcategory
   * @param {string} sellerId - Seller ID
   * @param {string} subcategoryId - Subcategory ID
   * @returns {Promise<Object>} Created interest
   */
  static async addInterestedSubcategory(sellerId, subcategoryId) {
    try {
      return await SellerInterestModel.addInterestedSubcategory(sellerId, subcategoryId);
    } catch (error) {
      if (error.message.includes('not found')) {
        throw new ApiError(404, error.message);
      }
      throw new ApiError(500, `Failed to add interested subcategory: ${error.message}`);
    }
  }

  /**
   * Remove seller interested category
   * @param {string} sellerId - Seller ID
   * @param {string} categoryId - Category ID
   * @returns {Promise<boolean>} Success status
   */
  static async removeInterestedCategory(sellerId, categoryId) {
    try {
      return await SellerInterestModel.removeInterestedCategory(sellerId, categoryId);
    } catch (error) {
      throw new ApiError(500, `Failed to remove interested category: ${error.message}`);
    }
  }

  /**
   * Remove seller interested subcategory
   * @param {string} sellerId - Seller ID
   * @param {string} subcategoryId - Subcategory ID
   * @returns {Promise<boolean>} Success status
   */
  static async removeInterestedSubcategory(sellerId, subcategoryId) {
    try {
      return await SellerInterestModel.removeInterestedSubcategory(sellerId, subcategoryId);
    } catch (error) {
      throw new ApiError(500, `Failed to remove interested subcategory: ${error.message}`);
    }
  }

  /**
   * Update seller interested categories
   * @param {string} sellerId - Seller ID
   * @param {Array} categoryIds - Array of category IDs
   * @returns {Promise<Object>} Result with added and removed counts
   */
  static async updateInterestedCategories(sellerId, categoryIds) {
    try {
      // Get current interested categories
      const currentCategories = await SellerInterestModel.getInterestedCategories(sellerId);
      const currentCategoryIds = currentCategories.map(cat => cat.id);

      // Determine categories to add and remove
      const categoriesToAdd = categoryIds.filter(id => !currentCategoryIds.includes(id));
      const categoriesToRemove = currentCategoryIds.filter(id => !categoryIds.includes(id));

      // Add new categories
      for (const categoryId of categoriesToAdd) {
        await SellerInterestModel.addInterestedCategory(sellerId, categoryId);
      }

      // Remove categories
      for (const categoryId of categoriesToRemove) {
        await SellerInterestModel.removeInterestedCategory(sellerId, categoryId);
      }

      return {
        added: categoriesToAdd.length,
        removed: categoriesToRemove.length,
        total: categoryIds.length
      };
    } catch (error) {
      throw new ApiError(500, `Failed to update interested categories: ${error.message}`);
    }
  }

  /**
   * Update seller interested subcategories
   * @param {string} sellerId - Seller ID
   * @param {Array} subcategoryIds - Array of subcategory IDs
   * @returns {Promise<Object>} Result with added and removed counts
   */
  static async updateInterestedSubcategories(sellerId, subcategoryIds) {
    try {
      // Get current interested subcategories
      const currentSubcategories = await SellerInterestModel.getInterestedSubcategories(sellerId);
      const currentSubcategoryIds = currentSubcategories.map(subcat => subcat.id);

      // Determine subcategories to add and remove
      const subcategoriesToAdd = subcategoryIds.filter(id => !currentSubcategoryIds.includes(id));
      const subcategoriesToRemove = currentSubcategoryIds.filter(id => !subcategoryIds.includes(id));

      // Add new subcategories
      for (const subcategoryId of subcategoriesToAdd) {
        await SellerInterestModel.addInterestedSubcategory(sellerId, subcategoryId);
      }

      // Remove subcategories
      for (const subcategoryId of subcategoriesToRemove) {
        await SellerInterestModel.removeInterestedSubcategory(sellerId, subcategoryId);
      }

      return {
        added: subcategoriesToAdd.length,
        removed: subcategoriesToRemove.length,
        total: subcategoryIds.length
      };
    } catch (error) {
      throw new ApiError(500, `Failed to update interested subcategories: ${error.message}`);
    }
  }

  /**
   * Get subcategories for categories that the seller is interested in
   * @param {string} sellerId - Seller ID
   * @returns {Promise<Array>} List of subcategories
   */
  static async getSubcategoriesForInterestedCategories(sellerId) {
    try {
      // First, get the list of category IDs that the seller is interested in
      const interestedCategories = await SellerInterestModel.getInterestedCategories(sellerId);
      const categoryIds = interestedCategories.map(category => category.id);

      // If there are no interested categories, return an empty array
      if (categoryIds.length === 0) {
        return [];
      }

      // Then, get subcategories where category_id is in the list of interested category IDs
      return await SellerInterestModel.getSubcategoriesByCategoryIds(categoryIds);
    } catch (error) {
      throw new ApiError(500, `Failed to get subcategories for interested categories: ${error.message}`);
    }
  }
}

module.exports = SellerInterestService;
