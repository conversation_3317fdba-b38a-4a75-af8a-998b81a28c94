const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const CodeGeneratorService = require('../services/codeGeneratorService');

class RequestModel {
  /**
   * Create a new request
   * @param {Object} requestData - Request data
   * @param {string} buyerId - Buyer ID
   * @returns {Promise<Object>} Created request
   */
  static async createRequest(requestData, buyerId) {
    // Validate required fields
    if (!requestData.title || !requestData.category_id) {
      throw new Error('Title and category are required');
    }

    const request = await prisma.requests.create({
      data: {
        buyer_id: buyerId,
        category_id: requestData.category_id,
        sub_category_id: requestData.sub_category_id,
        title: requestData.title,
        short_description: requestData.short_description,
        description: requestData.description,
        quantity: requestData.quantity || 1,
        budget_min: requestData.budget_min,
        budget_max: requestData.budget_max,
        deadline: requestData.deadline ? new Date(requestData.deadline) : null,
        urgency: requestData.urgency || 'Normal',
        status: 'Pending',
        request_type: requestData.request_type || 'General',
        location: requestData.location,
        additional_info: requestData.additional_info,
        file: requestData.file,
        custom_fields: requestData.custom_fields || null,
        request_attachments: requestData.attachments ? {
          create: requestData.attachments.map(attachment => ({
            file_path: attachment.file_path,
            file_type: attachment.file_type,
            file_size: attachment.file_size,
            description: attachment.description,
            is_public: attachment.is_public || false
          }))
        } : undefined
      },
      include: {
        request_attachments: true,
        category: true,
        sub_category: true
      }
    });

    return request;
  }

  /**
   * Get request by ID
   * @param {string} requestId - Request ID
   * @returns {Promise<Object>} Request details
   */
  static async getRequestById(requestId) {
    // Get the request with all related data except offers
    const request = await prisma.requests.findFirst({
      where: {
        id: requestId,
        is_deleted: false
      },
      include: {
        buyer: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            phone_number: true,
            profile_picture_url: true,
            is_approved: true,
            status: true,
          }
        },
        category: true,
        sub_category: true,
        request_attachments: true,
        assigned_sellers: {
          include: {
            seller: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
                email: true,
                profile_picture_url: true
              }
            },
            assigner: {
              select: {
                id: true,
                first_name: true,
                last_name: true
              }
            }
            // Removed the seller_offers field that doesn't exist in the model
          }
        },
        request_statuses: {
          orderBy: { created_at: 'desc' },
          include: {
            updated_by_user: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
                email: true,
              }
            }
          }
        },

      }
    });

    if (!request) {
      return null;
    }

    // Generate and add the request code
    const requestWithCode = CodeGeneratorService.addCodeToRequest(request);

    // Get the best offer for this request (e.g., the most recent one)
    const bestOffer = await prisma.offers.findFirst({
      where: {
        request_id: requestId,
        is_deleted: false,
      },
      orderBy: {
        created_at: 'desc', // Get the most recent offer
      },
      include: {
        seller: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            profile_picture_url: true
          }
        },
        offer_attachments: true,
        offer_status_changes: {
          orderBy: { created_at: 'desc' },
          take: 1
        },
        offer_negotiations: {
          orderBy: { created_at: 'desc' }
        }
      }
    });

    // Fetch merged requests separately to avoid relationship issues
    const mergedRequests = await prisma.request_merged_items.findMany({
      where: { request_id: requestId },
      include: {
        merged_item: {
          select: {
            id: true,
            request_code: true,
            title: true,
            short_description: true,
            description: true,
            quantity: true,
            budget_min: true,
            budget_max: true,
            deadline: true,
            urgency: true,
            status: true,
            created_at: true,
            buyer: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
                email: true,
                profile_picture_url: true
              }
            },
            category: true,
            sub_category: true
          }
        },
        merged_by_user: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true
          }
        }
      }
    });

    // Add the offer and merged requests to the request as a single object instead of an array
    const result = {
      ...requestWithCode,
      offer: bestOffer ? CodeGeneratorService.addCodeToOffer(bestOffer) : null,
      merged_requests: mergedRequests
    };

    return result;
  }

  /**
   * Update request
   * @param {string} requestId - Request ID
   * @param {Object} updateData - Data to update
   * @param {string} userId - User ID making the update
   * @param {Array} newAttachments - New attachments to add
   * @param {Array} removedImageIds - IDs of attachments to remove
   * @returns {Promise<Object>} Updated request
   */
  static async updateRequest(requestId, updateData, userId, newAttachments = null, removedImageIds = []) {
    const existingRequest = await prisma.requests.findFirst({
      where: {
        id: requestId,
        is_deleted: false
      },
      include: {
        request_attachments: true
      }
    });

    if (!existingRequest) {
      throw new Error('Request not found');
    }

    // Create update payload with only the fields that are provided
    const updatePayload = {};

    // Only include fields that are explicitly provided in the request body
    // Use 'in' operator to check if the property exists in the object
    if ('title' in updateData) updatePayload.title = updateData.title;
    if ('short_description' in updateData) updatePayload.short_description = updateData.short_description;
    if ('description' in updateData) updatePayload.description = updateData.description;

    // Ensure quantity is never null - use default value of 1 if null is provided
    if ('quantity' in updateData) {
      updatePayload.quantity = updateData.quantity === null ? 1 : updateData.quantity;
    }

    if ('budget_min' in updateData) updatePayload.budget_min = updateData.budget_min;
    if ('budget_max' in updateData) updatePayload.budget_max = updateData.budget_max;
    if ('deadline' in updateData) updatePayload.deadline = updateData.deadline ? new Date(updateData.deadline) : null;
    if ('urgency' in updateData) updatePayload.urgency = updateData.urgency;
    if ('request_type' in updateData) updatePayload.request_type = updateData.request_type;
    if ('location' in updateData) updatePayload.location = updateData.location;
    if ('additional_info' in updateData) updatePayload.additional_info = updateData.additional_info;
    if ('file' in updateData) updatePayload.file = updateData.file;
    if ('custom_fields' in updateData) updatePayload.custom_fields = updateData.custom_fields;

    // Only update status if it's explicitly provided and changing
    if ('status' in updateData && updateData.status !== existingRequest.status) {
      updatePayload.status = updateData.status;
      updatePayload.request_statuses = {
        create: {
          status: updateData.status,
          updated_by: userId,
          previous_status: existingRequest.status,
          reason: 'reason' in updateData ? updateData.reason : null
        }
      };
    }

    // Handle new attachments if provided
    if (newAttachments && newAttachments.length > 0) {
      updatePayload.request_attachments = {
        create: newAttachments.map(attachment => ({
          file_path: attachment.file_path,
          file_type: attachment.file_type,
          file_size: attachment.file_size,
          description: attachment.description,
          is_public: attachment.is_public || false
        }))
      };
    }

    // Handle removed images if provided
    if (removedImageIds && removedImageIds.length > 0) {
      // Delete each attachment from the file system and database
      const { deleteFile } = require('../utils/fileDelete');

      for (const attachmentId of removedImageIds) {
        try {
          // Find the attachment to get the file path
          const attachment = await prisma.request_attachments.findUnique({
            where: { id: attachmentId }
          });

          if (attachment) {
            // Delete the file from the server
            deleteFile(attachment.file_path);

            // Delete the attachment from the database
            await prisma.request_attachments.delete({
              where: { id: attachmentId }
            });
          }
        } catch (error) {
          console.error(`Error deleting attachment ${attachmentId}:`, error);
          // Continue with other deletions even if one fails
        }
      }
    }

    // Update the request
    const updatedRequest = await prisma.requests.update({
      where: { id: requestId },
      data: updatePayload,
      include: {
        request_attachments: true,
        request_statuses: {
          orderBy: { created_at: 'desc' },
          take: 1
        },
        buyer: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            profile_picture_url: true
          }
        },
        category: true,
        sub_category: true
      }
    });

    return updatedRequest;
  }

  /**
   * Delete request (soft delete)
   * @param {string} requestId - Request ID
   * @returns {Promise<Object>} Deleted request
   */
  static async deleteRequest(requestId) {
    return await prisma.requests.update({
      where: { id: requestId },
      data: { is_deleted: true }
    });
  }

  /**
   * Admin delete request (hard delete with all related data)
   * @param {string} requestId - Request ID
   * @param {string} adminId - Admin ID making the request
   * @returns {Promise<Object>} Deletion result with summary of deleted items
   */
  static async adminDeleteRequest(requestId, adminId) {
    // Use a transaction to ensure all related data is deleted or none at all
    return await prisma.$transaction(async (tx) => {
      // Get the request with all related data to delete files
      const request = await tx.requests.findUnique({
        where: { id: requestId },
        include: {
          request_attachments: true,
          assigned_sellers: {
            include: {
              seller_offers: {
                include: {
                  offer_attachments: true
                }
              }
            }
          },
          offers: {
            include: {
              offer_attachments: true
            }
          }
        }
      });

      if (!request) {
        throw new Error('Request not found');
      }

      const { deleteFile } = require('../utils/fileDelete');
      const deletionSummary = {
        request_id: requestId,
        deleted_by: adminId,
        deleted_at: new Date(),
        attachments_deleted: 0,
        offers_deleted: 0,
        offer_attachments_deleted: 0,
        assigned_sellers_deleted: 0,
        status_updates_deleted: 0
      };

      // 1. Delete all request attachments and their files
      if (request.request_attachments && request.request_attachments.length > 0) {
        for (const attachment of request.request_attachments) {
          // Delete the file from the server
          deleteFile(attachment.file_path);
          deletionSummary.attachments_deleted++;
        }

        // Delete all attachments from the database
        await tx.request_attachments.deleteMany({
          where: { request_id: requestId }
        });
      }

      // 2. Delete the main request file if it exists
      if (request.file) {
        deleteFile(request.file);
      }

      // 3. Delete all offer attachments and their files
      // First from direct offers
      if (request.offers && request.offers.length > 0) {
        for (const offer of request.offers) {
          if (offer.offer_attachments && offer.offer_attachments.length > 0) {
            for (const attachment of offer.offer_attachments) {
              // Delete the file from the server
              deleteFile(attachment.file_path);
              deletionSummary.offer_attachments_deleted++;
            }
          }
          deletionSummary.offers_deleted++;
        }
      }

      // Then from assigned seller offers
      if (request.assigned_sellers && request.assigned_sellers.length > 0) {
        for (const assignedSeller of request.assigned_sellers) {
          if (assignedSeller.seller_offers && assignedSeller.seller_offers.length > 0) {
            for (const offer of assignedSeller.seller_offers) {
              if (offer.offer_attachments && offer.offer_attachments.length > 0) {
                for (const attachment of offer.offer_attachments) {
                  // Delete the file from the server
                  deleteFile(attachment.file_path);
                  deletionSummary.offer_attachments_deleted++;
                }
              }
              deletionSummary.offers_deleted++;
            }
          }
          deletionSummary.assigned_sellers_deleted++;
        }
      }

      // 4. Delete all offer negotiations
      await tx.offer_negotiations.deleteMany({
        where: {
          offer: {
            request_id: requestId
          }
        }
      });

      // 5. Delete all offer status changes
      await tx.offer_status_changes.deleteMany({
        where: {
          offer: {
            request_id: requestId
          }
        }
      });

      // 6. Delete all offer attachments from the database
      await tx.offer_attachments.deleteMany({
        where: {
          offer: {
            request_id: requestId
          }
        }
      });

      // 7. Delete all offers
      await tx.offers.deleteMany({
        where: { request_id: requestId }
      });

      // 8. Delete all assigned sellers
      await tx.request_assigned_sellers.deleteMany({
        where: { request_id: requestId }
      });

      // 9. Delete all request status updates
      const statusUpdates = await tx.request_statuses.findMany({
        where: { request_id: requestId }
      });

      deletionSummary.status_updates_deleted = statusUpdates.length;

      await tx.request_statuses.deleteMany({
        where: { request_id: requestId }
      });

      // 10. Delete any merged request references
      await tx.request_merged_items.deleteMany({
        where: {
          OR: [
            { request_id: requestId },
            { merged_item_id: requestId }
          ]
        }
      });

      // 11. Finally, delete the request itself
      await tx.requests.delete({
        where: { id: requestId }
      });

      // Create an activity log entry for the deletion
      await tx.activity_logs.create({
        data: {
          user_id: adminId,
          action: 'Request Deleted',
          details: `Admin deleted request: ${request.title} (ID: ${requestId}). Details: ${JSON.stringify(deletionSummary)}`
        }
      });

      return deletionSummary;
    });
  }

  /**
   * Get request status history by request ID
   * @param {string} requestId - Request ID
   * @param {string} buyerId - Buyer ID (for authorization)
   * @returns {Promise<Array>} Status history of the request
   */
  static async getRequestStatusHistory(requestId, buyerId) {
    // First check if the request exists and belongs to the buyer
    const request = await prisma.requests.findFirst({
      where: {
        id: requestId,
        buyer_id: buyerId,
        is_deleted: false
      }
    });

    if (!request) {
      return null;
    }

    // Get the status history
    const statusHistory = await prisma.request_statuses.findMany({
      where: {
        request_id: requestId
      },
      include: {
        updated_by_user: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            profile_picture_url: true,
            roles: {
              include: {
                role: true
              }
            }
          }
        }
      },
      orderBy: {
        created_at: 'desc'
      }
    });

    // Transform the roles for each user
    return statusHistory.map(status => {
      if (status.updated_by_user && status.updated_by_user.roles) {
        status.updated_by_user.roles = status.updated_by_user.roles.map(
          userRole => userRole.role.name
        );
      }
      return status;
    });
  }

  /**
   * Get request status history by request ID for admin
   * @param {string} requestId - Request ID
   * @returns {Promise<Array>} Status history of the request
   */
  static async getRequestStatusHistoryForAdmin(requestId) {
    // Check if the request exists
    const request = await prisma.requests.findFirst({
      where: {
        id: requestId,
        is_deleted: false
      }
    });

    if (!request) {
      return null;
    }

    // Get the status history
    const statusHistory = await prisma.request_statuses.findMany({
      where: {
        request_id: requestId
      },
      include: {
        updated_by_user: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            profile_picture_url: true,
            roles: {
              include: {
                role: true
              }
            }
          }
        }
      },
      orderBy: {
        created_at: 'desc'
      }
    });

    // Transform the roles for each user
    return statusHistory.map(status => {
      if (status.updated_by_user && status.updated_by_user.roles) {
        status.updated_by_user.roles = status.updated_by_user.roles.map(
          userRole => userRole.role.name
        );
      }
      return status;
    });
  }

  /**
   * Get requests by filters
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @param {boolean} isAdminContext - Whether this is called from admin context (default: false)
   * @returns {Promise<Object>} Paginated requests
   */
  static async getRequests(filters = {}, page = 1, limit = 10, isAdminContext = false) {
    const whereClause = { is_deleted: false };

    console.log('Filters:', filters);

    // Apply filters
    if (filters.buyer_id) whereClause.buyer_id = filters.buyer_id;
    if (filters.category_id) whereClause.category_id = filters.category_id;
    if (filters.sub_category_id) whereClause.sub_category_id = filters.sub_category_id;
    if (filters.status) whereClause.status = filters.status;
    if (filters.request_type) whereClause.request_type = filters.request_type;
    if (filters.urgency) whereClause.urgency = { contains: filters.urgency, mode: 'insensitive' };
    if (filters.search) {
      whereClause.OR = [
        { title: { contains: filters.search, mode: 'insensitive' } },
        { short_description: { contains: filters.search, mode: 'insensitive' } },
        { description: { contains: filters.search, mode: 'insensitive' } }
      ];
    }

    // Only apply is_child filter when called from admin context
    // This hides child requests from admin list but shows all requests to buyers
    if (isAdminContext) {
      whereClause.is_child = false;
    }

    const [requests, total] = await Promise.all([
      prisma.requests.findMany({
        where: whereClause,
        skip: (page - 1) * limit,
        take: limit,
        orderBy: { created_at: 'desc' },
        include: {
          buyer: {
            select: {
              id: true,
              first_name: true,
              last_name: true
            }
          },
          category: { select: { id: true, title: true, image: true } },
          sub_category: { select: { id: true, title: true, image: true } },
          assigned_sellers: {
            include: {
              seller: {
                select: {
                  id: true,
                  first_name: true,
                  last_name: true,
                  email: true,
                  profile_picture_url: true
                }
              }
            }
          }
        }
      }),
      prisma.requests.count({ where: whereClause })
    ]);

    // Get the best offer for each request
    const requestsWithOffers = await Promise.all(
      requests.map(async (request) => {
        // Get the best offer for this request
        const bestOffer = await prisma.offers.findFirst({
          where: {
            request_id: request.id,
            is_deleted: false,
          },
          orderBy: {
            created_at: 'desc', // Get the most recent offer
          },
          include: {
            seller: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
                email: true,
                profile_picture_url: true
              }
            },
            offer_status_changes: {
              orderBy: { created_at: 'desc' },
              take: 1
            }
          }
        });

        // Generate and add the request code
        const requestWithCode = CodeGeneratorService.addCodeToRequest(request);

        // Add offer code if there is an offer
        const offerWithCode = bestOffer ? CodeGeneratorService.addCodeToOffer(bestOffer) : null;

        // Add combined name field and best offer
        return {
          ...requestWithCode,
          buyer: {
            ...request.buyer,
            name: `${request.buyer.first_name} ${request.buyer.last_name}`
          },
          offer: offerWithCode
        };
      })
    );

    return {
      data: requestsWithOffers,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Create a new request by merging multiple existing requests
   * @param {Object} newRequestData - New request data
   * @param {string[]} requestIdsToMerge - Request IDs to merge
   * @param {string} adminId - Admin ID who created the merge
   * @returns {Promise<Object>} Newly created request with merged items
   */
  static async createMergedRequest(newRequestData, requestIdsToMerge, adminId) {
    return await prisma.$transaction(async (tx) => {
      // Create the new request
      const newRequest = await tx.requests.create({
        data: {
          buyer_id: adminId, // Admin becomes the buyer for the merged request
          category_id: newRequestData.category_id,
          sub_category_id: newRequestData.sub_category_id,
          title: newRequestData.title,
          short_description: newRequestData.short_description,
          description: newRequestData.description,
          quantity: newRequestData.quantity || 1,
          budget_min: newRequestData.budget_min,
          budget_max: newRequestData.budget_max,
          deadline: newRequestData.deadline ? new Date(newRequestData.deadline) : null,
          urgency: newRequestData.urgency || 'Normal',
          status: 'Merged',
          request_type: newRequestData.request_type || 'General',
          location: newRequestData.location,
          additional_info: newRequestData.additional_info,
          custom_fields: newRequestData.custom_fields || null,
        }
      });

      // Create merge records linking the new request to the old ones
      await tx.request_merged_items.createMany({
        data: requestIdsToMerge.map(requestId => ({
          request_id: newRequest.id,
          merged_item_id: requestId,
          merged_by: adminId
        }))
      });

      // Update status of merged requests to 'Merged' and mark as child requests
      await tx.requests.updateMany({
        where: { id: { in: requestIdsToMerge } },
        data: {
          status: 'Merged',
          is_merged: true,
          is_child: true
        }
      });

      // Mark the new parent request as merged
      await tx.requests.update({
        where: { id: newRequest.id },
        data: { is_merged: true }
      });

      // Add status update records for the merged requests
      for (const requestId of requestIdsToMerge) {
        await tx.request_statuses.create({
          data: {
            request_id: requestId,
            status: 'Merged',
            updated_by: adminId,
            reason: `Merged into new request: ${newRequest.title}`
          }
        });
      }

      // Add status update for the new request
      await tx.request_statuses.create({
        data: {
          request_id: newRequest.id,
          status: 'Merged',
          updated_by: adminId,
          reason: `Created by merging ${requestIdsToMerge.length} requests`
        }
      });

      // Get the new request with all related data
      const newRequestWithData = await tx.requests.findUnique({
        where: { id: newRequest.id },
        include: {
          buyer: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
              phone_number: true,
              profile_picture_url: true,
              is_approved: true,
              status: true,
            }
          },
          category: true,
          sub_category: true,
          request_statuses: {
            orderBy: { created_at: 'desc' },
            include: {
              updated_by_user: {
                select: {
                  id: true,
                  first_name: true,
                  last_name: true,
                  email: true,
                }
              }
            }
          }
        }
      });

      // Fetch merged requests separately
      const mergedRequests = await tx.request_merged_items.findMany({
        where: { request_id: newRequest.id },
        include: {
          merged_item: {
            select: {
              id: true,
              request_code: true,
              title: true,
              short_description: true,
              description: true,
              quantity: true,
              budget_min: true,
              budget_max: true,
              deadline: true,
              urgency: true,
              status: true,
              created_at: true,
              buyer: {
                select: {
                  id: true,
                  first_name: true,
                  last_name: true,
                  email: true,
                  profile_picture_url: true
                }
              },
              category: true,
              sub_category: true
            }
          },
          merged_by_user: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true
            }
          }
        }
      });

      // Return the new request with merged requests
      return {
        ...newRequestWithData,
        merged_requests: mergedRequests
      };
    });
  }

  /**
   * Add attachment to request
   * @param {string} requestId - Request ID
   * @param {Object} attachmentData - Attachment data
   * @returns {Promise<Object>} Created attachment
   */
  static async addAttachment(requestId, attachmentData) {
    return await prisma.request_attachments.create({
      data: {
        request_id: requestId,
        file_path: attachmentData.file_path,
        file_type: attachmentData.file_type,
        file_size: attachmentData.file_size,
        description: attachmentData.description,
        is_public: attachmentData.is_public || false
      }
    });
  }

  /**
   * Delete attachment from request
   * @param {string} attachmentId - Attachment ID
   * @returns {Promise<Object>} Deleted attachment
   */
  static async deleteAttachment(attachmentId) {
    // First get the attachment to get the file path
    const attachment = await prisma.request_attachments.findUnique({
      where: { id: attachmentId }
    });

    if (!attachment) {
      throw new Error('Attachment not found');
    }

    // Delete the file from the server
    const { deleteFile } = require('../utils/fileDelete');
    deleteFile(attachment.file_path);

    // Delete the attachment from the database
    return await prisma.request_attachments.delete({
      where: { id: attachmentId }
    });
  }

  /**
   * Assign sellers to a request
   * @param {string} requestId - Request ID
   * @param {string[]} sellerIds - Array of seller IDs to assign
   * @param {string} assignerId - User ID who is assigning the sellers
   * @param {string} notes - Optional notes about the assignment
   * @returns {Promise<Object>} Request with assigned sellers
   */
  static async assignSellers(requestId, sellerIds, assignerId, notes = null) {
    // Check if request exists
    const request = await prisma.requests.findFirst({
      where: {
        id: requestId,
        is_deleted: false
      }
    });

    if (!request) {
      throw new Error('Request not found');
    }

    // Get existing assigned sellers to avoid duplicates
    const existingAssignments = await prisma.request_assigned_sellers.findMany({
      where: { request_id: requestId },
      select: { seller_id: true }
    });

    const existingSellerIds = existingAssignments.map(assignment => assignment.seller_id);

    // Filter out sellers that are already assigned
    const newSellerIds = sellerIds.filter(id => !existingSellerIds.includes(id));

    if (newSellerIds.length === 0) {
      throw new Error('All specified sellers are already assigned to this request');
    }

    // Create assignments for new sellers
    await prisma.request_assigned_sellers.createMany({
      data: newSellerIds.map(sellerId => ({
        request_id: requestId,
        seller_id: sellerId,
        assigned_by: assignerId,
        notes: notes
      })),
      skipDuplicates: true // Skip if there's a unique constraint violation
    });

    // Update request status if it's still pending
    if (request.status === 'Pending') {
      await prisma.requests.update({
        where: { id: requestId },
        data: {
          status: 'Assigned',
          request_statuses: {
            create: {
              status: 'Assigned',
              updated_by: assignerId,
              previous_status: request.status,
              reason: 'Sellers assigned to request'
            }
          }
        }
      });
    }

    // Get the request with the newly assigned sellers
    const updatedRequest = await prisma.requests.findUnique({
      where: { id: requestId },
      include: {
        assigned_sellers: {
          include: {
            seller: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
                email: true,
                phone_number: true,
                profile_picture_url: true
              }
            }
          }
        },
        request_statuses: {
          orderBy: { created_at: 'desc' },
          take: 1
        }
      }
    });

    // Get the best offer for this request
    const bestOffer = await prisma.offers.findFirst({
      where: {
        request_id: requestId,
        is_deleted: false,
      },
      orderBy: {
        created_at: 'desc', // Get the most recent offer
      },
      include: {
        seller: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            profile_picture_url: true
          }
        },
        offer_attachments: true,
        offer_status_changes: {
          orderBy: { created_at: 'desc' },
          take: 1
        },
        offer_negotiations: {
          orderBy: { created_at: 'desc' }
        }
      }
    });

    // Return the request with the offer as a single object
    return {
      ...updatedRequest,
      offer: bestOffer || null
    };
  }

  /**
   * Approve a request (Admin only)
   * @param {string} requestId - Request ID
   * @param {string} adminId - Admin ID
   * @param {string} note - Approval note
   * @returns {Promise<Object>} Updated request
   */
  static async approveRequest(requestId, adminId, note) {
    const request = await prisma.requests.findFirst({
      where: {
        id: requestId,
        is_deleted: false
      }
    });

    if (!request) {
      throw new Error('Request not found');
    }

    if (request.status === 'Approved') {
      throw new Error('Request is already approved');
    }

    return await prisma.requests.update({
      where: { id: requestId },
      data: {
        status: 'Approved',
        request_statuses: {
          create: {
            status: 'Approved',
            updated_by: adminId,
            previous_status: request.status,
            reason: note || 'Request approved by admin'
          }
        }
      },
      include: {
        buyer: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true
          }
        },
        category: true,
        sub_category: true,
        request_statuses: {
          orderBy: { created_at: 'desc' },
          take: 1
        }
      }
    });
  }

  /**
   * Reject a request (Admin only)
   * @param {string} requestId - Request ID
   * @param {string} adminId - Admin ID
   * @param {string} note - Rejection note
   * @returns {Promise<Object>} Updated request
   */
  static async rejectRequest(requestId, adminId, note) {
    const request = await prisma.requests.findFirst({
      where: {
        id: requestId,
        is_deleted: false
      }
    });

    if (!request) {
      throw new Error('Request not found');
    }

    if (request.status === 'Rejected') {
      throw new Error('Request is already rejected');
    }

    return await prisma.requests.update({
      where: { id: requestId },
      data: {
        status: 'Rejected',
        request_statuses: {
          create: {
            status: 'Rejected',
            updated_by: adminId,
            previous_status: request.status,
            reason: note || 'Request rejected by admin'
          }
        }
      },
      include: {
        buyer: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true
          }
        },
        category: true,
        sub_category: true,
        request_statuses: {
          orderBy: { created_at: 'desc' },
          take: 1
        }
      }
    });
  }
}

module.exports = RequestModel;
