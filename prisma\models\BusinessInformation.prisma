model business_informations {
  id                    String   @id @default(uuid())
  seller_id             String
  company_name          String
  short_description     String?
  long_description      String?
  logo_url              String?
  banner_url            String?
  address               String?
  city                  String?
  state                 String?
  country               String?
  postal_code           String?
  phone_number          String?
  email                 String?
  website_url           String?
  business_type         String?  // e.g., "Retail", "Wholesale", "Service", "Manufacturing"
  business_category     String?  // e.g., "Electronics", "Clothing", "Food"
  established_year      Int?
  employee_count        String?  // e.g., "1-10", "11-50", "51-200", "200+"
  annual_revenue        String?  // e.g., "Under $1M", "$1M-$10M", "$10M+"
  business_license      String?
  tax_id                String?
  social_media_links    Json?    // Store social media links as JSON
  operating_hours       Json?    // Store operating hours as JSON
  services_offered      Json?    // Store list of services as JSON
  certifications        Json?    // Store certifications as JSON
  is_verified           Boolean  @default(false)
  verification_status   String   @default("pending") // "pending", "verified", "rejected"
  verification_date     DateTime?
  is_active             Boolean  @default(true)
  is_deleted            Boolean  @default(false)
  created_at            DateTime @default(now())
  updated_at            DateTime @updatedAt

  // Relations
  seller                users    @relation(fields: [seller_id], references: [id], onDelete: Cascade)

  @@map("business_informations")
}
