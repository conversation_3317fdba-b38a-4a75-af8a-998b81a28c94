const { body } = require('express-validator');

// Validation for adding an item to cart
const addToCartValidation = [
  body('offer_id')
    .notEmpty()
    .withMessage('Offer ID is required')
    .isUUID()
    .withMessage('Invalid offer ID format'),
  
  body('quantity')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Quantity must be a positive integer')
];

// Validation for updating a cart item
const updateCartItemValidation = [
  body('quantity')
    .notEmpty()
    .withMessage('Quantity is required')
    .isInt({ min: 0 })
    .withMessage('Quantity must be a non-negative integer')
];

module.exports = {
  addToCartValidation,
  updateCartItemValidation
};
