const { body } = require('express-validator');
const { validationResult } = require('express-validator');
const sendResponse = require('../../utils/sendResponse');

/**
 * Validation middleware to handle validation errors
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = {};
    errors.array().forEach(error => {
      if (!errorMessages[error.path]) {
        errorMessages[error.path] = [];
      }
      errorMessages[error.path].push(error.msg);
    });

    return sendResponse(
      res,
      false,
      'Validation failed',
      null,
      errorMessages,
      null,
      400
    );
  }
  next();
};

/**
 * Validation for creating merged requests
 */
const createMergedRequestValidation = [
  // New request data validation
  body('title')
    .notEmpty()
    .withMessage('Title is required')
    .isLength({ min: 3, max: 200 })
    .withMessage('Title must be between 3 and 200 characters'),

  body('short_description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Short description must not exceed 500 characters'),

  body('description')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('Description must not exceed 2000 characters'),

  body('category_id')
    .notEmpty()
    .withMessage('Category ID is required')
    .isUUID()
    .withMessage('Category ID must be a valid UUID'),

  body('sub_category_id')
    .notEmpty()
    .withMessage('Sub-category ID is required')
    .isUUID()
    .withMessage('Sub-category ID must be a valid UUID'),

  body('quantity')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Quantity must be a positive integer'),

  body('budget_min')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Minimum budget must be a positive number'),

  body('budget_max')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Maximum budget must be a positive number'),

  body('deadline')
    .optional()
    .isISO8601()
    .withMessage('Deadline must be a valid date'),

  body('urgency')
    .optional()
    .isIn(['Low', 'Normal', 'High', 'Urgent'])
    .withMessage('Urgency must be Low, Normal, High, or Urgent'),

  body('request_type')
    .optional()
    .isIn(['General', 'Service', 'Product', 'Consultation'])
    .withMessage('Request type must be General, Service, Product, or Consultation'),

  body('location')
    .optional()
    .isLength({ max: 200 })
    .withMessage('Location must not exceed 200 characters'),

  body('additional_info')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Additional info must not exceed 1000 characters'),

  // Request IDs to merge validation
  body('request_ids_to_merge')
    .isArray({ min: 1 })
    .withMessage('Request IDs to merge must be a non-empty array'),

  body('request_ids_to_merge.*')
    .isUUID()
    .withMessage('Each request ID must be a valid UUID'),

  // Custom validation to ensure no duplicate IDs in request_ids_to_merge
  body('request_ids_to_merge')
    .custom((value) => {
      const uniqueIds = [...new Set(value)];
      if (uniqueIds.length !== value.length) {
        throw new Error('Duplicate request IDs are not allowed');
      }
      return true;
    }),

  // Custom validation for budget range
  body('budget_max')
    .custom((value, { req }) => {
      if (value && req.body.budget_min && parseFloat(value) < parseFloat(req.body.budget_min)) {
        throw new Error('Maximum budget must be greater than or equal to minimum budget');
      }
      return true;
    }),

  handleValidationErrors
];

module.exports = {
  createMergedRequestValidation
};
