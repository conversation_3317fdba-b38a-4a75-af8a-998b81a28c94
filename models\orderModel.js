const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

class OrderModel {
  /**
   * Create a new order from cart
   * @param {string} buyerId - Buyer ID
   * @param {string} cartId - Cart ID
   * @param {Object} billingInfo - Billing information
   * @param {Object} cardInfo - Card information (will be tokenized/processed by payment gateway)
   * @returns {Promise<Object>} Created order with items
   */
  static async createOrderFromCart(buyerId, cartId, billingInfo, cardInfo) {
    try {
      console.log('Creating order with buyerId:', buyerId, 'cartId:', cartId);
      // Start a transaction
      return await prisma.$transaction(async (prisma) => {
        // 1. Get the cart with items
        console.log('Query 1: Getting cart with items');
        let cartWithItems;
        try {
          cartWithItems = await prisma.$queryRaw`
            SELECT c.id as cart_id, c.buyer_id,
                   ci.id as cart_item_id, ci.offer_id, ci.quantity, ci.price,
                   o.seller_id, o.request_id
            FROM carts c
            JOIN cart_items ci ON c.id = ci.cart_id
            JOIN offers o ON ci.offer_id = o.id
            WHERE c.id = ${cartId}::uuid
            AND c.buyer_id = ${buyerId}
          `;
          console.log('Query 1 successful, found items:', cartWithItems.length);
        } catch (error) {
          console.error('Query 1 failed:', error.message);
          throw error;
        }

        if (cartWithItems.length === 0) {
          throw new Error('Cart not found or is empty');
        }

        // Calculate total amount
        const totalAmount = cartWithItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);

        // Verify the amount matches what was sent
        if (Math.abs(totalAmount - parseFloat(billingInfo.amount)) > 0.01) {
          throw new Error('Order amount does not match cart total');
        }

        // 2. Create the order (using the first item's seller as the main seller)
        const firstItem = cartWithItems[0];
        console.log('Query 2: Creating order with firstItem:', {
          offer_id: firstItem.offer_id,
          buyer_id: buyerId,
          seller_id: firstItem.seller_id
        });

        let order;
        try {
          // Use Prisma's create method instead of raw SQL
          order = await prisma.orders.create({
            data: {
              offer_id: firstItem.offer_id,
              buyer_id: buyerId,
              seller_id: firstItem.seller_id,
              total_amount: totalAmount,
              payment_status: 'pending',
              order_status: 'pending',
              payment_method: 'credit_card'
            }
          });
          console.log('Query 2 successful, order created:', order.id);
        } catch (error) {
          console.error('Query 2 failed:', error.message);
          throw error;
        }

        // 3. Create order items for each cart item
        console.log('Query 3: Creating order items');
        for (const item of cartWithItems) {
          try {
            // Get the request title
            const request = await prisma.requests.findUnique({
              where: { id: item.request_id },
              select: { title: true }
            });

            if (!request) {
              console.error('Request not found for ID:', item.request_id);
              continue;
            }

            // Create order item
            await prisma.order_items.create({
              data: {
                order_id: order.id,
                title: request.title,
                quantity: item.quantity,
                price_per_unit: item.price,
                total_price: item.price * item.quantity
              }
            });
            console.log('Created order item for request:', request.title);
          } catch (error) {
            console.error('Failed to create order item:', error.message);
            throw error;
          }
        }

        // 4. Create order payment record
        console.log('Query 4: Creating order payment record');
        try {
          await prisma.order_payments.create({
            data: {
              order_id: order.id,
              buyer_id: buyerId,
              amount: totalAmount,
              payment_method: 'credit_card',
              payment_status: 'pending'
            }
          });
          console.log('Created order payment record');
        } catch (error) {
          console.error('Failed to create order payment record:', error.message);
          throw error;
        }

        // 5. Create initial order status record
        console.log('Query 5: Creating order status record');
        try {
          await prisma.order_status_changes.create({
            data: {
              order_id: order.id,
              updated_by: buyerId,
              status: 'pending',
              previous_status: null
            }
          });
          console.log('Created order status record');
        } catch (error) {
          console.error('Failed to create order status record:', error.message);
          throw error;
        }

        // 6. Clear the cart after successful order creation
        console.log('Query 6: Clearing cart items');
        try {
          await prisma.cart_items.deleteMany({
            where: {
              cart_id: cartId
            }
          });
          console.log('Cleared cart items');
        } catch (error) {
          console.error('Failed to clear cart items:', error.message);
          throw error;
        }

        // 7. Get the complete order with items
        console.log('Query 7: Getting complete order with items');
        try {
          // Get the order with items
          const orderWithItems = await prisma.orders.findUnique({
            where: { id: order.id },
            include: {
              order_items: true
            }
          });

          console.log('Retrieved order with items');

          return {
            ...orderWithItems,
            items: orderWithItems.order_items,
            billing_info: {
              full_name: billingInfo.full_name,
              email: billingInfo.email,
              address: billingInfo.address,
              city: billingInfo.city,
              state: billingInfo.state
            }
          };
        } catch (error) {
          console.error('Failed to get complete order:', error.message);
          throw error;
        }
      });
    } catch (error) {
      console.error('Error in createOrderFromCart:', error);
      throw error;
    }
  }

  /**
   * Get orders for a buyer
   * @param {string} buyerId - Buyer ID
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated orders
   */
  static async getBuyerOrders(buyerId, filters = {}, page = 1, limit = 10) {
    try {
      console.log('Getting buyer orders for buyerId:', buyerId);
      const offset = (page - 1) * limit;

      // Build the where condition for Prisma
      const where = {
        buyer_id: buyerId,
        is_deleted: false
      };

      if (filters.status) {
        where.order_status = filters.status;
      }

      if (filters.payment_status) {
        where.payment_status = filters.payment_status;
      }

      // Get total count
      const total = await prisma.orders.count({ where });

      // Get orders with pagination
      const orders = await prisma.orders.findMany({
        where,
        include: {
          order_items: true
        },
        orderBy: {
          created_at: 'desc'
        },
        skip: offset,
        take: limit
      });

      // Format the response
      const formattedOrders = orders.map(order => ({
        ...order,
        items: order.order_items
      }));

      return {
        data: formattedOrders,
        meta: {
          total,
          page,
          limit,
          last_page: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      console.error('Error in getBuyerOrders:', error);
      throw error;
    }
  }
  /**
   * Get all orders for admin with pagination and filtering
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Paginated orders
   */
  static async getAllOrders(filters = {}, page = 1, limit = 10) {
    try {
      console.log('Getting all orders for admin');
      const offset = (page - 1) * limit;

      // Build the where condition for Prisma
      const where = {
        is_deleted: false
      };

      if (filters.status) {
        where.order_status = filters.status;
      }

      if (filters.payment_status) {
        where.payment_status = filters.payment_status;
      }

      if (filters.buyer_id) {
        where.buyer_id = filters.buyer_id;
      }

      if (filters.seller_id) {
        where.seller_id = filters.seller_id;
      }

      // Get total count
      const total = await prisma.orders.count({ where });

      // Get orders with pagination
      const orders = await prisma.orders.findMany({
        where,
        include: {
          order_items: true,
          buyer: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
              profile_picture_url: true
            }
          },
          seller: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
              profile_picture_url: true
            }
          }
        },
        orderBy: {
          created_at: 'desc'
        },
        skip: offset,
        take: limit
      });

      // Format the response
      const formattedOrders = orders.map(order => ({
        ...order,
        items: order.order_items
      }));

      return {
        data: formattedOrders,
        meta: {
          total,
          page,
          limit,
          last_page: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      console.error('Error in getAllOrders:', error);
      throw error;
    }
  }

  /**
   * Get order details by ID
   * @param {string} orderId - Order ID
   * @returns {Promise<Object>} Order details
   */
  static async getOrderById(orderId) {
    try {
      console.log('Getting order details for orderId:', orderId);

      const order = await prisma.orders.findUnique({
        where: { id: orderId },
        include: {
          order_items: true,
          order_payments: true,
          order_status_changes: {
            include: {
              updated_by_user: {
                select: {
                  id: true,
                  first_name: true,
                  last_name: true,
                  email: true
                }
              }
            },
            orderBy: {
              created_at: 'desc'
            }
          },
          buyer: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
              phone_number: true,
              address: true,
              profile_picture_url: true
            }
          },
          seller: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
              phone_number: true,
              profile_picture_url: true
            }
          },
          offer: {
            select: {
              id: true,
              price: true,
              delivery_time: true,
              description: true,
              request: {
                select: {
                  id: true,
                  title: true,
                  short_description: true
                }
              }
            }
          }
        }
      });

      if (!order) {
        throw new Error('Order not found');
      }

      return order;
    } catch (error) {
      console.error('Error in getOrderById:', error);
      throw error;
    }
  }
}

module.exports = OrderModel;
