const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const DATABASE_URL = `postgresql://${process.env.DB_USERNAME}:${process.env.DB_PASSWORD}@${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_DATABASE}`;

const prisma = new PrismaClient({
    datasources: {
        db: {
            url: DATABASE_URL
        }
    }
});

const connectDB = async () => {
    try {
        await prisma.$connect();
        console.log('PostgreSQL connected');
    } catch (error) {
        console.error('Database connection error:', error);
        process.exit(1);
    }
};

module.exports = { prisma, connectDB };
