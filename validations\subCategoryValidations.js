const { body, param, query } = require('express-validator');

const createSubCategoryValidation = [
  body('title')
    .notEmpty().withMessage('Title is required')
    .isString().withMessage('Title must be a string')
    .trim()
    .isLength({ max: 100 }).withMessage('Title cannot exceed 100 characters'),
  
  body('category_id')
    .notEmpty().withMessage('Category ID is required')
    .isString().withMessage('Category ID must be a string')
    .isLength({ min: 1, max: 50 }).withMessage('Invalid category ID length'),
  
  body('description')
    .optional()
    .isString().withMessage('Description must be a string')
    .trim()
    .isLength({ max: 500 }).withMessage('Description cannot exceed 500 characters'),
  
  body('color')
    .optional()
    .isHexColor().withMessage('Invalid hex color code'),
  
  body('image')
    .optional()
    .custom((value, { req }) => {
      if (req.fileValidationError) {
        throw new Error(req.fileValidationError);
      }
      return true;
    }),
  
  body('thumbnail')
    .optional()
    .custom((value, { req }) => {
      if (req.fileValidationError) {
        throw new Error(req.fileValidationError);
      }
      return true;
    }),
  
  body('is_featured')
    .optional()
    .isBoolean().withMessage('is_featured must be a boolean'),
  
  body('sort_order')
    .optional()
    .isInt().withMessage('sort_order must be an integer'),
  
  body('seo_title')
    .optional()
    .isString().withMessage('SEO title must be a string')
    .trim()
    .isLength({ max: 100 }).withMessage('SEO title cannot exceed 100 characters'),
  
  body('seo_description')
    .optional()
    .isString().withMessage('SEO description must be a string')
    .trim()
    .isLength({ max: 300 }).withMessage('SEO description cannot exceed 300 characters'),
  
  body('seo_keywords')
    .optional()
    .isString().withMessage('SEO keywords must be a string')
    .trim()
    .isLength({ max: 200 }).withMessage('SEO keywords cannot exceed 200 characters'),
  
  body('translations')
    .optional()
    .isArray().withMessage('Translations must be an array'),
  
  body('translations.*.language')
    .if(body('translations').exists())
    .notEmpty().withMessage('Language code is required')
    .isLength({ min: 2, max: 5 }).withMessage('Language code must be 2-5 characters'),
  
  body('translations.*.title')
    .if(body('translations').exists())
    .notEmpty().withMessage('Translation title is required')
    .isString().withMessage('Translation title must be a string'),
  
  body('translations.*.description')
    .optional()
    .isString().withMessage('Translation description must be a string'),
  
  body('translations.*.seo_title')
    .optional()
    .isString().withMessage('Translation SEO title must be a string'),
  
  body('translations.*.seo_description')
    .optional()
    .isString().withMessage('Translation SEO description must be a string'),
  
  body('translations.*.seo_keywords')
    .optional()
    .isString().withMessage('Translation SEO keywords must be a string')
];

const subCategoryValidations = {
  // Create sub-category validation
  createSubCategory: createSubCategoryValidation,

  // Update sub-category validation
  updateSubCategory: [
    param('id')
      .isUUID().withMessage('Invalid sub-category ID'),
    
    body('title')
      .optional()
      .isString().withMessage('Title must be a string')
      .trim()
      .isLength({ max: 100 }).withMessage('Title cannot exceed 100 characters'),
    
    body('category_id')
      .optional()
      .isString().withMessage('Category ID must be a string')
      .isLength({ min: 1, max: 50 }).withMessage('Invalid category ID length'),
    
    // Include all other fields from create but make them optional
    ...createSubCategoryValidation
      .filter(validation => {
        // Skip the title and category_id validations since we already have them above
        const isTitleValidation = validation._validator === 'notEmpty' && 
                                validation._fields.includes('title');
        const isCategoryIdValidation = validation._validator === 'notEmpty' && 
                                     validation._fields.includes('category_id');
        return !isTitleValidation && !isCategoryIdValidation;
      })
      .map(validation => validation.optional())
  ],

  // ID parameter validation
  idParam: [
    param('id')
      .isUUID().withMessage('Invalid sub-category ID')
  ],

  // Get by category validation
  getByCategory: [
    param('category_id')
      .isString().withMessage('Category ID must be a string')
      .isLength({ min: 1, max: 50 }).withMessage('Invalid category ID length')
  ],

  // Search validation
  searchSubCategories: [
    query('name')
      .notEmpty().withMessage('Search query is required')
      .isString().withMessage('Search query must be a string')
      .trim()
      .isLength({ min: 2 }).withMessage('Search query must be at least 2 characters')
  ],

  // Toggle status validation
  toggleStatus: [
    param('id')
      .isUUID().withMessage('Invalid sub-category ID'),
    body('is_deleted')
      .optional()
      .isBoolean().withMessage('is_deleted must be a boolean'),
    body('is_featured')
      .optional()
      .isBoolean().withMessage('is_featured must be a boolean')
  ]
};

module.exports = subCategoryValidations;