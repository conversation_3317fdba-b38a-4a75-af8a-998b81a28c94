const { body, param } = require('express-validator');

const formFieldValidations = {
  // Create form field validation
  createFormField: [
    body('label_name')
      .notEmpty().withMessage('Label name is required')
      .isString().withMessage('Label name must be a string')
      .isLength({ max: 255 }).withMessage('Label name must be less than 255 characters'),
    
    body('input_type')
      .notEmpty().withMessage('Input type is required')
      .isIn([
        'TEXT', 'TEXTAREA', 'NUMBER', 'EMAIL', 'PASSWORD', 'DATE', 'TIME', 
        'DATETIME', 'CHECKBOX', 'RADIO', 'SELECT', 'MULTISELECT', 'FILE', 
        'PHONE', 'URL', 'COLOR', 'RANGE'
      ]).withMessage('Invalid input type'),
    
    body('label_subtitle')
      .optional()
      .isString().withMessage('Label subtitle must be a string')
      .isLength({ max: 500 }).withMessage('Label subtitle must be less than 500 characters'),
    
    body('placeholder')
      .optional()
      .isString().withMessage('Placeholder must be a string')
      .isLength({ max: 255 }).withMessage('Placeholder must be less than 255 characters'),
    
    body('is_required')
      .optional()
      .isBoolean().withMessage('Is required must be a boolean')
      .toBoolean(),
    
    body('options')
      .optional()
      .isArray().withMessage('Options must be an array')
      .custom((value, { req }) => {
        const inputType = req.body.input_type;
        if (['SELECT', 'MULTISELECT', 'RADIO', 'CHECKBOX'].includes(inputType) && (!value || value.length === 0)) {
          throw new Error('Options are required for this input type');
        }
        return true;
      }),
    
    body('options.*')
      .optional()
      .isString().withMessage('Each option must be a string'),
    
    body('default_value')
      .optional()
      .isString().withMessage('Default value must be a string'),
    
    body('validation_regex')
      .optional()
      .isString().withMessage('Validation regex must be a string')
      .custom(value => {
        try {
          if (value) {
            new RegExp(value);
          }
          return true;
        } catch (e) {
          throw new Error('Invalid regex pattern');
        }
      }),
    
    body('min_value')
      .optional()
      .isInt().withMessage('Min value must be an integer')
      .toInt(),
    
    body('max_value')
      .optional()
      .isInt().withMessage('Max value must be an integer')
      .toInt()
      .custom((value, { req }) => {
        if (req.body.min_value !== undefined && value < req.body.min_value) {
          throw new Error('Max value must be greater than or equal to min value');
        }
        return true;
      }),
    
    body('min_length')
      .optional()
      .isInt({ min: 0 }).withMessage('Min length must be a non-negative integer')
      .toInt(),
    
    body('max_length')
      .optional()
      .isInt({ min: 1 }).withMessage('Max length must be a positive integer')
      .toInt()
      .custom((value, { req }) => {
        if (req.body.min_length !== undefined && value < req.body.min_length) {
          throw new Error('Max length must be greater than or equal to min length');
        }
        return true;
      }),
    
    body('sort_order')
      .optional()
      .isInt({ min: 0 }).withMessage('Sort order must be a non-negative integer')
      .toInt()
  ],

  // Create multiple form fields validation
  createManyFormFields: [
    body('form_fields')
      .isArray().withMessage('Form fields must be an array')
      .notEmpty().withMessage('Form fields array cannot be empty'),
    
    body('form_fields.*.label_name')
      .notEmpty().withMessage('Label name is required')
      .isString().withMessage('Label name must be a string')
      .isLength({ max: 255 }).withMessage('Label name must be less than 255 characters'),
    
    body('form_fields.*.input_type')
      .notEmpty().withMessage('Input type is required')
      .isIn([
        'TEXT', 'TEXTAREA', 'NUMBER', 'EMAIL', 'PASSWORD', 'DATE', 'TIME', 
        'DATETIME', 'CHECKBOX', 'RADIO', 'SELECT', 'MULTISELECT', 'FILE', 
        'PHONE', 'URL', 'COLOR', 'RANGE'
      ]).withMessage('Invalid input type'),
    
    // Other validations for each field in the array follow the same pattern as above
    // but with 'form_fields.*.' prefix
  ],

  // Update form field validation
  updateFormField: [
    // Same as createFormField but all fields are optional
    body('label_name')
      .optional()
      .isString().withMessage('Label name must be a string')
      .isLength({ max: 255 }).withMessage('Label name must be less than 255 characters'),
    
    body('input_type')
      .optional()
      .isIn([
        'TEXT', 'TEXTAREA', 'NUMBER', 'EMAIL', 'PASSWORD', 'DATE', 'TIME', 
        'DATETIME', 'CHECKBOX', 'RADIO', 'SELECT', 'MULTISELECT', 'FILE', 
        'PHONE', 'URL', 'COLOR', 'RANGE'
      ]).withMessage('Invalid input type'),
    
    // Other validations follow the same pattern as createFormField but all are optional
  ],

  // ID parameter validation
  idParam: [
    param('id')
      .isUUID().withMessage('Invalid form field ID')
  ],

  // Subcategory ID parameter validation
  subcategoryIdParam: [
    param('subcategoryId')
      .isUUID().withMessage('Invalid subcategory ID')
  ]
};

module.exports = formFieldValidations;
