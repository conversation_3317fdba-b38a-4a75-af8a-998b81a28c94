# Subcategory Form Fields

This document explains how to use the dynamic form fields feature for subcategories.

## Overview

Subcategories can now have dynamic form fields associated with them. These form fields will be used to generate forms for buyers when they create requests after selecting a category and subcategory.

## Database Structure

The feature adds a new table `subcategory_form_fields` with the following structure:

- `id`: Unique identifier for the form field
- `subcategory_id`: Reference to the subcategory
- `label_name`: Display name for the field
- `input_type`: Type of input field (TEXT, TEXTAREA, NUMBER, etc.)
- `label_subtitle`: Optional subtitle or description for the field
- `placeholder`: Optional placeholder text
- `is_required`: Whether the field is required
- `options`: Array of options for SELECT, RADIO, CHECKBOX fields
- `default_value`: Default value for the field
- `validation_regex`: Optional regex pattern for validation
- `min_value`: Minimum value for NUMBER fields
- `max_value`: Maximum value for NUMBER fields
- `min_length`: Minimum length for TEXT fields
- `max_length`: Maximum length for TEXT fields
- `sort_order`: Order in which fields should be displayed

## API Endpoints

### Subcategory Endpoints

- `GET /api/admin/subcategories/:id` - Get a subcategory with its form fields
- `POST /api/admin/categories/:id/subcategories` - Create a subcategory with form fields
- `PUT /api/admin/subcategories/:id` - Update a subcategory and its form fields
- `DELETE /api/admin/subcategories/:id` - Delete a subcategory and its form fields
- `GET /api/admin/subcategories/:id/form-fields` - Get form fields for a subcategory

### Form Field Endpoints

- `POST /api/admin/subcategories/:subcategoryId/form-fields` - Create a single form field
- `POST /api/admin/subcategories/:subcategoryId/form-fields/batch` - Create multiple form fields
- `GET /api/admin/form-fields/:id` - Get a form field by ID
- `PUT /api/admin/form-fields/:id` - Update a form field
- `DELETE /api/admin/form-fields/:id` - Delete a form field
- `DELETE /api/admin/subcategories/:subcategoryId/form-fields` - Delete all form fields for a subcategory

## Usage Examples

### Creating a Subcategory with Form Fields

```json
POST /api/admin/categories/:id/subcategories
{
  "title": "Web Development",
  "description": "Web development services",
  "is_featured": "1",
  "form_fields": [
    {
      "label_name": "Project Description",
      "input_type": "TEXTAREA",
      "label_subtitle": "Describe your project in detail",
      "placeholder": "Enter project details here...",
      "is_required": true,
      "sort_order": 1
    },
    {
      "label_name": "Project Type",
      "input_type": "SELECT",
      "label_subtitle": "Select the type of website you need",
      "is_required": true,
      "options": ["E-commerce", "Blog", "Corporate", "Portfolio", "Other"],
      "sort_order": 2
    },
    {
      "label_name": "Budget Range",
      "input_type": "RANGE",
      "label_subtitle": "Select your budget range",
      "is_required": true,
      "min_value": 100,
      "max_value": 10000,
      "default_value": "1000",
      "sort_order": 3
    }
  ]
}
```

### Updating a Subcategory's Form Fields

```json
PUT /api/admin/subcategories/:id
{
  "title": "Web Development",
  "form_fields": [
    {
      "label_name": "Project Description",
      "input_type": "TEXTAREA",
      "label_subtitle": "Describe your project in detail",
      "placeholder": "Enter project details here...",
      "is_required": true,
      "sort_order": 1
    },
    {
      "label_name": "Project Timeline",
      "input_type": "NUMBER",
      "label_subtitle": "Estimated project duration in weeks",
      "placeholder": "Enter number of weeks",
      "is_required": true,
      "min_value": 1,
      "max_value": 52,
      "sort_order": 2
    }
  ]
}
```

### Adding Form Fields to an Existing Subcategory

```json
POST /api/admin/subcategories/:subcategoryId/form-fields/batch
{
  "form_fields": [
    {
      "label_name": "Preferred Technology",
      "input_type": "RADIO",
      "label_subtitle": "Select your preferred technology stack",
      "is_required": true,
      "options": ["MERN", "MEAN", "PHP/Laravel", "Python/Django", "Ruby on Rails"],
      "sort_order": 4
    },
    {
      "label_name": "Additional Features",
      "input_type": "CHECKBOX",
      "label_subtitle": "Select additional features you need",
      "is_required": false,
      "options": ["User Authentication", "Payment Gateway", "Admin Dashboard", "API Integration", "Mobile Responsive"],
      "sort_order": 5
    }
  ]
}
```

## Form Field Types

The following input types are supported:

- `TEXT`: Single line text input
- `TEXTAREA`: Multi-line text input
- `NUMBER`: Numeric input
- `EMAIL`: Email input
- `PASSWORD`: Password input
- `DATE`: Date picker
- `TIME`: Time picker
- `DATETIME`: Date and time picker
- `CHECKBOX`: Multiple selection checkboxes
- `RADIO`: Single selection radio buttons
- `SELECT`: Dropdown select
- `MULTISELECT`: Multiple selection dropdown
- `FILE`: File upload
- `PHONE`: Phone number input
- `URL`: URL input
- `COLOR`: Color picker
- `RANGE`: Range slider

## Implementation Notes

- When a subcategory is deleted, all its form fields are automatically deleted (cascade delete).
- Form fields are ordered by their `sort_order` value when retrieved.
- For input types that require options (SELECT, RADIO, CHECKBOX, MULTISELECT), the `options` array must not be empty.
